import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const Host = `${BASIC.HMES_BASIC}`
const tenantId = getCurrentOrganizationId();
// const Host = '/kd-mes-37695'


export function QueryTail() {
  return {
    url: `${Host}/v1/${tenantId}/hme-graphite-platform/tail/material/query`,
    method: 'GET',
  };
}
// 查询条码
export function QueryMaterialLot() {
  return {
    url: `${Host}/v1/${tenantId}/hme-graphite-platform/tail/materialLot/info/get`,
    method: 'GET',
  };
}

export function CompleteManual() {
  return {
    url: `${Host}/v1/${tenantId}/hme-graphite-platform/tail/complete`,
    method: 'POST',
  };
}

export function CompleteCancel() {
  return {
    url: `${Host}/v1/${tenantId}/hme-graphite-platform/tail/complete/cancel`,
    method: 'POST',
  };
}
