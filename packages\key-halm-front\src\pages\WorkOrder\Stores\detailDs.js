/**
 * 工作单-明细页Ds
 * @since 2020-09-23
 * <AUTHOR>
 * @copyright Copyright (c) 2020, Hand
 */
import intl from 'utils/intl';
import { HALM_MTC } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const organizationId = getCurrentOrganizationId();

const modelPrompt = 'amtc.workOrder.model.workOrder';
// 查询列表数据
const queryListUrl = `${HALM_MTC}/v1/${organizationId}/work-orders`;
// 查询验收记录列表
const queryCheckRecord = `${HALM_MTC}/v1/${organizationId}/check-records`;

// 明细页基本数据
function basicDataDs() {
  return {
    autoQuery: false,
    primaryKey: 'woId',
    fields: [
      {
        name: 'currentWoType', // 当前工单类型 该字段仅用于动态控制位置等字段的必输与disabled
        type: 'object',
        ignore: 'always',
      },
      {
        name: 'woStatus',
        type: 'string',
        defaultValue: 'DRAFT',
      },
      {
        name: 'woNum',
        label: intl.get(`${modelPrompt}.woNum`).d('工单编号'),
        type: 'string',
      },
      {
        name: 'maintSiteLov',
        label: intl.get(`${modelPrompt}.maintSite`).d('服务区域'),
        type: 'object',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
        lovPara: {
          tenantId: organizationId,
        },
        ignore: 'always',
        required: true,
      },
      {
        name: 'maintSiteName',
        label: intl.get(`${modelPrompt}.maintSite`).d('服务区域'),
        type: 'string',
        bind: 'maintSiteLov.maintSiteName',
      },
      {
        name: 'maintSiteId',
        type: 'number',
        bind: 'maintSiteLov.maintSiteId',
      },
      {
        name: 'woTypeLov',
        label: intl.get(`${modelPrompt}.woType`).d('工单类型'),
        type: 'object',
        lovCode: 'AMTC.WORKORDERTYPES',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              organizationId,
              manualcreateEnableFlag: 1,
              woBasicType: record.get('woBasicTypeOfAct'),
              projectRelatedFlag: record.getState('fromPage') === 'WBS' ? 1 : null,
            };
          },
        },
        ignore: 'always',
        required: true,
      },
      {
        name: 'woBasicTypeOfAct',
        type: 'string', // 基础大类，用于限制工单类型lov查询结果 。
        // 新建的时候取标准作业上的，编辑的时候由于这个字段没有存入表里，所以取工单类型查询接口返回的值。
      },
      {
        name: 'woTypeId',
        type: 'number',
        bind: 'woTypeLov.woTypeId',
      },
      {
        name: 'woTypeCode',
        type: 'string',
        bind: 'woTypeLov.woTypeCode',
      },
      {
        name: 'woTypeName',
        label: intl.get(`${modelPrompt}.woType`).d('工单类型'),
        type: 'string',
        bind: 'woTypeLov.woTypeName',
      },
      {
        name: 'woName',
        label: intl.get(`${modelPrompt}.woName`).d('工单概述'),
        type: 'string',
        required: true,
        maxLength: 240,
      },
      {
        name: 'actId', // 标准作业
        type: 'number',
      },
      {
        name: 'actName',
        type: 'string',
        label: intl.get(`${modelPrompt}.act`).d('标准作业'),
      },
      {
        name: 'faultReason',
        type: 'string',
        label: intl.get(`${modelPrompt}.faultReason`).d('故障原因'),
        lookupCode: 'ALM.FAULT_REASON',
        dynamicProps: {
          required: ({ record }) => !record.get('isNew') && !['REJECTED', 'COMPLETED', 'CLOSED', 'CANCELED', 'UNABLE'].includes(record.get('woStatus')),
          disabled: ({ record }) => ['REJECTED', 'COMPLETED', 'CLOSED', 'CANCELED', 'UNABLE'].includes(record.get('woStatus')),
        },
      },
      {
        name: 'remark',
        type: 'string',
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
        dynamicProps: {
          required: ({ record }) => !record.get('isNew') && record.get('faultReason') === '其他' && !['REJECTED', 'COMPLETED', 'CLOSED', 'CANCELED', 'UNABLE'].includes(record.get('woStatus')),
          disabled: ({ record }) => ['REJECTED', 'COMPLETED', 'CLOSED', 'CANCELED', 'UNABLE'].includes(record.get('woStatus')),
        },
      },
      {
        name: 'description',
        label: intl.get(`${modelPrompt}.description`).d('问题描述'),
        type: 'string',
        maxLength: 240,
      },
      {
        name: 'orgName',
        label: intl.get(`${modelPrompt}.orgName`).d('客户/需求组织'),
        type: 'string',
      },
      {
        name: 'orgId',
        type: 'number',
      },
      {
        name: 'contactLov',
        label: intl.get(`${modelPrompt}.contact`).d('需求方联系人'),
        type: 'object',
        lovCode: 'AORI.EMPLOYEE_UNIT',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: organizationId,
              unitId: record.get('orgId'),
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'contactId',
        type: 'number',
        bind: 'contactLov.employeeId',
      },
      {
        name: 'contactName',
        label: intl.get(`${modelPrompt}.contact`).d('需求方联系人'),
        type: 'string',
        bind: 'contactLov.employeeName',
      },
      {
        name: 'manuallySpecifyFlag',
        type: 'boolean',
        label: intl.get(`${modelPrompt}.manuallySpecifyFlag`).d('手工指定联系人'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'contactDesc',
        label: intl.get(`${modelPrompt}.contactDesc`).d('联系人'),
        type: 'string',
      },
      {
        name: 'phone',
        label: intl.get(`${modelPrompt}.phone`).d('联系电话'),
        type: 'string',
      },
      {
        name: 'assetId',
        type: 'number',
      },
      {
        name: 'descAndLabel', // 资产全称assetDesc 及 资产标签 visualLabel 拼接而成
        type: 'string',
        label: intl.get(`${modelPrompt}.asset`).d('设备'),
        dynamicProps: {
          required: ({ record }) => {
            const typeData = record.get('currentWoType');
            if (typeData) {
              const { workObjCtrlCode, woBasicType } = typeData;
              if (woBasicType === 'CHECKING_TYPE') {
                // 工单类型基础大类为 检查类 时，该字段隐藏
                return false;
              } else if (workObjCtrlCode && workObjCtrlCode === 'LOCATION_OR_DEVICE') {
                // 工单类型上的 工单工作对象控制（workObjCtrlCode）为以下值时必输：
                // LOCATION_OR_DEVICE (必须提供设备、位置)
                return true;
              }
            }
          },
        },
      },

      {
        name: 'assetLocationName',
        label: intl.get(`${modelPrompt}.assetLocation`).d('位置'),
        type: 'string',
        dynamicProps: {
          required: ({ record }) => {
            const typeData = record.get('currentWoType');
            if (typeData) {
              const { workObjCtrlCode, woBasicType } = typeData;
              if (woBasicType === 'CHECKING_TYPE') {
                // 工单类型基础大类为 检查类 时，该字段隐藏
                return false;
              } else if (
                workObjCtrlCode &&
                (workObjCtrlCode === 'LOCATION_OR_DEVICE' || workObjCtrlCode === 'LOCATION')
              ) {
                // 工单类型上的 工单工作对象控制（workObjCtrlCode）为以下值时必输：
                // LOCATION_OR_DEVICE (必须提供设备、位置) LOCATION (必须提供位置)
                return true;
              }
            }
          },
        },
      },
      {
        name: 'assetLocationId',
        type: 'number',
      },
      {
        name: 'locationDesc',
        label: intl.get(`${modelPrompt}.locationDesc`).d('位置补充说明'),
        type: 'string',
        maxLength: 240,
      },
      {
        name: 'costObjectLimitType', // 成本对象限制
        type: 'string',
      },
      {
        name: 'costObjectType',
        label: intl.get(`${modelPrompt}.costObjectType`).d('成本对象类型'),
        type: 'string',
        lookupCode: 'HPFM.FINANCIAL_CODE_TYPE',
        required: true,
        // dynamicProps: {
        //   required: ({ record }) => record.get('costObjectLimitType') === 'DISPLAY&MANDATORY',
        // },
      },
      {
        name: 'costObjectLov',
        label: intl.get(`${modelPrompt}.costObject`).d('成本对象'),
        type: 'object',
        lovCode: 'HPFM.FINANCE_CODE',
        lovPara: {
          tenantId: organizationId,
        },
        cascadeMap: {
          type: 'costObjectType',
        },
        required: true,
        // dynamicProps: {
        //   required: ({ record }) => record.get('costObjectLimitType') === 'DISPLAY&MANDATORY',
        // },
      },
      {
        name: 'costObject',
        type: 'number',
        bind: 'costObjectLov.codeId',
      },
      {
        name: 'costObjectMeaning',
        type: 'string',
        bind: 'costObjectLov.name',
      },
      {
        name: 'mapSourceCode',
        label: intl.get(`${modelPrompt}.mapSourceCode`).d('地图来源'),
        type: 'string',
        defaultValue: 'NO_DISPLAY',
        lookupCode: 'AMTC.MAP_SOURCE',
      },
      {
        name: 'serviceAgreementLov',
        label: intl.get(`${modelPrompt}.serviceAgreement`).d('服务协议'),
        type: 'object',
        lovCode: 'AMTC.SERVICE_AGREEMENT',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              woAssetId: record.get('assetId'),
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'serviceAgreementId',
        type: 'number',
        bind: 'serviceAgreementLov.serviceAgreementId',
      },
      {
        name: 'agreementName',
        type: 'string',
        bind: 'serviceAgreementLov.agreementName',
      },
      {
        name: 'partnerName',
        type: 'string',
        bind: 'serviceAgreementLov.partnerName',
      },
      {
        name: 'agreementDescription',
        type: 'string',
        bind: 'serviceAgreementLov.description',
      },
      {
        name: 'plannerGroupId',
        type: 'number',
        required: true,
      },
      {
        name: 'plannerGroupName',
        label: intl.get(`${modelPrompt}.plannerGroup`).d('计划员组'),
        required: true,
        type: 'string',
      },
      {
        name: 'plannerId',
        type: 'number',
      },
      {
        name: 'plannerName',
        label: intl.get(`${modelPrompt}.planner`).d('计划员'),
        type: 'string',
      },
      {
        name: 'ownerGroupId',
        type: 'number',
        required: true,
      },
      {
        name: 'ownerGroupName',
        label: intl.get(`${modelPrompt}.ownerGroup`).d('负责人组'),
        required: true,
        type: 'string',
      },
      {
        name: 'ownerId',
        type: 'number',
      },
      {
        name: 'ownerName',
        label: intl.get(`${modelPrompt}.owner`).d('负责人'),
        type: 'string',
      },
      {
        name: 'waitingWoopownerFlag',
        type: 'boolean',
        label: intl.get(`${modelPrompt}.waitingWoopownerFlag`).d('抢单模式'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'priorityLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.priority`).d('计划优先级'),
        required: true,
        ignore: 'always',
        lovCode: 'AMTC.PRIORITIES',
      },
      {
        name: 'priorityId',
        type: 'number',
        bind: 'priorityLov.priorityId',
      },
      {
        name: 'priorityName',
        type: 'string',
        bind: 'priorityLov.priorityName',
        label: intl.get(`${modelPrompt}.priority`).d('计划优先级'),
      },
      {
        name: 'checkerLov',
        label: intl.get(`${modelPrompt}.checker`).d('验收员'),
        type: 'object',
        lovCode: 'HALM.EMPLOYEE_ORG',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: organizationId,
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'checkerId',
        type: 'number',
        bind: 'checkerLov.employeeId',
      },
      {
        name: 'checkerName',
        label: intl.get(`${modelPrompt}.checker`).d('验收员'),
        type: 'string',
        bind: 'checkerLov.employeeName',
      },
      {
        name: 'durationScheduled',
        label: intl.get(`${modelPrompt}.durationScheduled`).d('计划时长'),
        type: 'number',
        step: 0.01,
        min: 0,
        pattern: /^\d{1,4}(\.\d{1,2})?$/g,
        dynamicProps: {
          required: ({ record }) => {
            // 工单类型上的 计划状态（scheduleRequirmentStatus）为 工单可直接执行（	CANBE_EXECUTED_DIRECTLY ）时隐藏：
            const typeData = record.get('currentWoType');
            if (typeData) {
              const { scheduleRequirmentStatus } = typeData;
              if (
                scheduleRequirmentStatus &&
                scheduleRequirmentStatus !== 'CANBE_EXECUTED_DIRECTLY'
              ) {
                return true;
              } else {
                return false;
              }
            }
          },
        },
      },
      {
        name: 'durationUom',
        label: intl.get(`${modelPrompt}.durationUom`).d('工期单位'),
        type: 'string',
        lookupCode: 'AMTC.DURATION_UNIT',
        defaultValue: 'HOUR',
        required: true,
      },
      {
        name: 'durationUomMeaning',
        type: 'string',
      },
      {
        name: 'scheduledStartDate',
        label: intl.get(`${modelPrompt}.scheduledStartDate`).d('计划开始时间'),
        type: 'dateTime',
        max: 'scheduledFinishDate',
        dynamicProps: {
          required: ({ record }) => {
            // 工单类型上的 计划状态（scheduleRequirmentStatus）为 计划后下达工单（	RELEASE_AFTER_PLAN）时必输：
            const typeData = record.get('currentWoType');
            if (typeData) {
              const { scheduleRequirmentStatus } = typeData;
              if (scheduleRequirmentStatus && scheduleRequirmentStatus === 'RELEASE_AFTER_PLAN') {
                return true;
              }
            }
          },
        },
      },
      {
        name: 'scheduledFinishDate',
        label: intl.get(`${modelPrompt}.scheduledFinishDate`).d('计划完成时间'),
        type: 'dateTime',
        min: 'scheduledStartDate',
      },
      {
        name: 'targetStartDate',
        label: intl.get(`${modelPrompt}.targetStartDate`).d('目标开始日期'),
        type: 'dateTime',
        max: 'targetFinishDate',
        dynamicProps: {
          required: ({ record }) => {
            // 工单基本类型是保养时必输
            const woBasicType = record.get('woBasicType');
            return woBasicType === 'FAULT_MAINTAIN_TYPE';
          },
        },
      },
      {
        name: 'targetFinishDate',
        label: intl.get(`${modelPrompt}.targetFinishDate`).d('目标完成日期'),
        type: 'dateTime',
        min: 'targetStartDate',
        dynamicProps: {
          required: ({ record }) => {
            // 工单基本类型是保养时必输
            const woBasicType = record.get('woBasicType');
            return woBasicType === 'FAULT_MAINTAIN_TYPE';
          },
        },
      },
      {
        name: 'actualStartDate',
        label: intl.get(`${modelPrompt}.actualStartDate`).d('实际开始时间'), // 仅详情界面显示
        type: 'dateTime',
        max: 'actualFinishDate',
      },
      {
        name: 'actualFinishDate',
        label: intl.get(`${modelPrompt}.actualFinishDate`).d('实际完成时间'), // 仅详情界面显示
        type: 'dateTime',
        min: 'actualStartDate',
      },
      {
        name: 'durationActual',
        label: intl.get(`${modelPrompt}.durationActual`).d('实际工期'), // 仅详情界面显示
        type: 'string',
      },
      {
        name: 'reporterOrgLov',
        label: intl.get(`${modelPrompt}.reportOrgName`).d('报告人所在组织'),
        type: 'object',
        lovCode: 'AMDM.ORG_LIST_C7N',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: organizationId,
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'reportOrgId',
        type: 'number',
        bind: 'reporterOrgLov.unitId',
      },
      {
        name: 'reportOrgName',
        label: intl.get(`${modelPrompt}.reportOrgName`).d('报告人所在组织'),
        type: 'string',
        bind: 'reporterOrgLov.unitName',
      },
      {
        name: 'reporterLov',
        label: intl.get(`${modelPrompt}.reporter`).d('报告人'),
        type: 'object',
        lovCode: 'AORI.EMPLOYEE_UNIT',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: organizationId,
              unitId: record.get('reportOrgId'),
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'reporterId',
        type: 'number',
        bind: 'reporterLov.employeeId',
      },
      {
        name: 'reporterName',
        label: intl.get(`${modelPrompt}.reporter`).d('报告人'),
        type: 'string',
        bind: 'reporterLov.employeeName',
      },
      {
        name: 'reportDate',
        label: intl.get(`${modelPrompt}.reportDate`).d('报告时间'),
        type: 'dateTime',
        defaultValue: new Date(),
      },
      {
        name: 'reportPriorityLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.reportPriority`).d('报告优先级'),
        ignore: 'always',
        lovCode: 'AMTC.PRIORITIES',
      },
      {
        name: 'reportPriorityId',
        type: 'number',
        bind: 'reportPriorityLov.priorityId',
      },
      {
        name: 'reportPriorityName',
        type: 'string',
        bind: 'reportPriorityLov.priorityName',
        label: intl.get(`${modelPrompt}.reportPriority`).d('报告优先级'),
      },
      {
        name: 'sourceId', // 来源id
        type: 'number',
      },
      {
        name: 'sourceTypeCode',
        label: intl.get(`${modelPrompt}.sourceTypeCode`).d('工单来源'),
        type: 'string',
        lookupCode: 'AMTC.WO_SOURCE_TYPE',
      },
      {
        name: 'sourceReference',
        label: intl.get(`${modelPrompt}.sourceReference`).d('来源单据号'),
        type: 'string',
      },
      {
        name: 'longitude', // 经度
        type: 'string',
      },
      {
        name: 'latitude', // 纬度
        type: 'string',
      },
      {
        name: 'alarmRecordIds', // 告警记录id
        type: 'number',
        multiple: true,
      },
      // 附件关联id
      {
        name: 'fileModuleId',
        type: 'number',
        defaultValue: Date.now(),
      },
    ],
    transport: {
      read: ({ params, dataSet }) => {
        const { woId } = dataSet;
        return {
          url: `${queryListUrl}/${woId}`,
          method: 'GET',
          params: {
            ...params,
            tenantId: organizationId,
          },
        };
      },
    },
  };
}

function checkRecordDs() {
  return {
    autoQuery: false,
    primaryKey: 'checkId',
    selection: false,
    fields: [
      {
        name: 'checkDate',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.checkDate`).d('验收时间'),
      },
      {
        name: 'passFlag',
        type: 'boolean',
        label: intl.get(`${modelPrompt}.passFlag`).d('验收结果'),
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'checkerName',
        type: 'string',
        label: intl.get(`${modelPrompt}.checker`).d('验收员'),
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          url: `${queryCheckRecord}`,
          method: 'GET',
          params,
        };
      },
    },
  };
}

export { basicDataDs, checkRecordDs };
