import React, { useMemo } from 'react';
import { Table, DataSet, Modal } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { observer } from 'mobx-react';
import { BASIC } from '@utils/config';
import { tableDS, inspectValueDS } from './stores/TableDS';

const modelPrompt = 'check.presentation.report';

const CheckPresentationReport = observer((props) => {
  const { tableDs, inspectValueDs } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'siteName',
      },
      {
        name: 'inspectItemCode',
      },
      {
        name: 'inspectItemDesc',
      },
      {
        title: intl.get(`${modelPrompt}.inspectValue`).d('检测值'),
        renderer: ({ record }) => (
          <a onClick={() => handleInspectValue(record)}>{intl.get(`${modelPrompt}.detail`).d('明细')}</a>
        ),
      },
      {
        name: 'supplierName',
      },
      {
        name: 'shiftTeamName',
      },
      {
        name: 'equipmentName',
      },
      {
        name: 'operationDesc',
      },
      {
        name: 'processWorkcellName',
      },
      {
        name: 'lastInspectDate',
      },
    ];
  }, []);

  const inspectValueColumns = (record) => {
    return [
      {
        title: record?.get('inspectItemCode'),
        children: [{ name: 'lastUpdateDate' },{ name: 'inspectValue' }],
      },
    ];
  }

  const handleInspectValue = async (record) => {
    const res = await request(
      `${
        BASIC.TARZAN_SAMPLING
      }/v1/${getCurrentOrganizationId()}/mt-inspect-doc/Inspection-data/line/ui`,
      {
        method: 'GET',
        params: {
          inspectDocLineId: record.get('inspectDocLineId'),
        },
      },
    )
    if (res?.length) {
      inspectValueDs.loadData(res)
    } else {
      notification.error({})
    }
    Modal.open({
      drawer: true,
      maskClosable: true,
      okCancel: false,
      title: intl.get(`${modelPrompt}.detail`).d('明细'),
      children: (
        <Table dataSet={inspectValueDs} columns={inspectValueColumns(record)}/>
      ),
      style: { width:720 },
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('检验数据展示报表')}>
        <ExcelExport
          method="GET"
          requestUrl={`${
            BASIC.TARZAN_SAMPLING
          }/v1/${getCurrentOrganizationId()}/mt-inspect-doc/Inspection-data/export/ui`}
          queryParams={tableDs.queryDataSet.toData()[0]}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          otherButtonProps={{
            disabled: !tableDs.length,
          }}
        />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFieldsLimit={7}
          dataSet={tableDs}
          columns={columns}
          searchCode="CheckPresentationReport"
          customizedCode="CheckPresentationReport"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      const inspectValueDs = new DataSet(inspectValueDS());
      return {
        tableDs,
        inspectValueDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(CheckPresentationReport),
);
