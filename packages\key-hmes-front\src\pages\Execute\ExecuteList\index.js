/**
 * @Description: 执行作业管理列表页
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:40:22
 * @LastEditTime: 2023-01-10 13:56:55
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { DataSet, Table, Button, Modal } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import { getResponse } from '@utils/utils';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
// import ExcelExport from 'components/ExcelExport';
import { observer } from 'mobx-react';
import { tableDS, historyDS } from '../stores/ExecuteListDS';
import HistoryDrawer from './HistoryDrawer';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.workshop.execute';

const ExecuteList = observer(props => {
  const {
    match: { path },
    dataSet,
    customizeTable,
  } = props;

  // 选中列表项执行作业状态 (选中条状态不同时为空字符)
  // 选中列表项执行作业状态 (选中条状态不同时为空字符)
  const [selectIds, setSelectIds] = useState([]);
  const historyDs = useMemo(() => new DataSet(historyDS()), []);

  const columns = [
    {
      name: 'eoNum',
      lock: 'left',
      renderer: ({ record, value }) => {
        return (
          <a
            onClick={() => {
              orderDetail(record.data.eoId);
            }}
          >
            {value}
          </a>
        );
      },
      width: 220,
    },
    {
      name: 'statusDesc',
      width: 150,
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'revisionCode',
      width: 120,
    },
    {
      name: 'materialName',
      width: 200,
    },
    {
      name: 'qty',
      width: 150,
      align: 'right',
    },
    {
      name: 'completedQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'scrappedQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'workOrderNum',
      width: 200,
    },
    {
      name: 'planStartTime',
      width: 150,
      align: 'center',
    },
    {
      name: 'planEndTime',
      width: 150,
      align: 'center',
    },
    {
      name: 'eoTypeDesc',
      width: 150,
    },
    {
      name: 'productionLineCode',
      width: 200,
    },
    // {
    //   name: 'productionLineName',
    //   width: 200,
    // },
    {
      name: 'siteCode',
      width: 120,
    },
    {
      name: 'boilerNumber',
      width: 120,
    },
    {
      name: 'operationName',
      width: 120,
    },
    {
      name: 'description',
      width: 120,
    },
    {
      name: 'qualityStatusDesc',
      width: 120,
    },
  ];

  useEffect(() => {
    // 进入页面，进行数据查询时，有两种不同查询情况
    // 1.从新建/详情页返回到列表页
    // 2.从其他功能页面跳转到列表页
    if (Object.keys(props?.location?.query).length === 0 || props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      // dataSet.query(dataSet.currentPage);
      dataSet.setQueryParameter('customizeUnitCode', 'MT_EO.LIST.QUERY,MT_EO.LIST.TABLE');
      dataSet.query(props.dataSet.currentPage);
      handleDataSetSelectUpdate();
    }
    // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
    const { workOrderNum, workOrderId } = props?.location?.query || {};
    const queryParams = {
      workOrder: workOrderNum ? { workOrderNum, workOrderId } : undefined, // 回显生产指令编码
      workOrderId: workOrderId || undefined, // 回显生产指令编码
    };
    setTimeout(() => {
      dataSet.queryDataSet.loadData([queryParams]);
      dataSet.setQueryParameter('customizeUnitCode', 'MT_EO.LIST.QUERY,MT_EO.LIST.TABLE');
      dataSet.query(props.dataSet.currentPage);
      handleDataSetSelectUpdate();
    }, 200);
  }, [props?.location?.query, props?.location?.state]);

  // useEffect(() => {
  //   dataSet.setQueryParameter('customizeUnitCode', 'MT_EO.LIST.QUERY,MT_EO.LIST.TABLE');
  //   dataSet.query(props.dataSet.currentPage);
  //   handleDataSetSelectUpdate();
  // }, []);

  // 监听C7Npro列表选中操作
  useEffect(() => {
    if (dataSet) {
      dataSet.addEventListener('select', handleDataSetSelectUpdate);
      dataSet.addEventListener('unSelect', handleDataSetSelectUpdate);
      dataSet.addEventListener('selectAll', handleDataSetSelectUpdate);
      dataSet.addEventListener('unSelectAll', handleDataSetSelectUpdate);
    }
    return () => {
      if (dataSet) {
        dataSet.removeEventListener('select', handleDataSetSelectUpdate);
        dataSet.removeEventListener('unSelect', handleDataSetSelectUpdate);
        dataSet.removeEventListener('selectAll', handleDataSetSelectUpdate);
        dataSet.removeEventListener('unSelectAll', handleDataSetSelectUpdate);
      }
    };
  });

  // 处理选中条执行作业状态
  const handleDataSetSelectUpdate = () => {
    if (dataSet && dataSet.selected) {
      const selectList = dataSet.selected;
      if (selectList && selectList.length > 0) {
        const { status: _status } = selectList[0].toData();
        let statusSame = true;
        const _selectIds = [];
        selectList.forEach(item => {
          const { status: itemStatus, eoId: itemEoId } = item.toData();
          if (itemEoId) {
            _selectIds.push(itemEoId);
          }
          if (itemStatus !== _status) {
            statusSame = false;
          }
        });
        if (statusSame) {
          setSelectIds(_selectIds);
        } else {
          setSelectIds([]);
        }
      } else {
        setSelectIds([]);
      }
    } else {
      setSelectIds([]);
    }
  };

  const orderDetail = id => {
    props.history.push(`/hmes/workshop/execute-operation-management/detail/${id}`);
  };

  const clickMenu = async ({ key }) => {
    const response = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/status/update/ui`, {
      method: 'POST',
      body: {
        eoIds: selectIds,
        operationType: key,
      },
    });
    const res = getResponse(response);
    if (res) {
      if (dataSet) {
        const selectList = dataSet.selected;
        const _selectIds = [];
        selectList.forEach(item => {
          const { eoId } = item.toData();
          if (eoId) {
            _selectIds.push(eoId);
          }
        });
        setSelectIds(_selectIds);
      }
      notification.success();
      dataSet.query(props.dataSet.currentPage);
    }
  };

  const handleClose = async () => {
    Modal.confirm({
      title: '是否确认关闭所选执行作业？',
      onOk: async () => {
        const response = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo/close`, {
          method: 'POST',
          body: dataSet.selected.map(item => item.data),
        });
        const res = getResponse(response);
        if (res) {
          notification.success();
          dataSet.query(props.dataSet.currentPage);
        }
      },
    });
  };

  const handleReopen = async () => {
    const response = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/working/again`, {
      method: 'POST',
      body: dataSet.selected.map(item => item.data),
    });
    const res = getResponse(response);
    if (res) {
      notification.success();
      dataSet.query(props.dataSet.currentPage);
    }
  };

  const handleQueryMaterialLotsHistory = () => {
    const eoId = dataSet.selected.map(ele => ele.toData().eoId);
    historyDs.setQueryParameter('eoId', eoId);
    historyDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      closable: true,
      drawer: true,
      maskClosable: false,
      style: {
        width: 1080,
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      key: Modal.key(),
      title: (
        <div
          style={{
            width: 'calc(100% - 20px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}</div>
          {/* <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/export/his/ui`}
            queryParams={{
              materialLotIds: eoId,
            }}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          /> */}
        </div>
      ),
      destroyOnClose: true,
      children: <HistoryDrawer ds={historyDs} />,
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('执行作业管理')}>
        <Button
          onClick={() => {
            clickMenu({ key: 'RELEASE' });
          }}
          disabled={
            dataSet.selected.length === 0 ||
            dataSet.selected.some(item => item?.get('status') !== 'NEW')
          }
        >
          {intl.get(`${modelPrompt}.button.issued`).d('下达')}
        </Button>
        <Button
          onClick={() => {
            clickMenu({ key: 'EO_WORKING_CANCEL' });
          }}
          disabled={
            dataSet.selected.length === 0 ||
            dataSet.selected.some(item => item?.get('status') !== 'WORKING')
          }
        >
          {intl.get(`${modelPrompt}.button.workingCancel`).d('取消运行')}
        </Button>
        <Button
          onClick={handleClose}
          disabled={
            dataSet.selected.length === 0 ||
            dataSet.selected.some(item => item?.get('status') !== 'WORKING')
          }
        >
          {intl.get(`${modelPrompt}.button.close`).d('关闭')}
        </Button>
        <PermissionButton
          type="c7n-pro"
          icon="cached"
          onClick={handleReopen}
          disabled={
            dataSet.selected.length === 0 ||
            dataSet.selected.some(item => item?.get('status') !== 'CLOSED')
          }
          permissionList={[
            {
              code: `${path}.button.reopen`,
              type: 'button',
              meaning: '列表页-重新打开按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.reopen`).d('重新打开')}
        </PermissionButton>
        <Button disabled={!dataSet.selected.length} onClick={handleQueryMaterialLotsHistory}>
          {intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}
        </Button>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: 'MT_EO.LIST.QUERY',
            code: 'MT_EO.LIST.TABLE',
          },
          <Table
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            queryFieldsLimit={8}
            dataSet={dataSet}
            columns={columns}
            searchCode="ExecuteList"
            customizedCode="ExecuteList"
            pagination={{ pageSizeOptions: ['10', '20', '50', '100', '200'] }}
          />,
        )}
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.workshop.execute', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...tableDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: ['MT_EO.LIST.QUERY', 'MT_EO.LIST.TABLE'],
    })(ExecuteList),
  ),
);
