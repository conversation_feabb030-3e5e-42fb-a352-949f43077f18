/**
 * @Description:  投入产出报表-入口页
 */
import React, {useMemo} from 'react';
import { DataSet, Table, Modal} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { flow, isNil } from 'lodash';
import { BASIC } from '@utils/config';
import { tableDS, drawerDS } from './stores/ListDS';

const endUrl = '';
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inputOutputReport';

const InputOutputReportList = observer(props => {
  const { tableDs } = props;

  const drawerDs = useMemo(() => new DataSet(drawerDS()), []);

  const assembleColumns = [
    {
      name: 'workOrderNum',
      align: 'center',
    },
    {
      name: 'materialName',
      align: 'center',
    },
    {
      name: 'assembleQty',
      align: 'center',
    },
  ];

  const primaryUomColumns = [
    {
      name: 'workOrderNum',
      align: 'center',
    },
    {
      name: 'type',
      align: 'center',
    },
    {
      name: 'materialName',
      align: 'center',
    },
    {
      name: 'productionBatch',
      align: 'center',
    },
    {
      name: 'primaryUomQty',
      align: 'center',
    },
  ];

  const handleToOpenModal = (type, record) => {
    let drawerData;
    let title;
    if (type === 'Assemble') {
      title = intl.get(`${modelPrompt}.modal.Assemble`).d('投入');
      drawerData = record.get('assembleList');
    } else {
      title = intl.get(`${modelPrompt}.modal.PrimaryUom`).d('产出');
      drawerData = record.get('lotList');
    }
    drawerDs.loadData([...drawerData]);
    Modal.open({
      key: Modal.key(),
      style: {
        width: '720px',
      },
      title,
      drawer: false,
      closable: true,
      resizable: true,
      children: <Table dataSet={drawerDs} columns={type === 'Assemble' ? assembleColumns : primaryUomColumns} style={{ height: 400 }} />,
      footer: null,
    });
  };

  // 头列表配置
  const columns = [
    {
      name: 'siteCode',
      align: 'center',
    },
    {
      name: 'areaCode',
      align: 'center',
    },
    {
      name: 'workOrderNum',
      align: 'center',
    },
    {
      name: 'materialCode',
      align: 'center',
    },
    {
      name: 'materialName',
      align: 'center',
      width: 200,
    },
    {
      name: 'qty',
      align: 'center',
    },
    {
      name: 'sumAssembleQty',
      align: 'center',
      renderer: ({ record }) => (
        <a
          onClick={() => handleToOpenModal('Assemble', record)}
        >
          {record?.get('sumAssembleQty')}
        </a>
      ),
    },
    {
      name: 'sumPrimaryUomQty',
      align: 'center',
      renderer: ({ record }) => (
        <a
          onClick={() => handleToOpenModal('PrimaryUom', record)}
        >
          {record?.get('sumPrimaryUomQty')}
        </a>
      ),
    },
    {
      name: 'rate',
      align: 'center',
    },
  ];

  const onFieldEnterDown = () => {
    tableDs.query(props.tableDs.currentPage);
  };

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParams = tableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return queryParams;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('投入产出报表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-input-product-report/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="inputOutputReportList"
          customizedCode="inputOutputReportList"
          dataSet={tableDs}
          columns={columns}
          highLightRow
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
            onFieldEnterDown,
          }}
        />,
      </Content>
    </div>
  );
});

export default flow(
  formatterCollections({ code: ['tarzan.inputOutputReport', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({...tableDS()});
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(InputOutputReportList);
