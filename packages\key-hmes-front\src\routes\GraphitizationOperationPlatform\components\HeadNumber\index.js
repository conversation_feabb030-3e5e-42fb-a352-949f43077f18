/* eslint-disable jsx-a11y/alt-text */
// 新加工件
import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Button, Table, Modal, Select } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import intl from 'utils/intl';
import { getCurrentUser } from 'utils/utils';
import { connect } from 'dva';
import { observer } from 'mobx-react';
import { workOrderDS, detailDS } from './stores/HeatNumberDS';
import { CardLayout } from '../commonComponents';
import { Complete, QueryWorkInfo, SaveWorkOrder, FeedEnd, Create } from './services';
import { namespace } from '../../model';
import cardSvg from './check.png';
import styles from './index.modules.less';

let workOrderModal = null;

const modelPrompt = 'tarzan.hmes.graphitizationOperationPlatform.HeadNumber';

const HeatNumber = observer(props => {
  const { dispatch } = props;
  const workOrderDs = useMemo(
    () =>
      new DataSet({
        ...workOrderDS(),
      }),
    [],
  );
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );
  const { run: queryWorkInfo, loading: queryWorkInfoLoading } = useRequest(QueryWorkInfo(), {
    manual: true,
    needPromise: true,
  });
  const { run: feedEnd, loading: feedLoading } = useRequest(FeedEnd(), {
    manual: true,
    needPromise: true,
  });

  const { run: saveWorkOrder, loading: saveWorkOrderLoading } = useRequest(SaveWorkOrder(), {
    manual: true,
    needPromise: true,
  });
  const { run: complete, loading: completeLoading } = useRequest(Complete(), {
    manual: true,
    needPromise: true,
  });
  const { run: create, loading: createLoading } = useRequest(Create(), {
    manual: true,
    needPromise: true,
  });

  const [dataList, setDataList] = useState([]);
  const [currentEo, setCurrentEo] = useState(null);
  const [catchStoveCode, setCatchStoveCode] = useState(null);
  const { modelState = {} } = props;
  const { refreshStoveData = [] } = modelState;

  useEffect(() => {
    if (refreshStoveData) {
      init();
      props.handleRefresh({
        refreshStoveData: false,
      });
    }
  }, [refreshStoveData]);

  useEffect(() => {
    if (props.modelState?.stoveCode && props.modelState?.stoveCode !== catchStoveCode) {
      setCatchStoveCode(props.modelState.stoveCode);
      init();
    }
  }, [props.modelState?.stoveCode]);

  // 查询数据
  const init = async () => {
    const res = await queryWorkInfo({
      params: { ...props.modelState },
    });
    if (res && res.failed) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'FAIL',
        recordType: 'complete',
        message: res.message,
      });
    } else {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'query',
        message: '查询成功！',
      });
      dispatch({
        type: `${namespace}/updateState`,
        payload: {
          ...res,
          realName: getCurrentUser().realName,
          userId: getCurrentUser().id,
          logined: true,
          selectEo: res.eoList.filter(item => item.eoId === currentEo)[0],
        },
      });
      handleData(res.eoList || []);
      console.log('props.modelState', props.modelState)

    }
  };

  const handleData = data => {
    const stoveCodelist = data
      .map(item => item.stoveCode)
      .filter((item, index, array) => array.indexOf(item) === index);
    const newArr = [];
    stoveCodelist.forEach(item => {
      const obj = {
        stoveCode: item,
        children: data.filter(i => i.stoveCode === item),
      };
      newArr.push(obj);
    });

    newArr.forEach(item => {
      const layer = item.children
        .map(m => m.layerMeaning)
        .filter((item, index, array) => array.indexOf(item) === index);
      const child = [];
      for (let i = 0; i < layer.length; i++) {
        const obj = {
          layerMeaning: layer[i],
          children: item.children.filter(m => m.layerMeaning === layer[i]),
        };
        child.push(obj);
      }
      item.children = child;
    });
    setDataList(newArr);
  };
  const handleCreateWorkOrder = async () => {
    workOrderDs.setQueryParameter('stoveCode', props.modelState.stoveCode);
    await workOrderDs.query();
    workOrderModal = Modal.open({
      key: 'workOrderModal',
      title: intl.get(`${modelPrompt}.createWorkOrder`).d('创建工单'),
      destroyOnClose: true,
      closable: true,
      contentStyle: { backgroundColor: '#3c87ad' },
      mask: false,
      style: { width: '1400px' },
      className: styles.HeatNumber,
      children: <Table dataSet={workOrderDs} columns={columns} />,
      footer: (
        <>
          <Button onClick={handleCloseModal}>{intl.get(`${modelPrompt}.cancel`).d('取消')}</Button>
          <Button onClick={handleSave}>{intl.get(`${modelPrompt}.save`).d('保存')}</Button>
          <Button onClick={handleComplete}>
            {intl.get(`${modelPrompt}.complete`).d('装炉完成')}
          </Button>
        </>
      ),
    });
  };
  const handleUpdate = loading => {
    workOrderModal.update({
      footer: (
        <>
          <Button disabled={loading} onClick={handleCloseModal}>
            {intl.get(`${modelPrompt}.cancel`).d('取消')}
          </Button>
          <Button disabled={loading} onClick={handleSave}>
            {intl.get(`${modelPrompt}.save`).d('保存')}
          </Button>
          <Button disabled={loading} onClick={handleComplete}>
            {intl.get(`${modelPrompt}.complete`).d('装炉完成')}
          </Button>
        </>
      ),
    });
  };
  const handleComplete = async () => {
    const p = workOrderDs.records.map(item => item?.validate(true, false));
    const resp = await Promise.all(p);
    if (resp.some(item => !item)) return;
    handleUpdate(true);
    const res = await complete({
      params: workOrderDs.toData().map(item => ({ ...item, siteCode: props.modelState.siteCode })),
    });
    handleUpdate(false);
    if (res && !res.success) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'FAIL',
        recordType: 'complete',
        message: res.message,
      });
    } else {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'complete',
        message: '操作成功',
      });
      props.handleRefresh({
        refreshStoveData: true,
      });
      workOrderModal?.close();
    }
  };
  const handleSave = async () => {
    const p = workOrderDs.records.map(item => item?.validate());
    const resp = await Promise.all(p);
    if (resp.some(item => !item)) return;
    if (workOrderDs.toJSONData().length === 0) return;
    handleUpdate(true);
    const res = await saveWorkOrder({
      params: workOrderDs
        .toJSONData()
        .map(item => ({ ...item, siteCode: props.modelState.siteCode })),
    });
    handleUpdate(false);
    if (res && res.failed) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'FAIL',
        recordType: 'save',
        message: res.message,
      });
      return false;
    }
    props.handleAddRecords({
      cardId: props.cardId,
      messageType: 'SUCCESS',
      recordType: 'complete',
      message: '操作成功',
    });
    workOrderModal?.close();
    return true;
  };

  const handleCloseModal = () => {
    workOrderModal?.close();
  };

  const columns = [
    {
      name: 'serialNumber',
      width: 90,
      renderer: ({ record }) => record.index + 1,
    },
    {
      name: 'materialCode',
    },
    {
      name: 'lotCode',
      width: 90,
    },
    {
      name: 'qty',
      width: 100,
    },
    {
      name: 'workOrderType',
      width: 130,
      editor: ({ record }) =>
        record?.get('status') !== 'S' && (
          <Select
            getPopupContainer={() =>
              document.getElementById('graphitizationOperationPlatform') || document.body
            }
          />
        ),
    },
    {
      name: 'productionVersion',
      width: 130,
      editor: ({ record }) =>
        record?.get('status') !== 'S' && (
          <Select
            getPopupContainer={() =>
              document.getElementById('graphitizationOperationPlatform') || document.body
            }
          />
        ),
    },
    {
      name: 'status',
      width: 70,
    },
    {
      name: 'message',
    },
    {
      name: 'sapOrderNum',
    },
  ];

  const handleEnd = async () => {
    detailDs.setQueryParameter('stoveCode', props.modelState?.stoveCode);
    await detailDs.query().then(async ress => {
      if (ress && !ress.failed) {
        if (ress && ress.length) {
          const res = await feedEnd({
            params: {
              ...props.modelState,
            },
          });
          if (res && !res.success) {
            props.handleAddRecords({
              cardId: props.cardId,
              messageType: 'FAIL',
              recordType: 'save',
              message: res.message,
            });
          } else {
            props.handleAddRecords({
              cardId: props.cardId,
              messageType: 'SUCCESS',
              recordType: 'end',
              message: '投料结束，操作成功',
            });
            props.handleRefresh({
              refreshStoveData: true,
            });
          }
        } else {
          props.handleAddRecords({
            cardId: props.cardId,
            messageType: 'FAIL',
            recordType: 'save',
            message: '未投入煅后焦，请确认!',
          });
        }
      } else {
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'FAIL',
          recordType: 'save',
          message: ress.message,
        });
      }
    });
  };

  const handleSelect = item => {
    setCurrentEo(item.eoId);
    props.handleRefresh({
      selectEo: item,
    });
  };
  const handleCreate = async () => {
    const res = await create({
      params: {
        operationId: props.modelState.operationId,
        workcellId: props.modelState.workcellId,
        stoveHeadId: props.modelState.stoveHeadId,
        productionLineId: props.modelState.productionLineId,
      },
    });
    if (res && !res.success) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'FAIL',
        recordType: 'save',
        message: res.message,
      });
    } else {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'end',
        message: '操作成功',
      });
      props.handleRefresh({
        refreshStoveData: true,
      });
    }
  };

  const renderCard = () => {
    if (!dataList.length) return '';
    return (
      <>
        {dataList.length &&
          dataList.map(item => {
            return (
              <>
                <div className={styles.stoveCode}>炉号：{item.stoveCode}</div>
                {item.children &&
                  item.children.map(i => {
                    return (
                      <>
                        <div className={styles.box}>
                          <div className={styles.layer}>{i.layerMeaning}</div>
                          <div className={styles.content}>
                            {i.children.length &&
                              i.children.map(m => {
                                return (
                                  <>
                                    <div
                                      className={styles.contentItem}
                                      onClick={() => handleSelect(m)}
                                    >
                                      <div className={styles.contentHead}>
                                        <div className={styles.eoNum}>{m.eoNum}</div>
                                        <div className={styles.qty}>{m.qty}</div>
                                      </div>
                                      <div className={styles.contentBody}>
                                        <div className={styles.workOrder}>{m.workOrderNum}</div>
                                        <div className={styles.materialCode}>{m.materialCode}</div>
                                        <div className={styles.materialName}>{m.materialName}</div>
                                      </div>
                                      {currentEo === m.eoId && <img src={cardSvg} alt="" />}
                                    </div>
                                  </>
                                );
                              })}
                          </div>
                        </div>
                      </>
                    );
                  })}
              </>
            );
          })}
      </>
    );
  };

  return (
    <CardLayout.Layout
      className={styles.HeatNumber}
      spinning={queryWorkInfoLoading || feedLoading || saveWorkOrderLoading || completeLoading}
    >
      <CardLayout.Header
        title={intl.get(`${modelPrompt}.title`).d('石墨化单据')}
        help={props?.cardUsage?.meaning}
        content={
          props.modelState.sequence === 10 && (
            <>
              <Button
                onClick={handleCreate}
                style={{
                  fontSize: '16px',
                  backgroundColor: 'rgb(8, 64, 248)',
                  color: '#fff',
                  border: 'none',
                }}
              >
                {intl.get(`${modelPrompt}.create`).d('创建批次生产单')}
              </Button>
              <Button
                onClick={handleCreateWorkOrder}
                style={{
                  fontSize: '16px',
                  backgroundColor: 'rgb(223, 84, 57)',
                  color: '#fff',
                  border: 'none',
                }}
              >
                {intl.get(`${modelPrompt}.createWorkOrder`).d('创建工单')}
              </Button>
              <Button
                onClick={handleEnd}
                disabled={props.modelState.completeFlag !== 'Y'}
                style={{
                  fontSize: '16px',
                  backgroundColor: 'rgb(240, 194, 46)',
                  color: '#fff',
                  border: 'none',
                }}
              >
                {intl.get(`${modelPrompt}.feedOver`).d('投料结束')}
              </Button>
            </>
          )
        }
      />
      <CardLayout.Content>{renderCard()}</CardLayout.Content>
    </CardLayout.Layout>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.graphitizationOperationPlatform.HeadNumber', 'model.org.monitor'],
})(
  connect(state => {
    return {
      modelState: state[namespace],
    };
  })(HeatNumber),
);
