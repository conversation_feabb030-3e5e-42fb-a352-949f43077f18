import React, { useMemo } from 'react';
import { Row, Col, Tag, Spin, Tooltip, Collapse } from 'choerodon-ui';
import { Form, Output, Button } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { isUndefined } from 'lodash';
import BasicBMap from 'alm/components/BasicBMap';
import { statusColors } from 'alm/utils/constants';
import CommonComponent from 'alm/components/CommonComponent';
import UploadPanel from 'alm/components/UploadPanel';

const prompt = 'amtc.serviceApply';
const promptCode = `${prompt}.model.serviceApply`;

export default function ViewRender(props) {
  const {
    RefuseTip,
    detailFormDS,
    styles,
    showFault,
    goDetailPage,
    mapAddress,
    isShowMapFlag,
    isShowSearchFlag,
    evalHierarchyList,
    queryDeatilLoading,
    failureRequiredFlag,
    employeeId,
    fileModuleId,
  } = props;
  const mapDetailProps = {
    address: mapAddress,
    mapHeight: '120px',
    allowedDrag: false,
    isShowSpinOrSkeleton: false,
    allowedScrollWheelZoom: false,
    skeletonProps: {
      active: true,
      title: false,
      paragraph: {
        rows: 2,
      },
    },
  };

  const viewFailureRequiredFlag =
    failureRequiredFlag === '1' ||
    (detailFormDS &&
      detailFormDS.current &&
      detailFormDS.current.get('failureRequiredFlag') === '1');
  const faultDate = detailFormDS && detailFormDS.current && detailFormDS.current.get('faultData');
  const evalItemId = detailFormDS && detailFormDS.current && detailFormDS.current.get('evalItemId');
  const denialReason =
    detailFormDS && detailFormDS.current && detailFormDS.current.get('denialReason');
  const srStatus = detailFormDS && detailFormDS.current && detailFormDS.current.get('srStatus');
  const { checklistId } =
    (detailFormDS && detailFormDS.current && detailFormDS.current.toData()) || {};

  const fileUploadLogList = useMemo(() => {
    const list = [
      {
        moduleName: 'amtc-service-apply',
        moduleIdList: [fileModuleId],
      },
    ];
    // 由一个异常检查项创建查询关联附件
    if (checklistId) {
      list.push({
        moduleName: 'amtc-check',
        moduleIdList: [checklistId],
      });
    }
    return list;
  }, [fileModuleId, checklistId]);

  return (
    <div
      className={styles['content-right']}
      style={{ marginLeft: isShowSearchFlag ? 362 : isUndefined(isShowSearchFlag) ? 0 : 50 }}
    >
      <Spin spinning={queryDeatilLoading}>
        {/* 右侧上 */}
        <div className={styles['content-right-top']}>
          <Output
            name="srNum"
            tooltip="none"
            renderer={() => {
              const record = detailFormDS && detailFormDS.current;
              const srData = record && record.data ? record.data : {};
              return (
                <div className={styles['right-top']}>
                  {/* 右侧上 的 上半部分 */}
                  <div className={styles['right-top-header']}>
                    <div className={styles['right-top-header-top']}>
                      {/* 左侧标题 */}
                      <div className={styles['right-top-header-top-title-tag']}>
                        <div className={styles['right-top-header-top-title']}>
                          <Tooltip placement="bottomLeft" title={srData.srName}>
                            {srData.srName}
                          </Tooltip>
                        </div>
                        <Tag
                          style={{
                            marginLeft: 8,
                            color:
                              (statusColors[srData.srStatus] &&
                                statusColors[srData.srStatus].fontColor) ||
                              '#000',
                          }}
                          color={
                            (statusColors[srData.srStatus] &&
                              statusColors[srData.srStatus].bgColor) ||
                            '#fff'
                          }
                        >
                          {srData.srStatusMeaning}
                        </Tag>
                      </div>
                      {/* 右侧按钮 */}
                      <div className={styles['right-top-header-top-buttons']}>
                        {!!goDetailPage && (
                          <Button onClick={goDetailPage} color="primary">
                            {intl.get(`hzero.common.button.view`).d('查看')}
                          </Button>
                        )}
                      </div>
                    </div>
                    <div className={styles['right-top-header-content']}>{srData.srNumber}</div>
                  </div>
                  {/* 右侧上 的 下半部分 */}
                  <div className={styles['right-top-bottom']}>
                    <Form dataSet={detailFormDS} columns={3}>
                      <Output name="maintSiteLov" />
                      <Output name="srTypeLov" />
                      <Output name="priorityName" />
                    </Form>
                  </div>
                </div>
              );
            }}
          />
        </div>
        {/* 拒绝原因 */}
        {!!denialReason && (srStatus === 'REJECTED' || srStatus === 'CANCELED') && (
          <div className={styles['content-right-middle']} style={{ marginTop: 10 }}>
            <Output
              dataSet={detailFormDS}
              name="denialReason"
              tooltip="none"
              renderer={({ value }) => {
                return (
                  <div>
                    <div className={styles['right-middle-desc']}>
                      <img src={RefuseTip} alt="" />
                      <span>{intl.get(`${promptCode}.refuseReason`).d('拒绝的原因')}：</span>
                    </div>
                    <div className={styles['right-middle-content']}>{value}</div>
                  </div>
                );
              }}
            />
          </div>
        )}
        {/* 右侧下 */}
        <Collapse
          className={styles['content-right-bottom']}
          bordered={false}
          defaultActiveKey={['A', 'B', 'C', 'D', 'E', 'F']}
        >
          <Collapse.Panel header={intl.get(`${prompt}.panel.B`).d('申请的工作对象')} key="B">
            <div className={styles['apply-obj-cards']}>
              <div className={styles['apply-obj-card-left']}>
                <div className={styles['apply-obj-card-title']}>
                  <span>{intl.get(`${prompt}.view.message.demandSide`).d('需求方')}</span>
                </div>
                <Output
                  dataSet={detailFormDS}
                  name="orgName"
                  tooltip="none"
                  renderer={({ value, record }) => (
                    <>
                      <div className={styles['field-line-one']}>
                        <span>{intl.get(`${promptCode}.org`).d('需求组织')}</span>
                        <span>
                          <Tooltip placement="bottomLeft" title={value}>
                            {value}
                          </Tooltip>
                        </span>
                      </div>
                      {record ? (
                        <div className={styles['field-line-one']}>
                          {record?.get('manuallySpecifyFlag') ? (
                            <>
                              <span>{intl.get(`${promptCode}.contactDesc`).d('联系人')}</span>
                              <span>
                                <Tooltip placement="bottomLeft" title={record?.get('contactDesc')}>
                                  {record?.get('contactDesc')}
                                </Tooltip>
                              </span>
                            </>
                          ) : (
                            <>
                              <span>{intl.get(`${promptCode}.contact`).d('需求方联系人')}</span>
                              <span>
                                <Tooltip placement="bottomLeft" title={record?.get('contactName')}>
                                  {record?.get('contactName')}
                                </Tooltip>
                              </span>
                            </>
                          )}
                        </div>
                      ) : null}
                      <div className={styles['field-line-one']}>
                        <span>{intl.get(`${promptCode}.phone`).d('联系电话')}</span>
                        <span>
                          <Tooltip placement="bottomLeft" title={record?.get('phone')}>
                            {record?.get('phone')}
                          </Tooltip>
                        </span>
                      </div>
                    </>
                  )}
                />
              </div>
              <div className={styles['apply-obj-card-right']}>
                <div className={styles['apply-obj-card-title']}>
                  <span>{intl.get(`${promptCode}.workObject`).d('工作对象')}</span>
                </div>
                <Row>
                  <Col span={12}>
                    <Output
                      dataSet={detailFormDS}
                      name="assetLocationName"
                      tooltip="none"
                      renderer={({ value, record }) => (
                        <>
                          <div className={styles['field-line-one']}>
                            <span>{intl.get(`${promptCode}.asset`).d('设备')}</span>
                            <span>
                              <Tooltip placement="bottomLeft" title={record?.get('descAndLabel')}>
                                {record?.get('descAndLabel')}
                              </Tooltip>
                            </span>
                          </div>
                          <div className={styles['field-line-one']}>
                            <span>{intl.get(`${promptCode}.assetLocation`).d('位置')}</span>
                            <span>
                              <Tooltip placement="bottomLeft" title={value}>
                                {value}
                              </Tooltip>
                            </span>
                          </div>
                          <div className={styles['field-line-one']}>
                            <span>{intl.get(`${promptCode}.locationDesc`).d('位置补充说明')}</span>
                            <span>
                              <Tooltip placement="bottomLeft" title={record?.get('locationDesc')}>
                                {record?.get('locationDesc')}
                              </Tooltip>
                            </span>
                          </div>
                          <div className={styles['field-line-one']}>
                            <span>{intl.get(`${promptCode}.mapSource`).d('地图来源')}</span>
                            <span>
                              <Tooltip
                                placement="bottomLeft"
                                title={record?.get('mapSourceCodeMeaning')}
                              >
                                {record?.get('mapSourceCodeMeaning')}
                              </Tooltip>
                            </span>
                          </div>
                        </>
                      )}
                    />
                  </Col>
                  <Col span={12}>{isShowMapFlag ? <BasicBMap {...mapDetailProps} /> : null}</Col>
                </Row>
              </div>
            </div>
          </Collapse.Panel>
          <Collapse.Panel header={intl.get(`${prompt}.panel.E`).d('故障缺陷信息')} key="E">
            {showFault && (viewFailureRequiredFlag === '1' || faultDate || evalItemId) ? (
              <Form dataSet={detailFormDS} columns={3}>
                <Output name="evalItemLov" />
                {evalHierarchyList.length > 0 ? (
                  <Output name={`${evalHierarchyList[0].code}`} />
                ) : null}
                {evalHierarchyList.length > 1 ? (
                  <Output name={`${evalHierarchyList[1].code}`} />
                ) : null}
                <Output name="faultDate" />
                <Output name="description" />
              </Form>
            ) : (
              <Form dataSet={detailFormDS} columns={3}>
                <Output name="description" />
              </Form>
            )}
          </Collapse.Panel>
          <Collapse.Panel header={intl.get(`${prompt}.panel.C`).d('工作处理安排')} key="C">
            <Form dataSet={detailFormDS} columns={3}>
              <CommonComponent name="plannerGroupName" dataSet={detailFormDS} />
              {/* <CommonComponent name="plannerName" dataSet={detailFormDS} /> */}
            </Form>
          </Collapse.Panel>
          <Collapse.Panel header={intl.get(`${prompt}.panel.D`).d('相关信息')} key="D">
            <Form dataSet={detailFormDS} columns={3}>
              <Output name="reportOrgName" />
              <Output name="reporterLov" />
              <Output name="reportDate" />
              <Output name="sourceTypeCode" />
              <Output name="sourceReference" />
              <Output name="woNum" />
            </Form>
          </Collapse.Panel>
          <Collapse.Panel header={intl.get(`alm.common.attachment`).d('附件')} key="F">
            <UploadPanel
              mode="view"
              employeeId={employeeId}
              fileUploadLogList={fileUploadLogList}
            />
          </Collapse.Panel>
        </Collapse>
      </Spin>
    </div>
  );
}
