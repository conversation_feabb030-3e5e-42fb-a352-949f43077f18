import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const API = `${BASIC.HMES_BASIC}`;
// const API = `/kd-mes-20000`;

export function QueryTemplate() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/switch`,
    method: 'GET',
  };
}
export function Save() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/save`,
    method: 'POST',
  };
}

export function CrucibleRelease() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/point/release`,
    method: 'POST',
  };
}

export function LineSave() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/line/save`,
    method: 'POST',
  };
}

// 点位生成
export function PointGenerate() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/point/generate`,
    method: 'POST',
  };
}

// 查询详情

export function QueryDetail() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/detail/ui`,
    method: 'GET',
  };
}

// 提交

export function Submit() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/submit`,
    method: 'POST',
  };
}

// 废弃

export function Abandon() {
  return {
    url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/abandon`,
    method: 'POST',
  };
}
