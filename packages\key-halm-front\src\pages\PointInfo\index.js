/**
 * 点位
 * @since 2021-09
 * @author: <EMAIL>
 * @copyright Copyright (c) 2021, Hand
 */
import React, { Component } from 'react';
import {
  DataSet,
  Button,
  Table,
  Switch,
  TextField,
  Select,
  Form,
  Lov,
  Icon,
} from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import { enableRender } from 'utils/renderer';
import { Bind } from 'lodash-decorators';
import { openTab } from 'utils/menuTab';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import queryString from 'querystring';
import { observer } from 'mobx-react';
import formatterCollections from 'utils/intl/formatterCollections';
import { HALM_ORI } from 'alm/utils/config';

import getLangs from './Langs';
import { tableDS, queryBarDS } from './Stores/indexDS';
import styles from './index.module.less';

const organizationId = getCurrentOrganizationId();
@formatterCollections({
  code: ['alm.common', 'alm.component', 'aori.point'],
})
@observer
export default class PointInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      expandSearchForm: false, // 更多查询
    };
    this.queryBarDS = new DataSet(queryBarDS());
    this.tableDS = new DataSet(tableDS());
  }

  componentDidMount() {
    this.handleSearch();
  }

  @Bind
  handleAdd() {
    this.tableDS.create({}, 0);
    this.tableDS.setState('lineEditing', true);
  }

  @Bind
  toggleForm() {
    const { expandSearchForm } = this.state;
    this.setState({
      expandSearchForm: !expandSearchForm,
    });
  }

  @Bind
  handleSearch() {
    const queryData = this.queryBarDS?.current?.toData() || {};

    this.tableDS.setQueryParameter('pointName', queryData?.pointName);
    this.tableDS.setQueryParameter('pointCode', queryData?.pointCode);
    this.tableDS.setQueryParameter('pointType', queryData?.pointType);
    this.tableDS.setQueryParameter('pointObjectId', queryData?.pointObjectId);
    this.tableDS.setQueryParameter('maintSiteIds', queryData?.maintSiteId);
    this.tableDS.setQueryParameter('enabledFlag', queryData?.enabledFlag);

    this.tableDS.query();
    this.tableDS.setState('lineEditing', false);
  }

  @Bind
  handleReset() {
    const queryData = this.queryBarDS?.current;
    if (queryData) {
      queryData.reset();
      this.handleSearch();
    }
  }

  @Bind
  handleClearLine(record) {
    this.tableDS.remove(record);
    this.tableDS.setState('lineEditing', false);
  }

  @Bind
  handleSaveLine(record) {
    this.tableDS.submit().then(res => {
      if (res?.content) {
        this.tableDS.query();
      } else if (res === undefined) {
        record.setState('editing', false);
        this.tableDS.setState('lineEditing', false);
      }
    });
  }

  @Bind()
  handleImport() {
    openTab({
      key: `/aori/point/data-import/AORI.CHECK_POINT_IMPORT`,
      title: `${getLangs('POINT_INFO')}${getLangs('IMPORT')}`,
      search: queryString.stringify({
        action: `${getLangs('POINT_INFO')}${getLangs('IMPORT')}`,
      }),
    });
  }

  @Bind
  handleCancelLine(record) {
    record.reset();
    record.setState('editing', false);
    this.tableDS.setState('lineEditing', false);
  }

  @Bind
  handleEditLine(record) {
    record.setState('editing', true);
    this.tableDS.setState('lineEditing', true);
  }

  @Bind
  commands = ({ record, dataSet }) => {
    return record.status === 'add' ? (
      <span className="action-link">
        <a onClick={() => this.handleClearLine(record)}>{getLangs('CLEAR')}</a>
        <a onClick={() => this.handleSaveLine(record)}>{getLangs('SAVE')}</a>
      </span>
    ) : record.getState('editing') ? (
      <span className="action-link">
        <a onClick={() => this.handleCancelLine(record)}>{getLangs('CANCEL')}</a>
        <a onClick={() => this.handleSaveLine(record)}>{getLangs('SAVE')}</a>
      </span>
    ) : (
      <span className="action-link">
        {dataSet.getState('lineEditing') ? (
          <span className={styles['action-link-disabled']}>{getLangs('EDIT')}</span>
        ) : (
          <a onClick={() => this.handleEditLine(record)}>{getLangs('EDIT')}</a>
        )}
      </span>
    );
  };

  get columns() {
    return [
      {
        name: 'pointName',
        editor: record => record.status === 'add' || record.getState('editing'),
      },
      {
        name: 'pointCode',
        editor: record => record.status === 'add' || record.getState('editing'),
      },
      {
        name: 'pointType',
        editor: record => record.status === 'add' || record.getState('editing'),
      },
      {
        name: 'pointObject',
        editor: record => record.status === 'add' || record.getState('editing'),
        width: 420,
      },
      {
        name: 'maintSite',
      },
      {
        name: 'enabledFlag',
        editor: record => (record.status === 'add' || record.getState('editing')) && <Switch />,
        align: 'left',
        renderer: ({ value }) => enableRender(value),
      },
      {
        header: getLangs('OPTION'),
        align: 'left',
        width: 150,
        renderer: this.commands,
      },
    ];
  }

  render() {
    const { expandSearchForm } = this.state;
    return (
      <>
        <Header title={getLangs('POINT_INFO')}>
          <Button
            icon="add"
            color="primary"
            onClick={() => this.handleAdd()}
            disabled={this.tableDS.getState('lineEditing')}
          >
            {getLangs('CREATE')}
          </Button>
          <ExcelExport requestUrl={`${HALM_ORI}/v1/${organizationId}/points/export`} />
          <Button icon="vertical_align_top" onClick={() => this.handleImport()}>
            {getLangs('IMPORT')}
          </Button>
        </Header>
        <Content>
          <div className={styles['query-box']}>
            <div className={styles['query-form']}>
              <Form dataSet={this.queryBarDS} columns={3}>
                <TextField name="pointName" />
                <TextField name="pointCode" />
                <Select name="pointType" />
              </Form>
              {expandSearchForm && (
                <Form dataSet={this.queryBarDS} columns={3}>
                  <Lov name="pointObject" />
                  <Lov name="maintSite" />
                  <Select name="enabledFlag" />
                </Form>
              )}
            </div>
            <div className={styles['query-buttons']}>
              <a onClick={this.toggleForm}>
                {getLangs('MORE')}
                {expandSearchForm ? <Icon type="expand_less" /> : <Icon type="expand_more" />}
              </a>
              <Button onClick={() => this.handleReset()}>{getLangs('RESET')}</Button>
              <Button color="primary" onClick={() => this.handleSearch()}>
                {getLangs('SEARCH')}
              </Button>
            </div>
          </div>
          <Table
            key="PointInfo"
            customizedCode="AORI.POINT_INFO.LIST"
            dataSet={this.tableDS}
            columns={this.columns}
            queryFieldsLimit={3}
          />
        </Content>
      </>
    );
  }
}
