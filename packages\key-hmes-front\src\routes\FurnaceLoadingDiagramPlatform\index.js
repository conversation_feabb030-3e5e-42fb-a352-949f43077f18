import React, {useMemo, useEffect, useState } from 'react';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import { DataSet, Button, Table, Modal, Row, Col, TextField, Form, Lov, Select, Icon } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import {tableDS, crucibleDetailDS} from './stores'
import InputLovDS from './stores/InputLovDS';
import LovModal from './LovModal';
import { Abandon } from './services';

const modelPrompt = 'tarzan.hmes.FurnaceLoadingDiagramPlatform';

const FurnaceLoadingDiagramPlatform = observer(props => {
  const {
    location: { state },
  } = props;


  const { run: abandon, loading: abandonLoading } = useRequest(Abandon(), {
    manual: true,
    needPromise: true,
  });

  const tableDs = useMemo(() => {
    return new DataSet(tableDS());
  }, []);

  const crucibleDetailDs = useMemo(() => {
    return new DataSet(crucibleDetailDS());
  }, []);
  const [expandForm, setExpandForm] = useState(false);
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);

  const handleToTemplate = (record) => {
    props.history.push(`/hmes/furnace-loading-diagram-platform/detail/${record.data.stoveHeadId}`);
  }
  useEffect(() => {
    tableDs.queryDataSet.addEventListener('update', handleUpdate);
    return () => {
      if (tableDs) {
        tableDs.removeEventListener('batchSelect', handleUpdate);
      }
    };
  }, []);

  useEffect(() => {
    if(state?.equipmentCode){
      tableDs.queryDataSet.current.set('equipmentCode', state?.equipmentCode);
      tableDs.query();
    }
  }, [state?.equipmentCode]);

  const handleUpdate = ({name, value}) => {
    if(name === "equipmentCode" && !value){
      tableDs.loadData([])
    }
  }

  const crucibleColumns = [
    {
      name: 'stoveLot',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'stoveNumber',
    },
    {
      name: 'releaseQty',
    },
  ]

  const handlePresentationDetail = (record) => {
    crucibleDetailDs.loadData([]);
    crucibleDetailDs.setQueryParameter('stoveHeadId', record.get('stoveHeadId'));
    crucibleDetailDs.query();
    Modal.open({
      title: intl.get(`${modelPrompt}.crucibleDetail`).d('坩锅明细'),
      key: Modal.key(),
      style: { width: '800px' },
      drawer: true,
      closable: true,
      maskClosable: true,
      okButton: false,
      children: (
        <Table
          customizedCode="crucibleDetail"
          dataSet={crucibleDetailDs}
          columns={crucibleColumns}
        />
      ),
    });
  }

  const columns = [
    {
      name: 'equipmentCode',
      renderer: ({ record }) => (
        <a
          onClick={() => {
            handleToTemplate(record);
          }}
        >
          {record?.get('equipmentCode')}
        </a>
      ),
    },
    {
      name: 'stoveCount',
    },
    {
      name: 'templateName',
    },
    {
      name: 'stoveCode',
    },
    {
      name: 'operationName',
    },
    {
      name: 'statusMeaning',
    },
    {
      name: 'chargingDate',
    },
    {
      name: 'dischargingDate',
    },
    {
      name: 'chargingLayer',
    },
    {
      name: 'chargingMethodDesc',
    },
    {
      name: 'sumNumber',
    },
    {
      name: 'sumWeight',
    },
    {
      name: 'userName',
    },
    { name: 'releaseFlag', width: 150 },
    { name: 'releaseQty', width: 150 },
    {
      title: intl.get(`${modelPrompt}.option`).d('操作'),
      lock: 'right',
      width: 180,
      align: 'center',
      renderer: ({ record }) => (
        <>
          <a
            // style={{ marginLeft: '12px' }}
            onClick={() => {
              handlePresentationDetail(record);
            }}
          >
            {intl.get(`${modelPrompt}.details`).d('坩锅明细')}
          </a>
        </>
      ),
    },
  ]

  const handleAdd = () => {
    props.history.push(`/hmes/furnace-loading-diagram-platform/detail/create`);
  }

  const handleAbandon = async () => {
    const res = await abandon({
      params: tableDs.selected.map(item => item.get('stoveHeadId')),
    })
    if(res&&res.success){
      tableDs.query()
      notification.success({
        message: '操作成功！',
      })
    }
  }

  const toggleForm = () => {
    setExpandForm(!expandForm);
  }

  const handleSearch = () => {
    tableDs.query();
  }

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet.current.getField('code').set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet.current.set('code', '');
      inputLovDS.data = [];
      handleSearch()
    }
  }

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  const renderQueryBar = ({ buttons, queryDataSet, queryFields, dataSet }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <TextField name="equipmentCode" />
              <Lov name="templeLov" />
              <TextField name="stoveCode"/>
              {expandForm && (
                <>
                  <TextField
                    name="stoveLots"
                    suffix={
                      <div className="c7n-pro-select-suffix">
                        <Icon
                          type="search"
                          onClick={() => onOpenInputModal(true, 'stoveLots', intl.get(`${modelPrompt}.stoveLotLov`).d('坩埚批次'), queryDataSet)}
                        />
                      </div>
                    }
                  />
                  <Select name="stoveMaterialCodes" />
                  <Select name="statusList" />
                  <Select name="releaseFlag" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div
              style={{
                flexShrink: 0,
                display: 'flex',
                alignItems: 'center',
                marginTop: '7px',
              }}
            >
              <Button funcType="link" icon={
                expandForm? 'expand_less':'expand_more'
              } onClick={toggleForm}>
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              <Button dataSet={null} onClick={handleSearch} color="primary">
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };

  return (
    <div className='hmes-style'>
      <Header
        title={intl
          .get(`${modelPrompt}.title`)
          .d('装炉图平台')}
      >
        <Button
          onClick={handleAdd}
          color="primary"
        >
          {intl.get('tarzan.aps.common.button.add').d('新建')}
        </Button>
        <Button
          onClick={handleAbandon}
          color="primary"

          disabled={abandonLoading|| tableDs.selected.length === 0 || tableDs.selected.some(item => item.get('status') === 'COMPLETED'||item.get('status') === 'DISUSED')}
        >
          {intl.get('tarzan.aps.common.button.abandon').d('废弃')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={renderQueryBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="FurnaceLoadingDiagramPlatform"
          customizedCode="FurnaceLoadingDiagramPlatform"
        />
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.FurnaceLoadingDiagramPlatform', 'tarzan.common'],
})(FurnaceLoadingDiagramPlatform);
