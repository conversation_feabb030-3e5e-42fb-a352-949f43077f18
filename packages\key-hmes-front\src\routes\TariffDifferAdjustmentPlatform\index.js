/**
 * @Description:  电费差异调整平台-入口页
 */
import React, { useEffect, useState } from 'react';
import { DataSet, Table, Button, Spin } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { isNil, flow } from 'lodash';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import notification from 'utils/notification';
import withProps from 'utils/withProps';
import ExcelExport from 'components/ExcelExport';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { API_HOST, BASIC } from '@utils/config';
import { observer } from 'mobx-react-lite';
import { tableDS } from './stores/ListDS';

const modelPrompt = 'tarzan.tariffDifferAdjustmentPlatform';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

const TariffDifferAdjustmentPlatform = props => {
  const { tableDs, customizeTable } = props;

  const [loading, setLoading] = useState(false);

  // DS事件监听
  useEffect(() => {
    // listener(true);
    // return function clean() {
    //   listener(false);
    // };
  });

  // 头列表配置
  const columns = [
    {
      name: 'workOrderNum',
      align: 'center',
    },
    {
      name: 'qty',
      align: 'center',
    },
    { name: 'sumQty', width: 150, align: 'center'},
    {
      name: 'materialName',
      align: 'center',
    },
    {
      name: 'month',
      align: 'center',
    },
    { name: 'powerStartDate', width: 180, align: 'center'},
    { name: 'powerEndDate', width: 180, align: 'center'},
    {
      name: 'equipmentCode',
      align: 'center',
    },
    {
      name: 'equipmentName',
      align: 'center',
    },
    {
      name: 'workcellCode',
      align: 'center',
    },
    {
      name: 'workcellName',
      align: 'center',
    },
    {
      name: 'stoveCode',
      align: 'center',
    },
    {
      name: 'chargingDate',
      align: 'center',
    },
    {
      name: 'dischargingDate',
      align: 'center',
    },
    {
      name: 'sumNumber',
      align: 'center',
    },
    {
      name: 'sumWeight',
      align: 'center',
    },
    {
      name: 'stoveCount',
      align: 'center',
    },
    {
      name: 'tipWoValue',
      align: 'center',
    },
    {
      name: 'peakWoValue',
      align: 'center',
    },
    {
      name: 'valleyWoValue',
      align: 'center',
    },
    {
      name: 'levelWoValue',
      align: 'center',
    },
    {
      name: 'planElectricity',
      align: 'center',
    },
    {
      name: 'actualElectricity',
      align: 'center',
    },
    {
      name: 'priceDifference',
      align: 'center',
    },
    {
      name: 'difStatusMeaning',
      align: 'center',
    },
    {
      name: 'difMessage',
      align: 'center',
    },
  ];

  const onFieldEnterDown = () => {
    tableDs.query(props.tableDs.currentPage);
  };

  const handleActual = async () => {
    if (tableDs.selected.length === 0) {
      notification.warning({
        message: intl.get(`${modelPrompt}.notification.need.chooseone`).d('请至少勾选一条数据'),
      });
      return;
    }
    const data = tableDs.selected.map(i => i.toData());
    setLoading(true);
    const res = await request(`${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-electric-price-sum-ifaces/actual/electricity/calculate`, {
      method: 'POST',
      body: data,
    })
    if (res && res.failed) {
      setLoading(false);
      notification.error({ message: res.message });
    } else {
      notification.success();
      tableDs.query();
      setLoading(false);
    }
  }

  // const handleDiffer = () => {
  //   if (tableDs.selected.length === 0) {
  //     notification.warning({
  //       message: intl.get(`${modelPrompt}.notification.need.chooseone`).d('请至少勾选一条数据'),
  //     });
  //     return;
  //   }
  //   const data = tableDs.selected.map(i => i.toData());
  //   setLoading(true);
  //   request(`${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-electric-price-sum-ifaces/price/difference/calculate`, {
  //     method: 'POST',
  //     body: data,
  //   }).then(res => {
  //     setLoading(false);
  //     if (res && res.failed) {
  //       notification.error({ message: res.message });
  //     } else {
  //       notification.success();
  //       tableDs.query();
  //     }
  //   });
  // }

  const handleReturn = async () => {
    if (tableDs.selected.length === 0) {
      notification.warning({
        message: intl.get(`${modelPrompt}.notification.need.chooseone`).d('请至少勾选一条数据'),
      });
      return;
    }
    const data = tableDs.selected.map(i => i.toData());
    const res = await request(`${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-electric-price-sum-ifaces/return/price/difference`, {
      method: 'POST',
      body: data,
    })
    if (res && res.failed) {
      notification.error({ message: res.message });
    } else {
      notification.success();
      tableDs.query();
    }
  }

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const idList = tableDs.selected.map(v => v.data.electricPriceSumIfaceId);
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return { ...queryParmas, idList };
  };

  return (
    <div className="hmes-style">
      <Spin spinning={loading}>
        <Header title={intl.get(`${modelPrompt}.title`).d('电费差异调整平台')}>
          <Button disabled={tableDs.selected.length === 0||tableDs.selected.some(e => e.get("difStatus") === "S")} onClick={() => handleActual()} style={{ marginRight: 15 }} color="primary">
            {intl.get(`${modelPrompt}.button.differActual`).d('差异计算')}
          </Button>
          <Button loading={loading} onClick={() => handleReturn()} style={{ marginRight: 15 }} color="primary" disabled={tableDs.selected.length === 0||tableDs.selected.some(e => e.get("difStatusMeaning") === "是")}>
            {intl.get(`${modelPrompt}.button.return`).d('回传差异')}
          </Button>
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/hme-electric-price-sum-ifaces/export/ui`}
            queryParams={getExportQueryParams}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          />
        </Header>
        <Content>
          {customizeTable(
            {
              filterCode: `${BASIC.CUSZ_CODE_BEFORE}.Tariff_Differ_Adjustment_Platform_LIST.QUERY`,
              code: `${BASIC.CUSZ_CODE_BEFORE}.Tariff_Differ_Adjustment_Platform_LIST.HEAD`,
            },
            <Table
              searchCode="dfcytzpt"
              customizedCode="dfcytzpt"
              dataSet={tableDs}
              columns={columns}
              highLightRow
              queryBar="filterBar"
              queryBarProps={{
                fuzzyQuery: false,
                autoQuery: false,
                onFieldEnterDown,
              }}
            />,
          )}
        </Content>
      </Spin>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.tariffDifferAdjustmentPlatform', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({ ...tableDS() });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.Tariff_Differ_Adjustment_Platform_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.Tariff_Differ_Adjustment_Platform_LIST.HEAD`,
    ],
  }),
)(observer(TariffDifferAdjustmentPlatform));
