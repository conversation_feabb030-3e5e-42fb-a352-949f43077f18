import React, { useEffect, useRef, useState, useMemo } from 'react';
import { DataSet, Select, YearPicker,MonthPicker,DateTimePicker } from 'choerodon-ui/pro';
import { ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { observer } from 'mobx-react';
import DashboardCard from '../DashboardCard.jsx';
import styles from '../../index.module.less';
import Chart from './chart';
import {filterDS}  from '../../Stores';

const PassRate = ({timers}) => {
  const filterDs = useMemo(() => new DataSet(filterDS()), []);
  const [timeDimension, setTimeDimension] = useState<any>();
  const [filterData, setFilterData] = useState<any>();
  const filterDataRef= useRef<any>();
  
  const filterDs2 = useMemo(() => new DataSet(filterDS()), []);
  const [timeDimension2, setTimeDimension2] = useState<any>();
  const [filterData2, setFilterData2] = useState<any>();
  const filterDataRef2= useRef<any>();

  useEffect(() => {
    const time = setInterval(() => {
      setQueryParam();
      setQueryParam2();
    }, (timers)*60000)
    filterDs.addEventListener('update', handleUpdate);
    filterDs2.addEventListener('update', handleUpdate2);
    return () => {
      filterDs.removeEventListener('update', handleUpdate);
      filterDs2.removeEventListener('update', handleUpdate2);
      clearTimeout(time)
    };
  }, [timers]);
  

  const handleUpdate =  ({record, name,value,oldValue}) => {
    if(name === 'timeDimension' && value && value!==oldValue){
      record.set('timeDimension',value);
      record.set('timeDimensionMeaning',null);
      record.set('year',null);
      record.set('month',null);
      record.set('date',null);
      record.set('season',null);
      record.set('week',null);
    }
    if((name === 'year'||name === 'month'||name === 'date' ||name === 'season'|| name==="week") && value && value!==oldValue){
      setQueryParam();    
    }
  }
  const handleUpdate2 = ({record, name,value,oldValue}) => {
    if(name === 'timeDimension' && value && value!==oldValue){
      record.set('timeDimension',value);
      record.set('timeDimensionMeaning',null);
      record.set('year',null);
      record.set('month',null);
      record.set('date',null);
      record.set('season',null);
      record.set('week',null);
    }
    if((name === 'year'||name === 'month'||name === 'date' ||name === 'season'|| name==="week") && value && value!==oldValue){
      setQueryParam2();
    }
  }
  const setQueryParam =()=>{
    setFilterData(filterDs.toData()[0]);
    const {timeDimension, timeDimensionMeaning, year, month, date,season,week} = filterDs.toData()[0];
    const params ={
      timeDimension: timeDimensionMeaning|| (filterDs?.current?.getField('timeDimension')?.getLookupData(timeDimension)?.meaning)?.charAt(0),
      year,
      date: timeDimension === '1'? year : timeDimension === '2' ? month:
        timeDimension === '3' ?date:timeDimension === '4'?season: `${`${month}/${week}`}`,
    };
    filterDataRef.current=params;
  }
  const setQueryParam2 =()=>{
    setFilterData2(filterDs2.toData()[0]);
    const {timeDimension,timeDimensionMeaning, year, month, date,season,week} = filterDs2.toData()[0];
    const params ={
      timeDimension: timeDimensionMeaning||(filterDs2?.current?.getField('timeDimension')?.getLookupData(timeDimension)?.meaning)?.charAt(0),
      year,
      date: timeDimension === '1'? year : timeDimension === '2' ? month:
        timeDimension === '3' ?date:timeDimension=== '4'?season: `${`${month}/${week}`}`,
    };      
    filterDataRef2.current=params;
  }

  const onFilterChange = (e) => {
    setTimeDimension(e);
  };

  const onFilterChange2 = (e) => {
    setTimeDimension2(e);
  };

  return (
    <DashboardCard height="100%">
      <div style={{ width: '100%', height: '100%', display: 'flex' }}>
        <div className={styles['my-bottom-chart']}> 
          <div className={styles['my-bottom-chart-title']}>
           粉碎工序不良分布图
          </div>
          <div className={styles['my-chart-filter']}>
            <div className={styles['container-inventory-select']}>
              <Select
                dataSet={filterDs}
                style={{backgroundColor: 'rgba(0,0,0,0)'}}
                name="timeDimension"
                onChange={onFilterChange}
                showValidation={ShowValidation.tooltip}
              />
              {timeDimension === '1' ? (
                <YearPicker name="year" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
              ) : timeDimension === '2' ? (
                <MonthPicker name="month" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
              ) : timeDimension === '3' ? (
                <DateTimePicker name="date"  dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                
              ) : timeDimension === '4' ? (
                <><YearPicker name="year" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                  <Select name="season" 
                    style={{backgroundColor: 'rgba(0,0,0,0)'}}
                    dataSet={filterDs} />
                </>
              ):
                <><MonthPicker name="month" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                  <Select name="week" 
                    style={{backgroundColor: 'rgba(0,0,0,0)'}}
                    dataSet={filterDs} />
                </>
              }
            </div>
          </div>
          <div className={styles['my-chart']}>
            {filterData && <Chart filterDataRef={filterDataRef} operation='FS'/>}
          </div>
        </div>
        <div className={styles['my-bottom-center-chart']}> 
          <div className={styles['my-bottom-chart-title']}>
           热包工序不良分布图
          </div>
          <div className={styles['my-chart-filter']}>
            <div className={styles['container-inventory-select']}>
              <Select
                dataSet={filterDs2}
                style={{backgroundColor: 'rgba(0,0,0,0)'}}
                name="timeDimension"
                onChange={onFilterChange2}
              />
              {timeDimension2 === '1' ? (
                <YearPicker name="year" dataSet={filterDs2} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
              ) : timeDimension2 === '2' ? (
                <MonthPicker name="month" dataSet={filterDs2} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
              ) : timeDimension2 === '3' ? (
                <DateTimePicker name="date"  dataSet={filterDs2} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                
              ) : timeDimension2 === '4' ? (
                <><YearPicker name="year" dataSet={filterDs2} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                  <Select name="season" 
                    style={{backgroundColor: 'rgba(0,0,0,0)'}}
                    dataSet={filterDs2} />
                </>
              ):
                <><MonthPicker name="month" dataSet={filterDs2} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                  <Select name="week" 
                    style={{backgroundColor: 'rgba(0,0,0,0)'}}
                    dataSet={filterDs2} />
                </>
              }
            </div>
          </div>
          {filterData2 && <div className={styles['my-chart']}>
            <Chart filterDataRef={filterDataRef2} operation='RB'/>
          </div>
          }
        </div>
      </div>
    </DashboardCard>
  );
};
export default observer(PassRate);
