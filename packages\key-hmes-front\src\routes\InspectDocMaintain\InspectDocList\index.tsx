/**
 * @Description: 检验单维护-主界面
 * @Author: <EMAIL>
 * @Date: 2023/2/13 10:43
 */
import React, { FC, useCallback, useMemo, useState, useEffect } from 'react';
import { RouteComponentProps } from 'react-router'; // 使用history与match的需引入，并将组件继承至RouteComponentProps
import { Badge, Collapse, Tag, Upload } from 'choerodon-ui';
import { Button, DataSet, Modal, Table, Dropdown, Menu, Spin } from 'choerodon-ui/pro';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import axios from 'axios';
import { observer } from 'mobx-react';
import { drawerDS, headDS, lineDS } from '../stores';
import { CancelInspectDoc, CopyInspectDoc, RestartInspectDoc } from '../services';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.hwms.inspectDocMaintain';
const { Panel } = Collapse;

interface InspectDocListProps extends RouteComponentProps {
  headDs: any;
  lineDs: DataSet;
  customizeTable: any;
}

const InspectDocList: FC<InspectDocListProps> = props => {
  const {
    match: { path },
    headDs,
    lineDs,
    history,
    customizeTable,
  } = props;
  const drawerTableDs = useMemo(() => new DataSet(drawerDS()), []);
  const [selectDocIds, setSelectDocIds] = useState([]);
  const [loading, setLoading] = useState(false);
  const [cancelDisabledFlag, setCancelDisabledFlag] = useState<boolean>(true); // 取消按钮禁用状态
  const [copyDisabledFlag, setCopyDisabledFlag] = useState<boolean>(true); // 取消按钮禁用状态
  const [exportDisabled, setExportDisabled] = useState<boolean>(true); // 取消按钮禁用状态
  const [restartDisabled, setRestartDisabledFlag] = useState<boolean>(true); // 重启按钮禁用状态
  const [statusFlag, setStatusFlag] = useState<boolean>(true); // 状态变更按钮


  const { run: cancelInspectDoc, loading: cancelLoading } = useRequest(CancelInspectDoc(), {
    manual: true,
  });
  const { run: copyInspectDoc, loading: copyLoading } = useRequest(CopyInspectDoc(), {
    manual: true,
  });
  const { run: restartInspectDoc, loading: restartLoading } = useRequest(RestartInspectDoc(), {
    manual: true,
  });

  useEffect(() => {
    headDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.LIST`,
    );
    headDs.query(headDs.currentPage).then(r => r);
  }, []);

  useDataSetEvent(headDs.queryDataSet, 'update', ({ name, record }) => {
    if (name === 'siteLov') {
      record?.init('inspectBusinessType', undefined);
    }
  });

  useDataSetEvent(headDs, 'selectAll', () => {
    eventStatus()
  });

  useDataSetEvent(headDs, 'unSelectAll', () => {
    eventStatus()
  });

  useDataSetEvent(headDs, 'unselect', () => {
    eventStatus()
  });

  useDataSetEvent(headDs, 'select', () => {
    eventStatus()
  });

  const eventStatus = () => {
    if (headDs.selected.length > 0) {
      const flag = headDs.selected.every(item => item.get('inspectDocStatus') === 'LAST_COMPLETED' )
      setStatusFlag(!flag)
    }else{
      setStatusFlag(true)
    }
  }

  const handleUpdateSelect = () => {
    let _cancelDisabledFlag = !headDs.selected?.length;
    let _copyDisabledFlag = headDs.selected?.length !== 1;
    let _restartDisabledFlag = !headDs.selected?.length;
    const _selectDocIds: any = [];
    headDs.selected.forEach(_record => {
      _selectDocIds.push(_record.get('inspectDocId'));
      if (!['NEW', 'RELEASED', 'INSPECTING'].includes(_record.get('inspectDocStatus'))) {
        _cancelDisabledFlag = true;
      }
      if (['NEW', 'CANCEL'].includes(_record.get('inspectDocStatus'))) {
        _copyDisabledFlag = true;
      }
      if (!['CANCEL'].includes(_record.get('inspectDocStatus'))) {
        _restartDisabledFlag = true;
      }
    });
    setSelectDocIds(_selectDocIds);
    setCancelDisabledFlag(_cancelDisabledFlag);
    setCopyDisabledFlag(_copyDisabledFlag);
    setRestartDisabledFlag(_restartDisabledFlag);

    if (headDs.selected?.length) {
      const inspectDocStatus = headDs.selected
        .map(record => record?.get('inspectDocStatus'))
        .every(val => val === 'LAST_COMPLETED');
      const inspectBusinessType = headDs.selected
        .map(record => record?.get('inspectBusinessType'))
        .every(val => val.includes('IQC'));
      if (inspectDocStatus && inspectBusinessType) {
        setExportDisabled(false);
      } else {
        setExportDisabled(true);
      }
    } else {
      setExportDisabled(true);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    if(headDs.selected.length===0){
      setStatusFlag(true)
    }
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      queryLineTable(dataSet?.current.get('inspectDocId'));
    } else {
      queryLineTable(null);
    }
    handleUpdateSelect();
  };

  useDataSetEvent(headDs, 'batchSelect', handleUpdateSelect);
  useDataSetEvent(headDs, 'batchUnSelect', handleUpdateSelect);
  useDataSetEvent(headDs, 'load', resetHeaderDetail);

  const queryLineTable = inspectDocId => {
    lineDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.TASK_LIST`,
    );
    if (inspectDocId) {
      lineDs.setQueryParameter('inspectDocId', inspectDocId);
    } else {
      lineDs.setQueryParameter('inspectDocId', 0);
    }
    lineDs.query();
  };

  const handleCancelInspectDoc = () => {
    cancelInspectDoc({
      params: selectDocIds,
      onSuccess: () => {
        notification.success({});
        headDs.query(headDs.currentPage);
      },
    });
  };

  const handleRestartInspectDoc = () => {
    restartInspectDoc({
      params: selectDocIds,
      onSuccess: () => {
        notification.success({});
        headDs.query(headDs.currentPage);
      },
    });
  };

  const handleCopyInspectDoc = () => {
    copyInspectDoc({
      params: { inspectDocId: selectDocIds[0] },
      onSuccess: res => {
        notification.success({});
        history.push(`/hwms/inspect-doc-maintain/dist/${res?.inspectDocId}`);
      },
    });
  };

  const handleOpenDrawer = inspectDocId => {
    drawerTableDs.setQueryParameter('inspectDocId', inspectDocId);
    drawerTableDs.query();
    Modal.open({
      ...drawerPropsC7n({
        ds: drawerTableDs,
        canEdit: false,
      }),
      title: intl.get(`${modelPrompt}.title.inspectObjectDetail`).d('报检明细'),
      destroyOnClose: true,
      style: {
        width: 1080,
      },
      children: (
        <Table
          dataSet={drawerTableDs}
          columns={detailColumns}
          customizedCode="inspectDocMaintain-drawer"
        />
      ),
    });
  };

  const renderTag = (value, record) => {
    switch (record.get('inspectDocStatus')) {
      case 'NEW':
      case 'RELEASED':
        return <Tag color="green">{value}</Tag>;
      case 'INSPECTING':
      case 'REINSPECTING':
        return <Tag color="yellow">{value}</Tag>;
      case 'LAST_COMPLETED':
        return <Tag color="geekblue">{value}</Tag>;
      case 'FIRST_COMPLETED':
        return <Tag color="blue">{value}</Tag>;
      default:
        return <Tag color="gray">{value}</Tag>;
    }
  };

  const detailColumns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'sequence', width: 100 },
      { name: 'objectTypeDesc', width: 120 },
      { name: 'objectCode', width: 200 },
      { name: 'quantity', width: 120 },
      { name: 'uomName', width: 100 },
      { name: 'locatorName' },
      { name: 'lot' },
      { name: 'supplierLot' },
      { name: 'inspectInfoCode', width: 120 },
    ];
  }, []);

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'inspectDocNum',
        lock: ColumnLock.left,
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hwms/inspect-doc-maintain/dist/${record!.get('inspectDocId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'inspectDocStatusDesc',
        renderer: ({ value, record }) => renderTag(value, record),
      },
      { name: 'siteName' },
      { name: 'inspectBusinessTypeDesc', width: 150 },
      {
        name: 'urgentFlag',
        align: ColumnAlign.center,
        width: 100,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('urgentFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('urgentFlag') === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      { name: 'inspectSchemeCode', width: 150 },
      { name: 'sourceObjectCode'},
      { name: 'quantity'},
      { name: 'actualQty'},
      { name: 'notActualQty'},
      { name: 'docCreateMethodDesc', width: 150 },
      { name: 'firstInspectResultDesc' },
      { name: 'lastInspectResultDesc' },
      {
        name: 'inspectInfoCodes',
        width: 180,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push({
                  pathname: `/hwms/inspection-info-management/list`,
                  state: {
                    inspectInfoCodes: record?.get('inspectInfoCodes'),
                  },
                });
              }}
            >
              {value?.join(',')}
            </a>
          );
        },
      },
      {
        name: 'inspectInfoUserNames',
        width: 180,
        renderer: ({ value }) => {
          if (value && value.length) {
            const set = new Set(value);
            return Array.from(set).join(',');
          }
          return null;
        },
      },
      { name: 'creationDate', width: 150, align: ColumnAlign.center },
      { name: 'actualStartTime', width: 150, align: ColumnAlign.center },
      { name: 'actualEndTime', width: 150, align: ColumnAlign.center },
      { name: 'reviewStatusDesc' },
      { name: 'reviewUserName' },
      { name: 'reviewTime', width: 150, align: ColumnAlign.center },
      { name: 'materialName' },
      { name: 'revisionCode' },
      { name: 'customerName' },
      { name: 'supplierName' },
      { name: 'processWorkcellName' },
      { name: 'stationWorkcellName' },
      { name: 'operationName' },
      { name: 'areaName' },
      { name: 'prodLineName' },
      { name: 'locatorName' },
      { name: 'productionBatch' },
      { name: 'equipmentName' },
      { name: 'stoveCode' },
      { name: 'stoveLayerMeaning' },
      { name: 'sourceEo' },
      { name: 'stoveCount' },
      { name: 'inspectObjectNum' },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 150,
        renderer: ({ record }) => (
          <a onClick={() => handleOpenDrawer(record!.get('inspectDocId'))}>
            {intl.get(`${modelPrompt}.action.inspectObjectDetail`).d('报检明细')}
          </a>
        ),
      },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'inspectTaskCode',
        width: 150,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                history.push(`/hwms/inspection-platform/dist/${record?.get('inspectTaskId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      { name: 'inspectTaskStatusDesc', width: 120 },
      { name: 'inspectTaskTypeDesc', width: 120 },
      { name: 'inspectResult' },
      { name: 'inspectorName' },
      {
        name: 'sourceTaskId',
        width: 150,
        align: ColumnAlign.center,
        renderer: ({ record }) => (
          <Badge
            status={record?.get('sourceTaskId') > 0 ? 'success' : 'error'}
            text={
              record?.get('sourceTaskId') > 0
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
      { name: 'outsourceSupplierName' },
      { name: 'actualStartTime', width: 150, align: ColumnAlign.center },
      { name: 'actualEndTime', width: 150, align: ColumnAlign.center },
      { name: 'scrapQty' },
      { name: 'okQty' },
      { name: 'ngQty' },
      { name: 'sourceTaskCode' },
    ];
  }, []);

  const handleAdd = useCallback(() => {
    history.push(`/hwms/inspect-doc-maintain/dist/create`);
  }, []);

  const headerRowClick = record => {
    queryLineTable(record?.get('inspectDocId'));
  };

  const downloadField = () => {
    setLoading(true);
    axios({
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc-value-import/import/template/download`,
      method: 'get',
      params: { inspectDocIds: headDs.selected.map(record => record.get('inspectDocId')) },
      responseType: 'blob',
    })
      .then((res: any) => {
        // 判断是否有报错
        if (res.type === 'application/json') {
          const fileReader = new FileReader();
          fileReader.onloadend = () => {
            // @ts-ignore
            const jsonData = JSON.parse(fileReader.result);

            // 普通对象，读取信息
            if (!jsonData?.success) {
              notification.error({ description: jsonData?.message });
            } else {
              try {
                getResponse(jsonData);
              } catch (err) {
                console.log(err);
              }
            }
          };
          fileReader.readAsText(res);
        } else {
          const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
          const a = document.createElement('a');
          const url = window.URL.createObjectURL(blob);
          a.href = url;
          a.download = decodeURI('检验值导入模板.xlsx');
          a.style.display = 'none';
          document.body.appendChild(a);
          a.click();
          a.parentNode?.removeChild(a);
          window.URL.revokeObjectURL(url);
        }
        setLoading(false);
      })
      .catch(err => {
        console.log(err);
        setLoading(false);
      });
  };

  const uploadFile = formData => {
    axios({
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc-value-import/excel/upload/for/ui`,
      method: 'post',
      headers: { 'content-type': 'multipart/form-data' },
      data: formData,
    })
      .then((res: any) => {
        console.log(res, 'uploadFile');
        setLoading(false);
        if (res?.success && res?.rows) {

          checkUploadResult(res.rows);
        } else if (res?.message) {
          notification.error({ message: res.message });
          setLoading(false);
        } else {
          notification.error({});
          setLoading(false);
        }
      })
      .catch(err => {
        console.log(err, 'uploadFile');
        setLoading(false);
      });
  };

  const checkUploadResult = id => {
    axios({
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc-value-import/validate/result/select/for/ui`,
      method: 'get',
      params: { importId: id },
    })
      .then((res: any) => {
        console.log(res, 'checkUploadResult');
        if (res?.success) {
          if (res?.rows?.batchStatus !== 'VALID_FAILED') {
            axios({
              url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc-value-import/data/save/for/ui`,
              method: 'post',
              data: {
                importId: id,
              },
            })
              .then((subRes: any) => {
                console.log(subRes, 'checkUploadResult subRes');
                if (subRes?.success) {
                  headDs.query(headDs.currentPage);
                  notification.success({});
                  setLoading(false);
                } else {
                  setLoading(false);
                  notification.error({ message: subRes.message || '导入失败' });
                }
              })
              .catch(err => {
                console.log(err, 'checkUploadResult subRes err');
                setLoading(false);
              });
          } else {
            setLoading(false);
            if (res?.rows?.validateDataList?.length > 0) {
              Modal.open({
                title: '校验失败',
                key: Modal.key(),
                maskClosable: true, // 点击蒙层是否允许关闭
                keyboardClosable: true, // 按 esc 键是否允许关闭
                destroyOnClose: true, // 关闭时是否销毁
                style: {
                  width: 720,
                },
                okButton: false,
                cancelText: '关闭',
                children: (
                  <>
                    {res?.rows?.validateDataList?.map((validateItem, index) => (
                      <p>
                        {index + 1}.{validateItem.errorMsg}
                      </p>
                    ))}
                  </>
                ),
              });
            } else {
              setLoading(false);
              notification.error({ message: res.message || '校验失败' });
            }
          }
        } else {
          setLoading(false);
          notification.error({ message: res.message || '校验失败' });
        }
      })
      .catch(err => {
        console.log(err, 'checkUploadResult res');
        setLoading(false);
      });
  };

  function handleBeforeUpload(file) {
    const FileSizeLimit = 1024 * 1024 * 10;
    if (file.size > FileSizeLimit) {
      notification.warning({
        message: `文件不能超过10M`,
      });
      return false;
    }
    setLoading(true);
    console.log(file, 'file');
    const formData = new FormData();
    formData.append('file', file, file.name);
    uploadFile(formData);

    return false;
  }

  const ImportBtn = observer(({ ds }) => {
    let downloadAuth = false
    if (!(ds?.selected?.length !== 0)) {
      downloadAuth = true
    } else {
      ds?.selected?.forEach(record => {
        if (['CANCEL'].includes(record.get('inspectDocStatus'))) {
          downloadAuth = true
        }
      })
    }
    return (
      <Dropdown
        overlay={
          <Menu>
            <Menu.Item>
              <Button
                loading={loading}
                funcType={FuncType.flat}
                color={ButtonColor.default}
                disabled={downloadAuth}
                onClick={downloadField}
              >
                下载模板
              </Button>
            </Menu.Item>
            <Menu.Item>
              <Upload accept=".xlsx" multiple={false} beforeUpload={handleBeforeUpload}>
                <Button loading={loading} funcType={FuncType.flat} color={ButtonColor.default}>
                  上传数据
                </Button>
              </Upload>
            </Menu.Item>
          </Menu>
        }
      >
        <Button loading={loading} icon="arrow_drop_down">
          导入
        </Button>
      </Dropdown>
    );
  });

  const handleExport = () => {
    axios({
      url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/incoming-report/export/ui`,
      method: 'POST',
      responseType: 'blob',
      data:  headDs.selected.map(record => record.get('inspectDocId')),
    }).then((res:any) => {
      if (res.type === 'application/json') {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          // @ts-ignore
          const jsonData = JSON.parse(fileReader.result);
          // 普通对象，读取信息
          if (!jsonData?.success) {
            notification.error({ description: jsonData?.message });
          } else {
            try {
              getResponse(jsonData);
            } catch (err) {
              console.log(err);
            }
          }
        };
        fileReader.readAsText(res);
      } else {
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
        const a = document.createElement('a');
        const url = window.URL.createObjectURL(blob);
        a.href = url;
        a.download = decodeURI('来料检验报告.xlsx');
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        a.parentNode?.removeChild(a);
        window.URL.revokeObjectURL(url);
      }
    })
  }

  const handleStatus=async()=>{
    const url=`${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/status/change`
    const ids=headDs.selected.map(item=>item.get('inspectDocId'))
    const res:any=await axios.post(url,ids)
    console.log(res)
    if(res&&res.success){
      notification.success({})
      headDs.query(headDs.currentPage);
    }else{
      notification.error({
        message:res.message,
      })
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('检验单管理')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          permissionList={[
            {
              code: `list.button.cancel`,
              type: 'button',
              meaning: '列表页-取消按钮',
            },
          ]}
          onClick={handleCancelInspectDoc}
          disabled={cancelDisabledFlag}
          loading={cancelLoading}
        >
          {intl.get('tarzan.common.button.cancel').d('取消')}
        </PermissionButton>
        <Button onClick={handleCopyInspectDoc} disabled={copyDisabledFlag} loading={copyLoading}>
          {intl.get('tarzan.common.button.copy').d('复制')}
        </Button>
        <ImportBtn ds={headDs} />
        <Button disabled={exportDisabled} onClick={handleExport}>{intl.get(`${modelPrompt}.iqcExport`).d('来料检报告导出')}</Button>
        <PermissionButton
          permissionList={[
            {
              code: `${path}.button.status`,
              type: 'button',
              meaning: '列表页-状态变更',
            },
          ]}
          onClick={handleStatus}
          disabled={statusFlag}
        >
          {intl.get(`${modelPrompt}.title.status`).d('状态变更')}
        </PermissionButton>
        <PermissionButton
          permissionList={[
            {
              code: `list.button.restart`,
              type: 'button',
              meaning: '列表页-重启按钮按钮',
            },
          ]}
          onClick={handleRestartInspectDoc}
          disabled={restartDisabled}
          loading={restartLoading}
        >
          {intl.get('tarzan.common.button.restart').d('重启按钮')}
        </PermissionButton>
      </Header>
      <Content>
        <Spin spinning={loading}>
          {customizeTable(
            {
              filterCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.QUERY`,
              code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.LIST`,
            },
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              dataSet={headDs}
              columns={headColumns}
              searchCode="inspectDocMaintain1"
              customizedCode="inspectDocMaintain-listHeader"
              onRow={({ record }) => ({
                onClick: () => headerRowClick(record),
              })}
            />,
          )}
          <Collapse bordered={false} defaultActiveKey={['taskInfo']}>
            <Panel
              key="taskInfo"
              header={intl.get(`${modelPrompt}.title.taskInfo`).d('检验任务信息')}
            >
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.TASK_LIST`,
                },
                <Table
                  dataSet={lineDs}
                  columns={lineColumns}
                  customizedCode="inspectDocMaintain-listLine"
                />,
              )}
            </Panel>
          </Collapse>
        </Spin>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.QUERY`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.LIST`,
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECT_DOC_LIST.TASK_LIST`,
      ],
    })(InspectDocList as any),
  ),
);
