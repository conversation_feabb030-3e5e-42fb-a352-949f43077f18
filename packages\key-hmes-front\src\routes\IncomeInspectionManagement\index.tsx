import React, { useEffect, useState, useMemo } from 'react';
import moment from 'moment';
import { Spin } from 'choerodon-ui';
import { DataSet, Lov } from 'choerodon-ui/pro';
import { Content } from 'components/Page';
import { FullScreenContainer } from '@jiaminghi/data-view-react';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import logo from './assets/logo.png';

import styles from './index.module.less';

import DailyDelivery from './components/DailyDelivery';
import TotalQty from './components/TotalQty';
import NgQty from './components/NgQty';
import EmergencyTask from './components/EmergencyTask';
import CheckSupplier from './components/CheckSupplier';
import { filterDS } from './Stores'
import NgReport from './components/NgReport';

// import { TopFilterFormDS } from './stores';

const tenantId = getCurrentOrganizationId();
// 指标lovCode
// const dimLovCode = 'HALM.REPORT_TARGET';
// 报检信息数量查询URL
// const queryListUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/list/info`;
// 日到货单数趋势信息查询URL
// const dailyDeliveryUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/daily-delivery/list`;
// 紧急检验任务信息查询
const emergencyTaskUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/emergency-task/list`;
// 查询检验单数量URL
// const qtyUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/inspect/qty`;
// 查询交验合格率趋势URL
// const passRateUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/pass-rate`;
// 交验不合格Top10供应商
const checkSupplierUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/check-supplier/list`;
// 不合格笔数处置状态
// const ngQtyUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/ng-qty/info`;
// 不合格信息播报
const ngReportUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/ng-report/info`;

const defaultSiteUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-user-inspect-permission/default/site/ui`

const CurrentTime = () => {
  const [nowTime, setNowTime] = useState(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
  useEffect(() => {
    const timer = setInterval(() => {
      return setNowTime(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
    }, 1000)

    return () => {  // 每次卸载都执行此函数，清楚定时器
      clearTimeout(timer)
    }
  }, []);
  return <span style={{ color: '#36C5FF', fontSize: 18, fontWeight: 600, marginLeft: '3%', marginBottom: '5%' }}> {nowTime} </span>;
};

// const Main = ({ topFilterFormDs, filterValue, setFilterValue }) => {
const Main = ({ isFullScreen }) => {
  const [taskData, setTaskData] = useState([]);
  const [checkData, setCheckData] = useState([]);
  const [ngData, setNgData] = useState([]);
  const [siteId, setSiteId] = useState();

  const filterDs = useMemo(() => new DataSet(filterDS()), []);
  const [timers, setTimers] = useState<number>();

  const lovDataSet = useMemo(() => new DataSet({
    autoCreate: true,
    fields: [{
      name: 'siteLov',
      type: 'object',
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: 'always',
    }, {
      name: 'siteId',
      bind: 'siteLov.siteId',
    }, {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    }],
  }), [])

  const [initLoading, setInitLoading] = useState<boolean>(false);

  useEffect(() => {
    getDefaultSite();
  }, []);

  useEffect(() => {
    getTimers();
    let time
   if(timers){
     time = setInterval(() => {
      getTaskData();
      getCheckData();
      getNgData();
    }, timers*60000)
   }
    return () => {
      clearTimeout(time)
    }
  }, [timers])

  useEffect(() => {
    lovDataSet.addEventListener("update", handleChangeSiteId);
    return () => {
      lovDataSet.removeEventListener("update", handleChangeSiteId);
    };
  }, []);

  const getTimers = async () => {
    const url = `/hpfm/v1/${tenantId}/lovs/value/batch?QMS.MANAGEMENT_FREQUENCY=QMS.MANAGEMENT_FREQUENCY`
    const result = await request(url, {
      method: 'GET',
    });
    const data = result['QMS.MANAGEMENT_FREQUENCY'].filter(item => item.value === 'INCOME')
    if (data.length > 0) {
      setTimers(Number(data[0].meaning))
    }
  }

  const handleChangeSiteId = ({ name, value }) => {
    if (name === "siteLov") {
      setSiteId(value.siteId);
      filterDs.setState('siteId', value.siteId);
      getTaskData();
      getCheckData();
      getNgData();
    }
  };

  /**
   * 紧急检验任务信息查询
   */
  const getTaskData = async () => {
    setInitLoading(true);
    const result = await request(emergencyTaskUrl, {
      method: 'GET',
      query: { siteId: lovDataSet.current?.get('siteId') },
    });
    setTaskData(result || []);
    setInitLoading(false);
  };

  /**
   * 交验不合格Top10供应商
   */
  const getCheckData = async () => {
    const result = await request(checkSupplierUrl, {
      method: 'GET',
      query: { siteId: lovDataSet.current?.get('siteId') },
    });
    setCheckData(result || []);
  };
  /**
   * 不合格信息滚动播报
   */
  const getNgData = async () => {
    const result = await request(ngReportUrl, {
      method: 'GET',
      query: { siteId: lovDataSet.current?.get('siteId') },
    });
    setNgData(result || []);
  };

  const getDefaultSite = () => {
    return request(defaultSiteUrl, { method: 'GET' }).then(res => {
      if (res && res.success && res?.rows?.siteId) {
        lovDataSet.current.set("siteLov", res.rows);
        lovDataSet.current.set("siteId", res.rows.siteId);
        setSiteId(res.rows.siteId);
        filterDs.setState('siteId', res.rows.siteId);
      } else {
        getTaskData();
        getCheckData();
        getNgData();
      }
    });
  };

  return (
    <>
      <Content style={{ padding: 0, margin: 0, height: '100%' }}>
        {/* loading */}
        {initLoading && <Spin spinning={initLoading} className={styles['center-loading']} />}
        <div className={styles['dashboard-container']}>
          {/* header */}
          <div className={styles['dashboard-title']}>
            <div className={styles['dashboard-title-left']}>
              <img src={logo} alt="img" style={{ width: '35%', height: '100%', marginLeft: '2%' }} />
              <CurrentTime />
              {/* <TopFilterForm topFilterFormDs={topFilterFormDs} setFilterValue={setFilterValue} /> */}
            </div>
            <div className={styles['dashboard-title-center']}>来料检验管理看板</div>
            <div className={styles['dashboard-title-right']}>
              {/* <CurrentTime /> */}
              <Lov dataSet={lovDataSet} name="siteLov" />
            </div>
          </div>

          {/* Content */}
          <div className={styles['dashboard-content']}>
            <div className={styles['dashboard-col-side']}>
              <div className={styles['dashboard-item-left-top']}>
                <DailyDelivery siteId={siteId} timers={timers}/>
              </div>
              <div className={styles['dashboard-item-left-bottom']}>
                <EmergencyTask data={taskData} />
              </div>
            </div>
            <div className={styles['dashboard-col-center']}>
              <div className={styles['dashboard-item-center-top']}>
                <TotalQty siteId={siteId} filterDs={filterDs} timers={timers}  />
              </div>
              <div className={styles['dashboard-item-center-bottom']}>
                <CheckSupplier data={checkData} />
              </div>
            </div>
            <div className={styles['dashboard-col-side']}>
              <div className={styles['dashboard-item-left-top']}>
                <NgQty isFullScreen={isFullScreen} siteId={siteId} filterDs={filterDs} timers={timers} />
              </div>
              <div className={styles['dashboard-item-left-bottom']}>
                <NgReport data={ngData} />
              </div>
            </div>

          </div>
        </div>
      </Content>
    </>
  );
};

const IncomeInspectionManagement = () => {
  const [isFullScreen, setIsFullScreen] = useState(false); // 是否全屏
  // const [filterValue, setFilterValue] = useState([]); // 筛选值

  // const topFilterFormDs = useMemo(() => new DataSet(TopFilterFormDS()), []);

  const windowFullScreenChange = () => {
    if (document.fullscreenElement) {
      setIsFullScreen(true);
    } else {
      setIsFullScreen(false);
    }
  };
  useEffect(() => {
    document.addEventListener('fullscreenchange', windowFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', windowFullScreenChange);
    };
  }, []);

  return (
    <>
      <div className={styles['screen-container']}>
        {isFullScreen ? (
          <FullScreenContainer>
            <Main
              // topFilterFormDs={topFilterFormDs}
              // setFilterValue={setFilterValue}
              // filterValue={filterValue}
              isFullScreen={isFullScreen}
            />
          </FullScreenContainer>
        ) : (
          <Main
            // topFilterFormDs={topFilterFormDs}
            // setFilterValue={setFilterValue}
            // filterValue={filterValue}
            isFullScreen={isFullScreen}
          />
        )}
      </div>
    </>
  );
};
export default IncomeInspectionManagement;
