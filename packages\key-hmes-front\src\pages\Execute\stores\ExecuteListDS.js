/**
 * @Description: 执行作业管理列表页 DS
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:53:32
 * @LastEditTime: 2021-08-31 17:17:52
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.workshop.execute';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  selection: 'multiple',
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-eo/list/ui`,
        method: 'GET',
        data: {
          ...data,
          eoType: (data.eoType && data.eoType.length) > 0 ? data.eoType.join(',') : undefined,
          status: (data.status && data.status.length) > 0 ? data.status.join(',') : undefined,
        },
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'eoId',
  cacheSelection: true,
  autoLocateFirst: false,
  queryFields: [
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.eoNum`).d('执行作业编码'),
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.materialId`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'eoType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.eoType`).d('执行作业类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      multiple: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=EO_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'workOrder',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.workOrderNum`).d('生产指令编码'),
      lovCode: 'MT.WORK_ORDER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'boilerNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.boilerNumber`).d('釜'),
    },
    {
      name: 'startTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.startTimeFrom`).d('开始时间从'),
      max: 'startTimeTo',
    },
    {
      name: 'startTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.startTimeTo`).d('开始时间至'),
      min: 'startTimeFrom',
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.operationLov`).d('工艺'),
      lovCode: 'MT.OPERATION',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.status`).d('执行作业状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      multiple: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS&type=eoStatusOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'productionLine',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.execute.productionLineId`).d('生产线'),
      lovCode: 'MT.MODEL.PRODLINE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'productionLineId',
      type: FieldType.string,
      bind: 'productionLine.prodLineId',
    },
    {
      name: 'workOrderId',
      type: FieldType.string,
      bind: 'workOrder.workOrderId',
    },
    {
      name: 'endTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.endTimeFrom`).d('结束时间从'),
      max: 'endTimeTo',
    },
    {
      name: 'endTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.model.execute.endTimeTo`).d('结束时间至'),
      min: 'endTimeFrom',
    },

  ],
  fields: [
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.eoNum`).d('执行作业编码'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.siteName`).d('站点名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialRevision`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialName`).d('物料名称'),
    },
    {
      name: 'eoTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.eoType`).d('执行作业类型'),
      textField: 'description',
      valueField: 'typeCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=EO_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.status`).d('执行作业状态'),
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=EO_STATUS&type=eoStatusOptions`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'productionLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.productionLineCode`).d('生产线编码'),
    },
    {
      name: 'productionLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.productionLineName`).d('生产线短描述'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.planStartTime`).d('计划开始时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.planEndTime`).d('计划结束时间'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.qty`).d('执行作业数量'),
    },
    {
      name: 'completedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.completedQty`).d('完成数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.scrappedQty`).d('报废数量'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.workOrderNum`).d('生产指令编码'),
    },
    {
      name: 'boilerNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.boilerNumber`).d('釜'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.operationName`).d('工艺名称'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute1.description`).d('工艺描述'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.qualityStatusDesc`).d('质量状态'),
    },
  ],
});

const historyDS = () => ({
  primaryKey: 'materialLotHisId',
  selection: false,
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'eventId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型'),
    },
    {
      name: 'eventTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeDesc`).d('事件类型描述'),
    },
    {
      name: 'eventRequestId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventRequestId`).d('事件请求类型ID'),
    },
    {
      name: 'requestTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeCode`).d('事件请求类型编码'),
    },
    {
      name: 'requestTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeDesc`).d('事件请求类型描述'),
    },
    {
      name: 'eventUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventUserName`).d('操作人'),
    },
    {
      name: 'eventTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTime`).d('操作时间'),
    },
    {
      name: 'eoNum',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('EO状态描述'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialRevision`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.materialName`).d('物料名称'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qtyEo`).d('EO数量'),
    },
    {
      name: 'completedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completedQty`).d('完成数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.scrappedQty`).d('报废数量'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.workOrderNum`).d('生产指令编码'),
    },
    {
      name: 'planStartTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
    },
    {
      name: 'eoTypeDesc',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eoTypeDesc`).d('执行作业类型描述'),
    },
    {
      name: 'productionLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionLineCode`).d('生产线编码'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'boilerNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.execute.boilerNumber`).d('釜'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态描述'),
    },
  ],
  transport: {
    read: config => {
      const { data } = config;
      // 查询请求的 axios 配置或 url 字符串
      return {
        ...config,
        data: data.eoId,
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo/eo/his/ui`,
        method: 'POST',
      };
    },
  },
});


export { tableDS, historyDS };
