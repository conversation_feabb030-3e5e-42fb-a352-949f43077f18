import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import {FieldType} from "choerodon-ui/pro/lib/data-set/enum";

const tenantId = getCurrentOrganizationId();
// const Host = `/mes-38546`;
const modelPrompt = 'tarzan.hmes.WorkOrderManagementPlatform';

const tableDS = () => {
  return {
    name: 'tableDS',
    primaryKey: 'workOrderId',
    paging: true,
    autoQuery: true,
    selection: 'multiple',
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
      },
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
      },
      {
        name: 'statusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.statusDesc`).d('工单状态'),
      },
      {
        name: 'workOrderTypeDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderTypeDesc`).d('工单类型'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'splitFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.splitFlag`).d('是否拆分'),
      },
      {
        name: 'prodLineName',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineName`).d('生产线描述'),
      },
      {
        name: 'prodLineCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线编码'),
      },
      {
        name: 'qty',
        type: 'number',
        label: intl.get(`${modelPrompt}.qty`).d('生产数量'),
      },
      {
        name: 'completedQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.completedQty`).d('完工数量'),
      },
      {
        name: 'releasedQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.releasedQty`).d('在制数量'),
      },
      {
        name: 'scrappedQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
      },
      {
        name: 'priority',
        type: 'string',
        label: intl.get(`${modelPrompt}.priority`).d('优先级'),
      },
      {
        name: 'bomName',
        type: 'string',
        label: intl.get(`${modelPrompt}.bomName`).d('装配清单名称'),
      },
      {
        name: 'bomRevision',
        type: 'string',
        label: intl.get(`${modelPrompt}.bomRevision`).d('装配清单版本'),
      },
      {
        name: 'routerName',
        type: 'string',
        label: intl.get(`${modelPrompt}.routerName`).d('工艺路线名称'),
      },
      {
        name: 'stoveCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.stoveCode`).d('炉号'),
      },
      {
        name: 'routerRevision',
        type: 'string',
        label: intl.get(`${modelPrompt}.routerRevision`).d('工艺路线版本'),
      },
      {
        name: 'planStartTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
      },
      {
        name: 'planEndTime',
        type: 'string',
        label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
      },
      {
        name: 'actualEndDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.actualEndDate`).d('实际完成时间'),
      },
      {
        name: 'attribute1',
        type: 'string',
        label: intl.get(`${modelPrompt}.attribute1`).d('区域编码'),
      },
      {
        name: 'organizationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.organizationName`).d('区域描述'),
      },
      {
        name: 'uomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'attribute2',
        type: 'number',
        label: intl.get(`${modelPrompt}.attribute2`).d('接收数量'),
      },
      {
        name: 'level',
        type: 'string',
        label: intl.get(`${modelPrompt}.level`).d('等级'),
      },
      {
        name: 'parentWorkOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.parentWorkOrderNum`).d('母工单编码'),
      },
      {
        name: 'productionVision',
        type: 'string',
        label: intl.get(`${modelPrompt}.productionVision`).d('生产版本'),
      },
      {
        name: 'areaWorkOrder',
        type: 'string',
        label: intl.get(`${modelPrompt}.areaWorkOrder`).d('区域工单'),
      },
      {
        name: 'productionBatch',
        type: 'string',
        label: intl.get(`${modelPrompt}.productionBatch`).d('工艺批次'),
      },
    ],
    queryFields: [
      {
        name: 'areaWorkOrder',
        type: 'string',
        label: intl.get(`${modelPrompt}.areaWorkOrder`).d('区域工单'),
      },
      {
        name: 'workOrderNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderNumNew`).d('生产线工单'),
      },
      {
        name: 'workOrderType',
        type: 'string',
        label: intl.get(`${modelPrompt}.workOrderType`).d('工单类型'),
        lovPara: { tenantId },
        textField: 'description',
        valueField: 'typeCode',
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=ORDER&typeGroup=WO_TYPE&type=workOrderTypeOptions`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'statusList',
        type: 'string',
        label: intl.get(`${modelPrompt}.status`).d('工单状态'),
        textField: 'description',
        valueField: 'statusCode',
        multiple: true,
        noCache: true,
        lovPara: { tenantId },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=WO_STATUS&type=workOrderStatusOptions`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'areaLov',
        type: 'object',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.areaLov`).d('区域'),
        lovCode: 'MT.APS.AREA',
        labelWidth: 150,
      },
      {
        name: 'areaCode',
        type: 'string',
        bind: 'areaLov.areaCode',
        labelWidth: 150,
      },
      {
        name: 'stoveCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.stoveCode`).d('炉号'),
      },
      {
        name: 'planStartTimeFrom',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.planStartTimeFrom`).d('计划开始时间从'),
        max: 'planStartTimeTo',
      },
      {
        name: 'planStartTimeTo',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.planStartTimeTo`).d('计划开始时间至'),
        min: 'planStartTimeFrom',
      },
      {
        name: 'materialObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
        lovCode: 'MT.MATERIAL',
        labelWidth: 150,
        textField: 'materialCode',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
      },
      {
        name: 'materialCode',
        type: 'string',
        bind: 'materialObj.materialCode',
      },
      {
        name: 'materialId',
        type: 'number',
        bind: 'materialObj.materialId',
      },
      {
        name: 'prodLineObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.prodLineObj`).d('生产线'),
        lovCode: 'HME.PERMISSION_PROD_LINE',
        labelWidth: 150,
        ignore: 'always',
        textField: 'prodLineCode',
        dynamicProps: {
          lovPara: () => {
            return {
              tenantId: getCurrentOrganizationId(),
            };
          },
        },
      },
      {
        name: 'prodLineCode',
        type: 'string',
        bind: 'prodLineObj.prodLineCode',
      },
      {
        name: 'prodLineId',
        type: 'number',
        bind: 'prodLineObj.prodLineId',
      },
      {
        name: 'siteCode',
        type: 'object',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
        lovCode: 'HME.ASSEMBL_EPOINT_USER_SITE',
        labelWidth: 150,
      },
      {
        name: 'siteId',
        type: 'number',
        bind: 'siteCode.siteId',
        labelWidth: 150,
      },
      {
        type: 'string',
        name: 'existFlag',
        label: intl.get(`${modelPrompt}.existFlag`).d('是否存在在制品'),
        lookupCode: 'HME.ZZP_EXIST',
      },
      {
        name: 'planEndTimeFrom',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.planEndTimeFrom`).d('计划结束时间从'),
        max: 'planEndTimeTo',
      },
      {
        name: 'planEndTimeTo',
        type: 'dateTime',
        label: intl.get(`${modelPrompt}.planEndTimeTo`).d('计划结束时间至'),
        min: 'planEndTimeFrom',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-order-new/list/ui`,
          method: 'GET',
        };
      },
    },
  };
};

const dispatchFormDS = () => {
  return {
    name: 'dispatchFormDS',
    paging: false,
    autoCreate: true,
    fields: [
      {
        name: 'qty',
        type: 'number',
        label: intl.get(`${modelPrompt}.qty`).d('数量'),
        required: true,
        // pattern: '^[1-9]*[1-9][0-9]*$',
        min: 1,
        precision: 0,
        dynamicProps: {
          max: ({ dataSet }) => {
            return dataSet.getState('workOrderQty');
          },
        },
      },
    ],
  };
};

const dispatchDateFormDS = () => {
  return {
    name: 'dispatchDateFormDS',
    paging: false,
    autoCreate: true,
    fields: [
      {
        name: 'startDate',
        type: 'date',
        label: intl.get(`${modelPrompt}.startDate`).d('开始时间'),
        required: true,
        max: 'endDate',
      },
      {
        name: 'endDate',
        type: 'date',
        label: intl.get(`${modelPrompt}.endDate`).d('结束时间'),
        required: true,
        min: 'startDate',
      },
    ],
  };
};

const specifyBatchFormDS = () => {
  return {
    name: 'specifyBatchFormDS',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('品名'),
      },{
        name: 'type',
        type: 'string',
        label: intl.get(`${modelPrompt}.type`).d('类型'),
      },
      {
        name: 'productionBatch',
        type: 'string',
        label: intl.get(`${modelPrompt}.productionBatch`).d('工艺批次'),
        required: true,
        dynamicProps: {
          required: ({ record }) => {
            return record.get('type') === '主产品';
          },
        },
        validator: value => {
          const pattern = new RegExp('[\u4e00-\u9fa5\\（，。\\）]');
          if (pattern.test(value)) {
            return intl.get(`${modelPrompt}.not.ch`).d('不允许输入中文');
          }
          return true;
        },
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-order-new/production/batch/for/Ui`,
          method: 'GET',
        };
      },
    },
    events: {
      update: ({ name, value, dataSet, oldValue }) => {
        if (name === 'productionBatch' && value && value !== oldValue) {
          const productionBatchs = value.replace(/\s*/g,"");
          dataSet?.current?.set('productionBatch', productionBatchs);
        }
      },
    },
  };
};

const levelDS = () => {
  return {
    name: 'levelDS',
    paging: false,
    fields: [
      {
        name: 'level',
        type: 'string',
        lookupCode: 'HME.WO_LEVEL',
        label: intl.get(`${modelPrompt}.level`).d('指定等级'),
      },
    ],
  };
}

const splitFormDS = () => {
  return {
    name: 'splitFormDS',
    paging: true,
    fields: [
      {
        name: 'splitQty',
        type: 'number',
        label: intl.get(`${modelPrompt}.splitQty`).d('拆分数量'),
        required: true,
        pattern: '^[1-9]*[1-9][0-9]*$',
      },
      {
        name: 'prodLineObj',
        type: 'object',
        required: true,
        label: intl.get(`${modelPrompt}.prodLineObj`).d('生产线'),
        lovCode: 'HME.WO_PRODLINE',
        ignore: 'always',
        textField: 'organizationCode',
      },
      {
        name: 'organizationCode',
        type: 'string',
        bind: 'prodLineObj.organizationCode',
        label: intl.get(`${modelPrompt}.organizationCode`).d('生产线编码'),
      },
      {
        name: 'organizationId',
        type: 'number',
        bind: 'prodLineObj.organizationId',
      },
    ],
  };
};
const bomAndRouterDS = () => {
  return {
    autoQuery: false,
    autoCreate: true,
    paging: false,
    name: 'bomAndRouterDS',
    fields: [
      {
        name: 'selectType',
        type: 'string',
        defaultValue: 'designChange',
      },
      {
        name: 'designProductionVersion',
        type: 'object',
      },
      {
        name: 'designProductionVersionCode',
        type: 'string',
        bind: 'designProductionVersion.productionVersionCode',
        label: intl.get(`${modelPrompt}.designProductionVersionCode`).d('生产版本'),
        disabled: true,
      },
      {
        name: 'designBom',
        type: 'object',
        label: intl.get(`${modelPrompt}.designBom`).d('装配清单/版本'),
      },
      {
        name: 'designBomName',
        type: 'string',
        bind: 'designBom.bomName',
        disabled: true,
      },
      {
        name: 'designBomRevision',
        type: 'string',
        bind: 'designBom.revision',
        disabled: true,
      },
      {
        name: 'designRouter',
        type: 'object',
        label: intl.get(`${modelPrompt}.designRouter`).d('工艺路线/版本'),
      },
      {
        name: 'designRouterId',
        type: 'number',
        bind: 'designRouter.routerId',
      },
      {
        name: 'designRouterName',
        type: 'string',
        bind: 'designRouter.routerName',
        disabled: true,
      },
      {
        name: 'designRouterRevision',
        type: 'string',
        bind: 'designRouter.revision',
        disabled: true,
      },
      {
        name: 'ownProductionVersion',
        type: 'boolean',
        defaultValue: false,
      },
      {
        name: 'productionVersion',
        type: 'object',
        label: intl.get(`${modelPrompt}.productionVersion`).d('生产版本'),
        lovCode: 'HME.WO_PRODUCTION_VERSION',
        textField: 'productionVersionCode',
        valueField: 'productionVersionId',
        noCache: true,
        required: true,
        dynamicProps: {
          lovPara({ record }) {
            return {
              tenantId,
              siteId: record.get('siteId'),
              materialId: record.get('materialId'),
            };
          },
        },
      },
      {
        name: 'productionVersionCode',
        type: 'string',
        bind: 'productionVersion.productionVersionCode',
      },
      {
        name: 'bom',
        type: 'object',
        label: intl.get(`${modelPrompt}.bom`).d('装配清单/版本'),
        lovCode: 'MT.METHOD.PROD-VERSION.BOM', // 设计变更
        // lovCode: 'MT.BOM_BASIC',
        textField: 'bomName',
        valueField: 'bomId',
        noCache: true,
        ignore: 'always',
        dynamicProps: {
          lovPara({ record }) {
            const queryParams = {
              tenantId,
              siteId: record.get('siteId'),
              materialId: record.get('materialId'),
              revisionCode: record.get('revisionCode'),
              productionVersionCode: record.get('productionVersionCode'),
            };
            return queryParams;
          },
          disabled({ record }) {
            // 有生产版本的话，只能通过选择生产版本带出来
            return record.get('ownProductionVersion');
          },
        },
      },
      {
        name: 'bomName',
        type: 'string',
        bind: 'bom.bomName',
      },
      {
        name: 'bomId',
        type: 'number',
        bind: 'bom.bomId',
      },
      {
        name: 'bomRevision',
        type: 'string',
        disabled: true,
        bind: 'bom.revision',
      },
      {
        name: 'router',
        type: 'object',
        label: intl.get(`${modelPrompt}.router`).d('工艺路线/版本'),
        lovCode: 'MT.METHOD.PROD-VERSION.ROUTER', // 设计变更
        textField: 'routerName',
        valueField: 'routerId',
        noCache: true,
        ignore: 'always',
        dynamicProps: {
          lovPara({ record }) {
            const queryParams = {
              tenantId,
              siteId: record.get('siteId'),
              materialId: record.get('materialId'),
              revisionCode: record.get('revisionCode'),
              productionVersionCode: record.get('productionVersionCode'),
            };
            return queryParams;
          },
          disabled({ record }) {
            // 有生产版本的话，只能通过选择生产版本带出来
            return record.get('ownProductionVersion');
          },
        },
      },
      {
        name: 'routerName',
        type: 'string',
        bind: 'router.routerName',
      },
      {
        name: 'routerId',
        type: 'number',
        bind: 'router.routerId',
      },
      {
        name: 'routerRevision',
        type: 'string',
        disabled: true,
        bind: 'router.revision',
      },
    ],
  }
};

const prodDS = () => {
  return {
    name: 'prodDS',
    paging: false,
    autoCreate:true,
    fields: [
      {
        name: 'prodLineObj',
        type: 'object',
        required: true,
        label: intl.get(`${modelPrompt}.prodLineObj`).d('生产线'),
        lovCode: 'HME.WO_PRODLINE',
        ignore: 'always',
        textField: 'organizationCode',
      },
      {
        name: 'organizationCode',
        type: 'string',
        bind: 'prodLineObj.organizationCode',
        label: intl.get(`${modelPrompt}.organizationCode`).d('生产线编码'),
      },
      {
        name: 'organizationId',
        type: 'number',
        bind: 'prodLineObj.organizationId',
      },
    ],
  };
}

const drawerDS = () => {
  return {
    name: 'drawerDS',
    primaryKey: 'workOrderId',
    paging: true,
    autoQuery: false,
    selection: false,
    fields: [
      {
        name: 'eoNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      },
      {
        name: 'identification',
        type: 'string',
        label: intl.get(`${modelPrompt}.identification`).d('执行作业标识'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'revisionCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'statusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.statusDesc`).d('质量状态'),
      },
      {
        name: 'qty',
        type: 'number',
        label: intl.get(`${modelPrompt}.qty`).d('执行作业数量'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-order-new/list/detail/ui`,
          method: 'GET',
        };
      },
    },
  }
}

const historyDS = () => ({
  primaryKey: 'materialLotHisId',
  selection: false,
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    // {
    //   name: 'eventId',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.model.history.eventId`).d('事件ID'),
    // },
    // {
    //   name: 'eventTypeCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.model.history.eventTypeCode`).d('事件类型'),
    // },
    // {
    //   name: 'eventTypeDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.model.history.eventTypeDesc`).d('事件类型描述'),
    // },
    // {
    //   name: 'eventRequestId',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.model.history.eventRequestId`).d('事件请求类型ID'),
    // },
    // {
    //   name: 'requestTypeCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.model.history.requestTypeCode`).d('事件请求类型编码'),
    // },
    // {
    //   name: 'requestTypeDesc',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.model.history.requestTypeDesc`).d('事件请求类型描述'),
    // },
    {
      name: 'eventUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.eventUserName`).d('操作人'),
    },
    {
      name: 'eventTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.eventTime`).d('操作时间'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.workOrderNum`).d('工单编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.siteName`).d('工厂'),
    },
    {
      name: 'workOrderTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.workOrderTypeDesc`).d('工单类型'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.statusDesc`).d('工单状态'),
    },
    {
      name: 'lastWoStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.lastWoStatusDesc`).d('工单前次状态'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.prodLineCode`).d('生产线编码'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.prodLineName`).d('生产线名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.materialName`).d('物料描述'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.qty`).d('数量'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.uomName`).d('单位'),
    },
    {
      name: 'priority',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.priority`).d('优先级'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.planEndTime`).d('计划开始时间'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.planStartTime`).d('计划结束时间'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.locatorCode`).d('默认完工库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.locatorName`).d('默认完工库位描述'),
    },
    {
      name: 'bomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.bomName`).d('装配清单'),
    },
    {
      name: 'routerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.routerName`).d('工艺流程'),
    },
    {
      name: 'completeControlQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.completeControlQty`).d('完工限制值'),
    },
    {
      name: 'completeControlTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.history.completeControlTypeDesc`).d('完工限制类型'),
    },
  ],
  transport: {
    read: config => {
      const { data } = config;
      // 查询请求的 axios 配置或 url 字符串
      return {
        ...config,
        data: data.woIds,
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-work-order-new/wo/his/ui`,
        method: 'POST',
      };
    },
  },
});



export {drawerDS, prodDS, tableDS, dispatchFormDS, splitFormDS,bomAndRouterDS, levelDS, dispatchDateFormDS, specifyBatchFormDS, historyDS };
