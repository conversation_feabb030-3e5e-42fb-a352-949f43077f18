import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import axios from 'axios';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
// 不良分布图
const url = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qms-oper-inspects/not-pass-profile`;

const Chart = (params) => {
  const chartRef = useRef(null);
  const [xData, setXData] = useState<any>();
  const [yData1, setYData1] = useState<any>();
  const [yData2, setYData2] = useState<any>();
  useEffect(() => {
    fetchData();
  }, [params]);

  const fetchData = useCallback(async () => {
    const {
      filterDataRef,
      operation,
    } = params;
    const par = {
      ...filterDataRef.current,
      operation,
    }
    const xTemp:any=[];
    const fTemp:any=[];
    const rTemp:any=[];
    if(par?.timeDimension){
      const res = await axios.post<any, any>(url,par);
      if(res?.length){      
        res.forEach((i)=>{
          xTemp.push(i.inspectItemDesc);
          fTemp.push(i.notPassCount);
          rTemp.push(i.notPassRate);
          // rTemp.push(`${i.notPassRate*10000/100}%`);
        })      
      }
    }
    setXData(xTemp);
    setYData1(fTemp);
    setYData2(rTemp);
  }, []);
  const option = useMemo(() => {    
    return {
      grid: {
        left: '4%',
        right: '4%',
        bottom:'21%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: [
        {
          type: 'category',
          // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          data:xData,
          axisLine: { // 控制轴线样式
            lineStyle: {
              color: '#fff', // 设置轴线颜色
            },
          },
          axisLabel: { // 控制轴标签样式
            textStyle: {
              color: '#fff', // 设置轴标签文字颜色
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisTick: { show: false },// 坐标刻度是否显示
          // splitLine: {show:true}, // 是否显示背景分隔线
          splitLine: { // 分隔线样式
            lineStyle: {
              type: 'dashed', // 改变分隔线样式为虚线 默认是直线
            },
          },
          axisLine: { // 控制轴线样式
            lineStyle: {
              color: '#fff', // 设置轴线颜色
            },
          },
          axisLabel: { // 控制轴标签样式
            textStyle: {
              color: '#fff', // 设置轴标签文字颜色
            },
          },
        },
      ],
      series: [
        {
          name: '数量',
          type: 'bar',
          // data: [120, 200, 150],
          data: yData1,
          showBackground: true,
          label: {
            show: true,
            position: 'top',
            color:'#FFF',
          },
          backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)',
          },
          itemStyle: {
            color: params.operation === 'FS'? new echarts.graphic.LinearGradient(
              0, 0, 0, 1, // 渐变方向从上到下
              [
                {offset: 0, color: '#2B7AE1'},
                {offset: 1, color: '#323171'},
              ],
            ):params.operation === 'RB'?new echarts.graphic.LinearGradient(
              0, 0, 0, 1, // 渐变方向从上到下
              [
                {offset: 0, color: '#CB8031'},
                {offset: 1, color: '#323171'},
              ]):
              new echarts.graphic.LinearGradient(
                0, 0, 0, 1, // 渐变方向从上到下
                [
                  {offset: 0, color: '#02EBE7'},
                  {offset: 1, color: '#323171'},
                ]),
            
          },
        },
        {
          name: '累计占比',
          type: 'line',
          smooth: true,
          // data: [0,0,0, 791, 390, 30, 10],
          data: yData2,
          color:'#00FFF4',
          label: {
            show: true,
            position: 'top',
            color:'#FFF',
          },
          itemStyle: {
            color: '#786571',
            // new echarts.graphic.LinearGradient(
            //   0, 0, 0, 1, // 渐变方向从上到下
            //   [
            //     {offset: 0, color: '#0E5FFF'},
            //     {offset: 0.5, color: '#00FFF4'},
            //     {offset: 1, color: '#0B81FD'},
            //   ],
            // ),
          },
          lineStyle: {
            width: 5,  // 设置线条的粗细为3
          },
        },
      ],
    };
  }, [xData, yData1, yData2]);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <div key={params?.operation} ref={chartRef} style={{ width: '100%', height: '100%' }} />
    </div>
  );
};
export default Chart;
