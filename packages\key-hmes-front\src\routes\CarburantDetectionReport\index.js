/**
 * @Description:  增碳剂检测数据表-入口页
 */
import React, {useEffect, useState} from 'react';
import {Button, DataSet, Table} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import { useDataSetEvent } from 'utils/hooks';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import { useRequest } from '@components/tarzan-hooks';
import { tableDS } from './stores/ListDS';
import { Export } from './services';

const modelPrompt = 'tarzan.CarburantDetectionReport';

const CarburantDetectionReportList = observer(props => {

  const { tableDs } = props;

  const { run: exportFun, loading: exportLoading } = useRequest(Export(), { manual: true, needPromise: true });

  const [dynamicColumns, setDynamicColumns] = useState([
    {
      name: 'siteCode',
      align: 'center',
    },
    {
      name: 'siteName',
      align: 'center',
    },
    {
      name: 'sourceObjectCode',
      align: 'center',
      width: 200,
    },
    {
      name: 'productionBatch',
      align: 'center',
      width: 200,
    },
    {
      name: 'creationDate',
      align: 'center',
    },
    {
      name: 'creationDateTime',
      align: 'center',
      width: 200,
    },
    {
      name: 'materialName',
      align: 'center',
      width: 200,
    },
    {
      name: 'packagingMethod',
      align: 'center',
      width: 200,
    },
    {
      name: 'okJudgement',
      align: 'center',
    },
    {
      name: 'ngJudgement',
      align: 'center',
    },
    {
      name: 'remark',
      align: 'center',
    },
  ]);

  useEffect(() => {
    tableDs.query();
  }, []);

  useDataSetEvent(tableDs, 'load', () => {
    const inspectItemColumn = tableDs.toData()[0]?.inspectItems?.map((e) => {
      return {
        name: `${e.inspectItemId}`,
        align: 'center',
        title: e.inspectItemDesc,
      }
    }) || [];
    setDynamicColumns([
      {
        name: 'siteCode',
        align: 'center',
      },
      {
        name: 'siteName',
        align: 'center',
      },
      {
        name: 'sourceObjectCode',
        align: 'center',
        width: 200,
      },
      {
        name: 'productionBatch',
        align: 'center',
        width: 200,
      },
      {
        name: 'creationDate',
        align: 'center',
      },
      {
        name: 'creationDateTime',
        align: 'center',
        width: 200,
      },
      {
        name: 'materialName',
        align: 'center',
        width: 200,
      },
      {
        name: 'packagingMethod',
        align: 'center',
        width: 200,
      },
      ...inspectItemColumn,
      {
        name: 'okJudgement',
        align: 'center',
      },
      {
        name: 'ngJudgement',
        align: 'center',
      },
      {
        name: 'remark',
        align: 'center',
      },
    ]);
  });


  const onFieldEnterDown = () => {
    tableDs.query(props.tableDs.currentPage);
  };

  const handleExport = async () => {
    const res = await exportFun({
      params: {
        ...tableDs.queryDataSet.toData()[0],
      },
    })
    const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
    const a = document.createElement('a');
    const url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = decodeURI('增碳剂检测数据表.xlsx');
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    a.parentNode?.removeChild(a);
    window.URL.revokeObjectURL(url);
  };


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('增碳剂检测数据表')}>
        <Button onClick={handleExport} color="primary" disabled={exportLoading||!tableDs.records.length}>
          {intl.get(`hzero.common.button.export`).d('导出')}
        </Button>
      </Header>
      <Content>
        <Table
          searchCode="CarburantDetectionReportList"
          customizedCode="CarburantDetectionReportList"
          dataSet={tableDs}
          columns={[...dynamicColumns]}
          queryFieldsLimit={9}
          highLightRow
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
            onFieldEnterDown,
          }}
        />
      </Content>
    </div>
  );
});

export default flow(
  formatterCollections({ code: ['tarzan.CarburantDetectionReport', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({...tableDS()});
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(CarburantDetectionReportList);
