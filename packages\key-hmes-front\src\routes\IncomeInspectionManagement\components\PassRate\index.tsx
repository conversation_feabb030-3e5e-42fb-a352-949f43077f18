import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { Select, YearPicker, MonthPicker, DateTimePicker, Lov } from 'choerodon-ui/pro';
import { filterNullValueObject, getCurrentOrganizationId } from 'utils/utils';
import { ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import intl from 'utils/intl';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import DashboardCard from '../DashboardCard.jsx';
// import title from '../../assets/TimelinessRateChartTitle.png';
import styles from '../../index.module.less';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'hmes.incomeInspectionManagement';
// 交验合格率趋势查询URL
const url = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/pass-rate`;

const PassRate = (props) => {

  const { siteId, filterDs } = props;

  const chartRef = useRef(null);
  const [xData, setXData] = useState<any>([]);
  const [yData1, setYData1] = useState<any>([]);
  const [yData2, setYData2] = useState<any>([]);
  const [timeDimension, setTimeDimension] = useState<any>();

  useEffect(() => {
    if(filterDs) {
      filterDs.addEventListener('update', fetchData);
    }
    return () => {
      if(filterDs) {
        filterDs.removeEventListener('update', fetchData)
      }
    };
  }, [filterDs])

  useEffect(() => {
    fetchData();
  }, [siteId]);
  // const xData:any = [];
  // const yData1:any = [];
  // const yData2:any = [];
  const fetchData = useCallback(async () => {
    const {timeDimension, timeDimensionMeaning, year, month, date,season,week, materialIds, supplierIds } = filterDs.toData()[0];
    const params ={
      siteId: siteId || filterDs.getState('siteId'),
      timeDimension: timeDimensionMeaning || (filterDs?.current?.getField('timeDimension')?.getLookupData(timeDimension)?.meaning)?.charAt(0),
      year,
      materialIds,
      supplierIds,
      date: timeDimension === '1'? year : timeDimension === '2' ? month:
        timeDimension === '3' ?date:timeDimension === '4'?season: `${`${month}/${week}`}`,
    };
    const res = await request(url, {
      method: 'POST',
      body: filterNullValueObject(params),
    });
    // const xValue = ['年度','季度', '月度'];
    // const yValue = [res?.yearRate||0,res?.quarterRate||0,res?.monthRate||0];
    const xTemp:any=[];
    const yTemp:any=[];
    if(res?.length){      
      res.forEach((i)=>{
        xTemp.push(i.dateVar);
        yTemp.push(i.monthRate);
      })
    }
    // xData.push(xValue);
    setXData(xTemp);
    setYData1(yTemp);
    // yData1.push(yValue);
    if(res?.vo5List?.length){
      setXData(res.vo5List.map(i => i.xdata));
      setYData2(res.vo5List.map(i => i.yData));

      // res.vo5List.forEach((i)=>{
      //   xData.push(i.xData);
      //   yData2.push(i.yData);
      // })
    }
    // setData(res);
  }, [siteId]);

  // const formattedValue = useCallback(
  //   (v: number) => {
  //     if (v > 10000) {
  //       return `${Number((v / 10000).toFixed(2)).toLocaleString('en-US')}
  //       ${data?.targetCode === 'ORIGINAL_COST' ? '万元' : '万'}`;
  //     } 
  //     return `${v.toLocaleString('en-US')}`;
      
  //   },
  //   [data],
  // );
  // const formattedData = useMemo(() => {
  //   const res = [];
  //   const dataArr = data?.detailDataVOList ?? [];
  //   const dataLen = dataArr?.length;

  //   const otherItem = {
  //     name: '其他',
  //     value: 0,
  //   };
  //   dataArr.forEach((item, index) => {
  //     if (index < 5) {
  //       res.push({
  //         name: `${item.dimName}`,
  //         value: item.targetValue,
  //       });
  //     } else {
  //       otherItem.value += item.targetValue;
  //     }
  //   });
  //   if (dataLen > 5) {
  //     res.push(otherItem);
  //   }
  //   return res;
  // }, [data]);

  const option = useMemo(() => {
    return {
      title:{        
        top:'2%',
        text: '交验合格率趋势',
        left: 'center',
        textStyle: {
          fontWeight: 'bold', // 加粗
          color: '#00fff4',
        },
      },
      grid: {
        top: '20%',
        left: '4%',
        right: '4%',
        bottom:'2%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: [
        {
          type: 'category',
          // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          data:xData,
          axisLine: { // 控制轴线样式
            lineStyle: {
              color: '#fff', // 设置轴线颜色
            },
          },
          axisLabel: { // 控制轴标签样式
            textStyle: {
              color: '#fff', // 设置轴标签文字颜色
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisTick: { show: false },// 坐标刻度是否显示
          // splitLine: {show:true}, // 是否显示背景分隔线
          splitLine: { // 分隔线样式
            lineStyle: {
              type: 'dashed', // 改变分隔线样式为虚线 默认是直线
            },
          },
          axisLine: { // 控制轴线样式
            lineStyle: {
              color: '#fff', // 设置轴线颜色
            },
          },
          axisLabel: { // 控制轴标签样式
            textStyle: {
              color: '#fff', // 设置轴标签文字颜色
            },
          },
        },
      ],
      series: [
        {
          name: '柱状图',
          type: 'bar',
          // data: [120, 200, 150],
          data: yData1,
          showBackground: true,
          label: {
            show: true,
            position: 'top',
            color:'#FFF',
          },
          backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)',
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1, // 渐变方向从上到下
              [
                {offset: 0, color: '#00A2FF'},
                {offset: 1, color: '#00CCD2'},
              ],
            ),
          },
        },
        {
          name: '折线图',
          type: 'line',
          smooth: true,
          // data: [0,0,0, 791, 390, 30, 10],
          data: yData2,
          color:'#00FFF4',
          label: {
            show: true,
            position: 'top',
            color:'#FFF',
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1, // 渐变方向从上到下
              [
                {offset: 0, color: '#0E5FFF'},
                {offset: 0.5, color: '#00FFF4'},
                {offset: 1, color: '#0B81FD'},
              ],
            ),
          },
          lineStyle: {
            width: 5,  // 设置线条的粗细为3
          },
        },
      ],
    };
  }, [xData, yData1, yData2]);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option]);

  const onFilterChange = (e) => {
    setTimeDimension(e);
  };

  return (
    <DashboardCard height="100%">
      <div className={styles['my-chart-filter']}>
        <div className={styles['container-inventory-select']}>
          <Select
            dataSet={filterDs}
            style={{backgroundColor: 'rgba(0,0,0,0)'}}
            name="timeDimension"
            onChange={onFilterChange}
            showValidation={ShowValidation.tooltip}
          />
          {timeDimension === '1' ? (
            <YearPicker name="year" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
          ) : timeDimension === '2' ? (
            <MonthPicker name="month" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
          ) : timeDimension === '3' ? (
            <DateTimePicker name="date"  dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
            
          ) : timeDimension === '4' ? (
            <><YearPicker name="year" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
              <Select name="season" 
                style={{backgroundColor: 'rgba(0,0,0,0)'}}
                dataSet={filterDs} />
            </>
          ):
            <><MonthPicker name="month" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
              <Select name="week" 
                style={{backgroundColor: 'rgba(0,0,0,0)'}}
                dataSet={filterDs} />
            </>
          }
          <Lov
            dataSet={filterDs}
            name="materialLov"
            placeholder={intl.get(`${modelPrompt}.placeholder.materialLov`).d('物料')}
          />
          <Lov
            dataSet={filterDs}
            name="supplierLov"
            placeholder={intl.get(`${modelPrompt}.placeholder.supplierLov`).d('供应商')}
          />
        </div>
      </div>
      <div className={styles['my-chart']}>
        <div style={{ width: '100%', height: '100%' }}>
          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
        </div>
      </div>
    </DashboardCard>
  );
};
export default PassRate;
