import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { DEFAULT_DATETIME_FORMAT } from 'utils/constants';
import { HALM_MTC, HALM_MDM } from 'alm/utils/config';

const organizationId = getCurrentOrganizationId();

const prompt = 'amtc.serviceApply';
const promptCode = `${prompt}.model.serviceApply`;

// code:所在页面编码标识
const detailCommonFields = () => {
  return [
    {
      name: 'srNumber',
      type: 'string',
      label: intl.get(`${promptCode}.srNumber`).d('申请编号'),
    },
    {
      name: 'srTypeLov',
      type: 'object',
      lovCode: 'AMTC.SRTYPES',
      textField: 'srTypeName',
      valueField: 'srTypeId',
      label: intl.get(`${promptCode}.srType`).d('服务申请类型'),
      ignore: 'always',
      required: true,
    },
    {
      name: 'srTypeId',
      type: 'number',
      bind: 'srTypeLov.srTypeId',
    },
    {
      name: 'srTypeName',
      type: 'string',
      bind: 'srTypeLov.srTypeName',
    },
    {
      name: 'providedObjectCode',
      type: 'string',
      bind: 'srTypeLov.providedObjectCode',
    },
    {
      name: 'srName',
      type: 'string',
      label: intl.get(`${promptCode}.srName`).d('申请概述'),
      required: true,
      maxLength: 240,
    },
    {
      name: 'priorityLov',
      type: 'object',
      label: intl.get(`${promptCode}.priority`).d('优先级'),
      required: true,
      ignore: 'always',
      lovCode: 'AMTC.PRIORITIES',
    },
    {
      name: 'priorityId',
      type: 'number',
      bind: 'priorityLov.priorityId',
    },
    {
      name: 'priorityName',
      type: 'string',
      bind: 'priorityLov.priorityName',
      label: intl.get(`${promptCode}.priority`).d('优先级'),
    },
    {
      name: 'maintSiteLov',
      type: 'object',
      lovCode: 'AMDM.ASSET_MAINT_SITE',
      label: intl.get(`${promptCode}.maintSite`).d('服务区域'),
      ignore: 'always',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => !['DRAFT', 'REJECTED'].includes(record?.get('srStatus')),
      },
    },
    {
      name: 'maintSiteId',
      type: 'number',
      bind: 'maintSiteLov.maintSiteId',
    },
    {
      name: 'maintSiteName',
      type: 'string',
      bind: 'maintSiteLov.maintSiteName',
    },
    {
      name: 'assetLov',
      type: 'object',
      lovCode: 'AAFM.ASSET_RECEIPT',
      valueField: 'assetId',
      textField: 'descAndLabel',
      label: intl.get(`${promptCode}.asset`).d('设备'),
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            maintSiteId: record.get('maintSiteId'),
            assetLocationId: record.get('assetLocationId'),
            maintainFlag: 1,
            aclFlag: 1,
          };
        },
        required: ({ record }) => record.get('providedObjectCode') === 'LOCATION_OR_DEVICE',
        disabled: ({ record }) => record.get('providedObjectCode') === 'NOT_ALLOW',
      },
    },
    {
      name: 'assetId',
      type: 'number',
      bind: 'assetLov.assetId',
    },
    {
      name: 'descAndLabel',
      type: 'string',
      bind: 'assetLov.descAndLabel',
    },
    // {
    //   name: 'assetLocationId',
    //   type: 'number',
    //   dynamicProps: {
    //     required: ({ record }) => {
    //       return (
    //         record.get('providedObjectCode') === 'LOCATION_OR_DEVICE' ||
    //         record.get('providedObjectCode') === 'LOCATION'
    //       );
    //     },
    //     disabled: ({ record }) => record.get('providedObjectCode') === 'NOT_ALLOW',
    //   },
    // },
    {
      name: 'assetLocationName',
      type: 'string',
      label: intl.get(`${promptCode}.assetLocation`).d('位置'),
      dynamicProps: {
        required: ({ record }) => {
          return (
            record.get('providedObjectCode') === 'LOCATION_OR_DEVICE' ||
            record.get('providedObjectCode') === 'LOCATION'
          );
        },
        disabled: ({ record }) => record.get('providedObjectCode') === 'NOT_ALLOW',
      },
    },
    {
      name: 'locationDesc',
      type: 'string',
      label: intl.get(`${promptCode}.locationDesc`).d('位置补充说明'),
      maxLength: 240,
    },
    {
      name: 'evalItemLov',
      type: 'object',
      lovCode: 'AMTC.EVAL_ITEM_LIST',
      label: intl.get(`${promptCode}.evalItem`).d('缺陷评估项'),
      ignore: 'always',
      textField: 'evalItemName',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            enabledFlag: 1,
            organizationId,
            assetId: record.get('assetId'),
          };
        },
      },
    },
    {
      name: 'evalItemId',
      type: 'number',
      bind: 'evalItemLov.evalItemId',
    },
    {
      name: 'evalItemName',
      type: 'string',
      bind: 'evalItemLov.evalItemName',
    },
    {
      name: 'faultDate',
      type: 'date',
      format: DEFAULT_DATETIME_FORMAT,
      label: intl.get(`${promptCode}.faultDate`).d('缺陷时间'),
    },
    {
      name: 'description',
      type: 'string',
      label: intl.get(`${promptCode}.description`).d('描述'),
      maxLength: 240,
    },
    {
      name: 'contactLov',
      type: 'object',
      lovCode: 'AORI.EMPLOYEE_UNIT',
      label: intl.get(`${promptCode}.contact`).d('需求方联系人'),
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId: organizationId,
            unitId: record.get('orgId'),
          };
        },
      },
    },
    {
      name: 'contactId',
      type: 'number',
      bind: 'contactLov.employeeId',
    },
    {
      name: 'contactName',
      type: 'string',
      bind: 'contactLov.employeeName',
    },
    {
      name: 'contactDesc',
      type: 'string',
      label: intl.get(`${promptCode}.contactDesc`).d('联系人'),
    },
    {
      name: 'phone',
      type: 'string',
      label: intl.get(`${promptCode}.phone`).d('联系电话'),
    },
    {
      name: 'orgName',
      type: 'string',
      label: intl.get(`${promptCode}.orgName`).d('需求组织'),
    },
    {
      name: 'manuallySpecifyFlag',
      type: 'boolean',
      label: intl.get(`${promptCode}.manuallySpecifyFlag`).d('手工指定'),
      trueValue: 1,
      falseValue: 0,
      defaultValue: 0,
    },
    {
      name: 'mapSourceCode',
      type: 'string',
      lookupCode: 'AMTC.MAP_SOURCE',
      label: intl.get(`${promptCode}.mapSource`).d('地图来源'),
      defaultValue: 'NO_DISPLAY',
    },
    {
      name: 'mapSourceCodeMeaning',
      type: 'string',
    },
    {
      name: 'plannerGroupId',
      type: 'number',
    },
    {
      name: 'plannerGroupName',
      type: 'string',
      label: intl.get(`${promptCode}.plannerGroup`).d('计划员组'),
      required: true,
    },
    {
      name: 'plannerId',
      type: 'number',
    },
    {
      name: 'plannerName',
      type: 'string',
      label: intl.get(`${promptCode}.planner`).d('计划员'),
    },
    {
      name: 'reporterOrgLov',
      label: intl.get(`${promptCode}.reportOrg`).d('报告组织'),
      type: 'object',
      lovCode: 'AMDM.ORG_LIST_C7N',
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId: organizationId,
          };
        },
      },
      ignore: 'always',
      required: true,
    },
    {
      name: 'reportOrgId',
      type: 'number',
      bind: 'reporterOrgLov.unitId',
    },
    {
      name: 'reportOrgName',
      label: intl.get(`${promptCode}.reportOrg`).d('报告组织'),
      type: 'string',
      bind: 'reporterOrgLov.unitName',
    },
    {
      name: 'reporterLov',
      type: 'object',
      lovCode: 'AORI.EMPLOYEE_UNIT',
      label: intl.get(`${promptCode}.reporter`).d('报告人'),
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId: organizationId,
            unitId: record.get('reportOrgId'),
          };
        },
      },
      required: true,
    },
    {
      name: 'reporterId',
      type: 'number',
      bind: 'reporterLov.employeeId',
    },
    {
      name: 'reporterName',
      type: 'string',
      bind: 'reporterLov.employeeName',
    },
    {
      name: 'reportDate',
      type: 'dateTime',
      format: DEFAULT_DATETIME_FORMAT,
      label: intl.get(`${promptCode}.reportDate`).d('报告时间'),
    },
    {
      name: 'sourceTypeCode',
      type: 'string',
      lookupCode: 'AMTC.SR_SOURCE_TYPE',
      label: intl.get(`${promptCode}.srSourceType`).d('服务申请单来源'),
    },
    {
      name: 'sourceReference',
      type: 'string',
      label: intl.get(`${promptCode}.sourceReference`).d('来源单据号'),
    },
    {
      name: 'woNum',
      type: 'string',
      label: intl.get(`${promptCode}.woNum`).d('单据编号'),
    },
    {
      name: 'denialReason',
      type: 'string',
      maxLength: 240,
      label: intl.get(`${promptCode}.refuseReason`).d('拒绝的原因'),
    },
    {
      name: 'alarmRecordIds', // 告警记录id
      type: 'number',
      multiple: true,
    },
  ];
};

const detailMoreFields = evalHierarchyList => {
  if (!evalHierarchyList) return [];
  const fields = [];
  if (evalHierarchyList.length > 1) {
    fields.push(
      {
        name: `${evalHierarchyList[0].code}`,
        type: 'object',
        lovCode: 'AMTC.ASMT_CODES_LIST',
        label: intl
          .get(`${prompt}.model.serviceApply.${evalHierarchyList[0].id}`)
          .d(`${evalHierarchyList[0].evalHierarchyName}`),
        ignore: 'always',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: organizationId,
              evalItemId: record.get('evalItemId'),
              basicTypeCode: evalHierarchyList[0].basicTypeCode,
            };
          },
        },
      },
      {
        name: `${evalHierarchyList[0].id}`,
        type: 'number',
        bind: `${evalHierarchyList[0].code}.asmtCodeId`,
      },
      {
        name: `${evalHierarchyList[0].meaning}`,
        type: 'string',
        bind: `${evalHierarchyList[0].code}.asmtCodeName`,
      },
      {
        name: `${evalHierarchyList[1].code}`,
        type: 'object',
        lovCode: 'AMTC.ASMT_CODES_LIST',
        label: intl
          .get(`${prompt}.model.serviceApply.${evalHierarchyList[1].id}`)
          .d(`${evalHierarchyList[1].evalHierarchyName}`),
        ignore: 'always',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: organizationId,
              evalItemId: record.get('evalItemId'),
              parentCodeId: record.get(evalHierarchyList[0].id),
              basicTypeCode: evalHierarchyList[1].basicTypeCode,
            };
          },
        },
      },
      {
        name: `${evalHierarchyList[1].id}`,
        type: 'number',
        bind: `${evalHierarchyList[1].code}.asmtCodeId`,
      },
      {
        name: `${evalHierarchyList[1].meaning}`,
        type: 'string',
        bind: `${evalHierarchyList[1].code}.asmtCodeName`,
      }
    );
  } else if (evalHierarchyList.length === 1) {
    fields.push(
      {
        name: `${evalHierarchyList[0].code}`,
        type: 'object',
        lovCode: 'AMTC.ASMT_CODES_LIST',
        label: intl
          .get(`${prompt}.model.serviceApply.${evalHierarchyList[0].id}`)
          .d(`${evalHierarchyList[0].evalHierarchyName}`),
        ignore: 'always',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: organizationId,
              evalItemId: record.get('evalItemId'),
              basicTypeCode: evalHierarchyList[0].basicTypeCode,
            };
          },
        },
      },
      {
        name: `${evalHierarchyList[0].id}`,
        type: 'number',
        bind: `${evalHierarchyList[0].code}.asmtCodeId`,
      },
      {
        name: `${evalHierarchyList[0].meaning}`,
        type: 'string',
        bind: `${evalHierarchyList[0].code}.asmtCodeName`,
      }
    );
  }
  return fields;
};

const detailTransport = {
  read: ({ data }) => {
    return {
      url: `${HALM_MTC}/v1/${organizationId}/sr/${data.srId}`,
      method: 'GET',
    };
  },
  submit: ({ data }) => {
    return {
      data: data[0],
      url: `${HALM_MTC}/v1/${organizationId}/sr`,
      method: data[0].srId ? 'PUT' : 'POST',
    };
  },
};

const locationTreeDS = () => {
  return {
    autoQuery: true,
    selection: 'single',
    primaryKey: 'assetLocationId',
    idField: 'assetLocationId',
    dataKey: 'content',
    parentField: 'parentLocationId',
    expandField: 'expand',
    paging: 'server',
    pageSize: 10,
    queryFields: [
      {
        name: 'locationTypeName',
        label: intl.get(`${promptCode}.locationType`).d('位置类型'),
      },
      {
        name: 'locationName',
        label: intl.get(`${promptCode}.locationName`).d('位置名称'),
      },
    ],
    fields: [
      {
        name: 'maintSiteName',
        label: intl.get(`${promptCode}.maintSite`).d('服务区域'),
      },
      {
        name: 'locationTypeName',
        label: intl.get(`${promptCode}.locationType`).d('位置类型'),
      },
      {
        name: 'locationName',
        label: intl.get(`${promptCode}.locationName`).d('位置名称'),
      },
      {
        name: 'locationTitle',
        label: intl.get(`${promptCode}.title`).d('标题'),
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          params,
          url: `${HALM_MDM}/v1/${organizationId}/asset-locations`,
          method: 'GET',
        };
      },
    },
  };
};

const locationModalDS = () => {
  return {
    fields: [
      {
        name: 'address',
        label: intl.get(`${prompt}.modal.address`).d('详细地址'),
      },
      {
        name: 'currentLatLng',
        label: intl.get(`${prompt}.modal.currentLatLng`).d('当前经纬度'),
      },
    ],
  };
};

export { detailCommonFields, detailMoreFields, detailTransport, locationTreeDS, locationModalDS };
