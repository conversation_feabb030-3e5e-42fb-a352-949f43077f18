/**
 * @Description:  坩埚批次查询-入口页
 */
import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from "utils/utils";
import { flow } from 'lodash';
import ExcelExport from "components/ExcelExport";
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { headerTableDS, lineTableDS } from './stores/ListDS';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.crucibleBatchQuery';
const tenantId = getCurrentOrganizationId();

const CrucibleBatchList = props => {
  const { headerTableDs, lineTableDs, customizeTable } = props;

  // 判断头搜索条件切换
  // const [siteId, setSiteId] = useState();

  // 头选中行instructionDocType
  // DS事件监听
  useEffect(() => {
    // listener(true);
    // return function clean() {
    //   listener(false);
    // };
  });

  // 返回页面时恢复选中项和当前项状态
  // useEffect(() => {
  //   if (props?.history?.action === 'PUSH') {
  //     headerTableDs.query(props.headerTableDs.currentPage);
  //     handleLineTableChange({
  //       dataSet: headerTableDs,
  //     });
  //   }
  // }, []);

  // 生成行列表DS查询项
  // const listener = flag => {
  //   // 搜索条件监听
  //   if (headerTableDs.queryDataSet) {
  //     const handler = flag
  //       ? headerTableDs.queryDataSet.addEventListener
  //       : headerTableDs.queryDataSet.removeEventListener;
  //     handler.call(headerTableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
  //   }
  //   // 列表交互监听
  //   if (headerTableDs) {
  //     const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
  //     // 头选中和撤销选中事件
  //     // 列表加载事件
  //     handler.call(headerTableDs, 'load', resetHeaderDetail);
  //     handler.call(headerTableDs, 'batchSelect', handleLineTableChange);
  //     handler.call(headerTableDs, 'batchUnSelect', handleLineTableChange);
  //   }
  // };

  // 头搜索条件切换清空供应商地点
  // const handleQueryDataSetUpdate = ({ record }) => {
  //   const data = record.toData();
  //   if (data.siteId !== siteId) {
  //     setSiteId(data.siteId);
  //     record.set('prodLine', null);
  //   }
  // };

  // 头列表加载
  // const resetHeaderDetail = ({ dataSet }) => {
  //   // 列表刷新清除头单选状态
  //   // 数据正常时用第一条数据查询行数据否则空查
  //   if (dataSet?.current?.toData()) {
  //     headerRowClick(dataSet?.current);
  //   } else {
  //     queryLineTable();
  //   }
  // };

  // 行列表事件, 更新选中行数量
  // const handleLineTableChange = ({ dataSet }) => {
  //   const _selectedStatus = [];
  //   const _printIds = [];
  //   const completedList = ['1_PROCESSING', '1_COMPLETED', '2_PROCESSING', 'COMPLETED'];
  //   dataSet.selected.forEach(item => {
  //     const instructionDocStatus = item?.data?.instructionDocStatus;
  //     _printIds.push(item?.data?.instructionDocId);
  //     if (completedList.indexOf(instructionDocStatus) > -1) {
  //       if (_selectedStatus.indexOf('COMPLETED') === -1) {
  //         _selectedStatus.push('COMPLETED');
  //       }
  //     } else if (_selectedStatus.indexOf(instructionDocStatus) === -1) {
  //       _selectedStatus.push(instructionDocStatus);
  //     }
  //   });
  //   setSelectedStatus(_selectedStatus.length === 1 ? _selectedStatus[0] : undefined);
  //   setPrintIds(_printIds);
  // };

  // 行列表数据查询
  const queryLineTable = data => {
    lineTableDs.setQueryParameter('crucibleUsageId', data?.crucibleUsageId);
    lineTableDs.query();
  };

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'lot',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'quantity',
    },
    {
      name: 'useQty',
    },
    {
      name: 'discardQty',
    },
    {
      name: 'number',
    },
    {
      name: 'avgQty',
    },
    {
      name: 'discardRate',
    },
    {
      name: 'lotCreationDate',
    },
  ];

  // 行信息表配置
  const lineTableColumns = [
    {
      name: 'lot',
    },
    {
      name: 'quantity',
    },
    {
      name: 'instructionDocNum',
    },
    {
      name: 'equipmentName',
    },
    {
      name: 'useQty',
    },
    {
      name: 'discardQty',
    },
    { name: 'stoveCode' },
    { name: 'stoveCount' },
  ];

  const headerRowClick = record => {
    queryLineTable(record?.toData());
  };

  const onFieldEnterDown = () => {
    headerTableDs.query(props.headerTableDs.currentPage);
  };

  const getSearchForm = () => {
    return headerTableDs?.queryDataSet?.current?.toData();
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('坩埚批次查询')}>
        <ExcelExport
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-crucible-batch-query/list/export`}
          queryParams={getSearchForm}
        />
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.Crucible_Batch_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.Crucible_Batch_LIST.HEAD`,
          },
          <Table
            searchCode="wlplcx1"
            customizedCode="wlplcx1"
            dataSet={headerTableDs}
            columns={headerTableColumns}
            highLightRow
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
              onFieldEnterDown,
            }}
            onRow={({ record }) => {
              return {
                onClick: () => {
                  headerRowClick(record);
                },
              };
            }}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineTableDs}
          >
            <Table
              customizedCode="wlplcx2"
              dataSet={lineTableDs}
              highLightRow={false}
              columns={lineTableColumns}
            />
            {/* {lineTableDs && (
              customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.Crucible_Batch_LIST.LINE`,
                },
                <Table
                  customizedCode="wlplcx2"
                  dataSet={lineTableDs}
                  highLightRow={false}
                  columns={lineTableColumns}
                />,
              )
            )} */}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.receive.receiveReturn', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({
        ...headerTableDS(),
        events: {
          query: () => {
            lineTableDs.loadData([]);
          },
        },
      });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.Crucible_Batch_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.Crucible_Batch_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.Crucible_Batch_LIST.LINE`,
    ],
  }),
)(CrucibleBatchList);
