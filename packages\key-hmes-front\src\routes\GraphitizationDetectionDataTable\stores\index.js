import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.tariffDifferAdjustmentPlatform';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: true,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/graphite-report/list/ui`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      name: 'stoveCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stoveCode`).d('炉号'),
      // defaultValue: 'KDA1-0002',
    },
    {
      name: 'creationDateStart',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.creationDateStart`).d('检测时间从'),
      format: 'YYYY-MM-DD HH:mm:ss',
      max: 'creationDateEnd',
    },
    {
      name: 'creationDateEnd',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.creationDateEnd`).d('检测时间至'),
      format: 'YYYY-MM-DD HH:mm:ss',
      min: 'creationDateStart',
    },
  ],
  fields: [
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('检测时间'),
    },
    {
      name: 'stoveCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stoveCode`).d('炉号'),
    },
    {
      name: 'stoveCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stoveCount`).d('炉次'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('样品型号'),
    },
    {
      name: 'sourceObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectCode`).d('批号'),
    },
    {
      name: 'layerLevelDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerLevelDesc`).d('位置描述'),
    },
    {
      name: 'bbInspectValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bbInspectValue`).d('比表'),
    },
    {
      name: 'bbTrueValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bbTrueValue`).d('标准'),
    },
    {
      name: 'smhInspectValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.smhInspectValue`).d('石墨化度'),
    },
    {
      name: 'smhTrueValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.smhTrueValue`).d('标准'),
    },
    {
      name: 'sumWeight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumWeight`).d('装炉重量'),
    },
    {
      name: 'totalConsumption',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.totalConsumption`).d('总电量'),
    },
    {
      name: 'unitConsumption',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unitConsumption`).d('单耗'),
    },
    {
      name: 'reboundTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reboundTime`).d('反弹时间'),
    },
    {
      name: 'staggerTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.staggerTime`).d('错锋'),
    },
    {
      name: 'inspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectResult`).d('判定'),
    },
    {
      name: 'ngInspectResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngInspectResult`).d('不合格项'),
    },
  ],
});


export { tableDS };
