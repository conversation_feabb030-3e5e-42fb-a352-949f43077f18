import { extendParentConfig } from '@hzerojs/plugin-micro';

export default extendParentConfig({
  webpack5: {},
  fastRefresh: {},
  routes: [
    // 每日计划
    {
      path: '/hmes/daily-schedule',
      component: '@/routes/DailySchedule',
    },
    {
      path: '/hmes/material-prevent-error',
      routes: [
        {
          path: '/hmes/material-prevent-error/list',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList',
        },
        {
          path: '/hmes/material-prevent-error/detail/:id',
          component: '../routes/MaterialPreventError/MaterialPreventErrorDetail',
        },
        {
          path: '/hmes/material-prevent-error/import/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
        },
      ],
    },
    // 报工事物明细报表
    {
      path: '/hmes/work-transaction-report/transaction-detail-report',
      component: '@/routes/transactionReport/WorkTransactionDetailReport',
    },
    // 石墨化检测数据表
    {
      path: '/hmes/graphitization-detection-data-table/list',
      component: '@/routes/GraphitizationDetectionDataTable',
    },
    // 尾料产出报表
    {
      path: '/hmes/tail-material-output-report',
      component: '@/routes/TailMaterialOutputReport',
      authorized: true,
    },
    // 抽样方式维护
    {
      path: '/sampling/sampling-method/sampling-mode',
      priority: 10,
      routes: [
        {
          path: '/sampling/sampling-method/sampling-mode/list',
          priority: 10,
          component: '@/routes/samplingMethod/SamplingMode/SamplingModeList',
        },
        {
          path: '/sampling/sampling-method/sampling-mode/detail/:id',
          priority: 10,
          component: '@/routes/samplingMethod/SamplingMode/SamplingModeDetail',
        },
      ],
    },
    // 报工移动事件明细报表
    {
      path: '/hmes/work-transaction-report/mobile-event-detail-report',
      component: '@/routes/transactionReport/WorkMobileEventDetailReport',
    },
    {
      path: '/hmes/assemblyOrderDispatch',
      title: '装配工单派工',
      routes: [
        {
          path: '/hmes/assemblyOrderDispatch',
          component: '@/pages/AssemblyOrderDispatch',
        },
      ],
    },
    {
      path: '/hmes/circulationCardActivate',
      title: '流转卡开卡',
      routes: [
        {
          path: '/hmes/circulationCardActivate/list',
          component: '@/pages/CirculationCardActivate',
        },
        {
          path: '/hmes/circulationCardActivate/detail/:id',
          component: '@/pages/CirculationCardActivate/Detail',
        },
      ],
    },
    // 执行作业管理
    {
      path: '/hmes/workshop/execute-operation-management',
      priority: 10,
      routes: [
        {
          path: '/hmes/workshop/execute-operation-management/list',
          priority: 10,
          component: '@/pages/Execute/ExecuteList',
        },
        {
          path: '/hmes/workshop/execute-operation-management/detail/:id',
          priority: 10,
          component: '@/pages/Execute/ExecuteDetail',
        },
      ],
    },
    // 制造工艺路线-c7n
    {
      path: '/hmes/new/manufacture-process/routes-c7n',
      priority: 10,
      routes: [
        {
          path: '/hmes/new/manufacture-process/routes-c7n/list',
          component: '@/routes/ProcessRouteC7n/index',
          priority: 10,
        },
        {
          path: '/hmes/new/manufacture-process/routes-c7n/dist/:id',
          component: '@/routes/ProcessRouteC7n/detail',
          priority: 10,
        },
      ],
    },
    // 消息处理查询
    {
      path: '/message/message-processing',
      title: '消息处理查询',
      priority: 10,
      component: '@/routes/message/MessageProcessing',
    },
    // 物料工艺路线-c7n
    {
      path: '/hmes/new/process/routes-c7n',
      priority: 10,
      routes: [
        {
          path: '/hmes/new/process/routes-c7n/list',
          component: '@/routes/MaterialProcessRouteC7n/index',
          priority: 10,
        },
        {
          path: '/hmes/new/process/routes-c7n/dist/:id',
          component: '@/routes/MaterialProcessRouteC7n/detail',
          priority: 10,
        },
      ],
    },
    // 工序作业平台
    {
      path: '/hmes/operation-platform',
      authorized: true,
      title: '工序作业平台',
      component: '../routes/OperationPlatform/RoutePage',
      models: '../routes/OperationPlatform/model.ts',
    },
    // 石墨化作业平台
    {
      path: '/hmes/graphitization-operation-platform',
      authorized: true,
      title: '石墨化作业平台',
      component: '../routes/GraphitizationOperationPlatform/RoutePage',
      models: '../routes/GraphitizationOperationPlatform/model.ts',
    },

    // 机加工序作业平台
    {
      authorized: true,
      path: '/pub/hmes/machine-operation-platform',
      routes: [
        {
          authorized: true,
          path: '/pub/hmes/machine-operation-platform/enter',
          component: '../routes/MachineOperationPlatform/EnterModal',
        },
        {
          authorized: true,
          path: '/pub/hmes/machine-operation-platform/list',
          component: '../routes/MachineOperationPlatform',
          models: '../routes/MachineOperationPlatform/model.ts',
        },
      ],
    },
    // 返修作业平台
    {
      path: '/hmes/repair-operation-platform',
      authorized: true,
      routes: [
        {
          path: '/hmes/repair-operation-platform/enter',
          component: '@/routes/RepairOperationPlatform/EnterModal',
        },
        {
          path: '/hmes/repair-operation-platform/list',
          component: '@/routes/RepairOperationPlatform',
          models: '@/routes/RepairOperationPlatform/model.ts',
        },
      ],
    },
    // 机加生产派工
    {
      authorized: true,
      path: '/hmes/machine-production-dispatch',
      component: '@/routes/MachineProductionDispatch',
    },
    // 自制件返修指令创建
    {
      authorized: true,
      path: '/hmes/repair-instruction-created',
      component: '@/routes/RepairInstructionCreated',
    },
    // 工艺维护
    {
      path: '/hmes/process/technology',
      priority: 10,
      routes: [
        {
          path: '/hmes/process/technology/list',
          component: '@/routes/Technology/TechnologyList',
          priority: 10,
        },
        {
          path: '/hmes/process/technology/dist/:id',
          component: '@/routes/Technology/TechnologyDist',
          priority: 10,
        },
      ],
    },
    // 作业指导书管理
    {
      path: '/hmes/working-instruction-maintenance',
      routes: [
        {
          path: '/hmes/working-instruction-maintenance/list',
          component: '../routes/WorkingInstructionMaintenance/WorkingInstructionMaintenanceList',
        },
        {
          path: '/hmes/working-instruction-maintenance/detail/:id',
          component: '../routes/WorkingInstructionMaintenance/WorkingInstructionMaintenanceDetail',
        },
      ],
    },
    // 装配点维护
    {
      path: '/hmes/equipment-point-maintenance',
      routes: [
        {
          path: '/hmes/equipment-point-maintenance/list',
          priority: 10,
          component: '../routes/EquipmentPointMaintenanceNew/EquipmentPointMaintenanceList',
        },
        {
          path: '/hmes/equipment-point-maintenance/detail/:id',
          priority: 10,
          component: '../routes/EquipmentPointMaintenanceNew/EquipmentPointMaintenanceDetail',
        },
      ],
    },
    // 装配组维护
    {
      path: '/hmes/equipment-group-maintenance',
      routes: [
        {
          path: '/hmes/equipment-group-maintenance/list',
          component: '@/routes/EquipmentGroupMaintenance',
        },
        {
          path: '/hmes/equipment-group-maintenance/:id',
          component: '@/routes/EquipmentGroupMaintenance/Create',
        },
      ],
    },
    // 加工策略配置
    {
      path: '/hmes/processing-strategy-configuration',
      component: '@/routes/ProcessingStrategyConfigNew',
    },
    // 检验业务类型规则维护
    {
      path: '/hwms/inspect-business-type-rule',
      routes: [
        {
          path: '/hwms/inspect-business-type-rule/list',
          component: '@/routes/InspectBusTypeRule/InspectBusList',
        },
        {
          path: '/hwms/inspect-business-type-rule/dist/:id',
          component: '@/routes/InspectBusTypeRule/InspectBusDetail',
        },
      ],
    },
    // 采购退货平台
    {
      path: '/hmes/purchase/purchase-return',
      routes: [
        {
          path: '/hmes/purchase/purchase-return/list',
          priority: 10,
          component: '@/routes/PurchaseReturn/PurchaseList',
        },
        {
          path: '/hmes/purchase/purchase-return/detail/:id',
          priority: 10,
          component: '@/routes/PurchaseReturn/PurchaseDetail',
        },
      ],
    },
    // 入库单查询
    {
      path: '/hmes/inbound/inbound-order-query',
      routes: [
        {
          path: '/hmes/inbound/inbound-order-query/list',
          priority: 10,
          component: '@/routes/InboundOrderQuery/InboundOrderQueryList',
        },
      ],
    },
    // 检验单维护
    {
      path: '/hwms/inspect-doc-maintain',
      routes: [
        {
          path: '/hwms/inspect-doc-maintain/list',
          component: '@/routes/InspectDocMaintain/InspectDocList',
        },
        {
          path: '/hwms/inspect-doc-maintain/dist/:id',
          component: '@/routes/InspectDocMaintain/InspectDocDetail',
        },
      ],
    },
    // 检验属性维护功能
    {
      path: '/hmes/product/inspection-attributes',
      routes: [
        {
          path: '/hmes/product/inspection-attributes/list',
          component: '@/routes/InspectionAttributes/InspectionAttributesList',
        },
      ],
    },
    // 用户检验权限维护
    {
      path: '/hwms/user-inspect-permission',
      routes: [
        {
          path: '/hwms/user-inspect-permission/list',
          component: '@/routes/UserInspectPermission',
        },
        {
          path: '/hwms/user-inspect-permission/detail/:id/:siteId',
          component: '@/routes/UserInspectPermission/userDetail',
        },
      ],
    },
    // 产品追溯模型报表
    {
      path: '/hmes/product-traceability-model-report',
      component: '@/routes/ProductTraceabilityModelReport',
    },
    // 检验平台
    {
      path: '/hwms/inspection-platform',
      routes: [
        {
          path: '/hwms/inspection-platform/list',
          component: '@/routes/InspectionPlatform/InspectionPlatformList',
        },
        {
          path: '/hwms/inspection-platform/dist/:id',
          component: '@/routes/InspectionPlatform/InspectionPlatformDetail',
        },
        {
          path: '/hwms/inspection-platform/inspect-doc/:id',
          component: '@/routes/InspectDocMaintain/InspectDocDetail',
        },
      ],
    },
    // 不良记录单管理平台
    {
      path: '/hwms/ncReport-doc-maintain',
      routes: [
        {
          path: '/hwms/ncReport-doc-maintain/list',
          component: '@/routes/NcReportDocMaintain/NcReportList',
        },
        {
          path: '/hwms/ncReport-doc-maintain/dist/:id',
          component: '@/routes/NcReportDocMaintain/NcReportDetail',
        },
      ],
    },
    {
      path: '/hwms/summary-nonconformity-review-details/list',
      authorized: true,
      component: '@/routes/SummaryNonconformityReviewDetails',
    },
    // 检验方案维护
    {
      path: '/hwms/inspection-scheme-maintenance',
      routes: [
        {
          path: '/hwms/inspection-scheme-maintenance/list',
          component: '@/routes/InspectionScheme/InspectionSchemeList',
        },
        {
          path: '/hwms/inspection-scheme-maintenance/detail/:id',
          component: '@/routes/InspectionScheme/InspectionSchemeDetail',
        },
      ],
    },
    // 处置方法维护
    {
      path: '/hmes/bad/disposition-method',
      priority: 999,
      component: '@/routes/DispositionMethod/DispositionMethodList',
    },
    // 报检信息管理平台
    {
      path: '/hwms/inspection-info-management',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-info-management/list',
          component: '@/routes/InspectionInfoManagement/List',
          priority: 10,
        },
      ],
    },
    // 超期报表
    {
      path: '/hmes/report/over-due',
      priority: 10,
      component: '@/routes/report/OverDue',
    },
    // 车间电能查询报表
    {
      path: '/hmes/report/workshop-energy-query',
      component: '@/routes/report/WorkshopEnergyQuery',
    },
    // 物料批管理平台
    {
      path: '/hmes/product/material-lot-traceability',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/material-lot-traceability/list/:timer?',
          component: '@/routes/product/MaterialLotTrace',
          priority: 10,
        },
        {
          path: '/hmes/product/material-lot-traceability/detail/:id',
          component: '@/routes/product/MaterialLotTrace/MaterialLotTraceDetail',
          priority: 10,
        },
        {
          path: '/hmes/product/material-lot-traceability/import/:code',
          component: '@/pages/Import/CommentImport',
          priority: 10,
        },
      ],
    },
    // 销售订单管理
    {
      path: '/hmes/so-delivery/sell-order-manage',
      priority: 10,
      routes: [
        {
          path: '/hmes/so-delivery/sell-order-manage/list',
          component: '@/routes/soDelivery/SellOrder',
          priority: 10,
        },
        {
          path: '/hmes/so-delivery/sell-order-manage/detail/:id',
          component: '@/routes/soDelivery/SellOrder/SellOrderDetail',
        },
      ],
    },
    // 呆滞报表
    {
      path: '/hmes/sluggish-report',
      component: '@/routes/sluggishReport/SluggishReport',
    },
    // 报检请求管理平台
    {
      path: '/hmes/inspection/inspection-management-new',
      authorized: true,
      routes: [
        {
          path: '/hmes/inspection/inspection-management-new/list',
          component: '@/routes/inspection/InspectionManagement',
        },
      ],
    },
    // 装配记录查询
    {
      priority: 10,
      path: '/hmes/assembly-record-query',
      component: '@/routes/AssemblyRecordQuery',
    },
    // 报工事务报表平台
    {
      priority: 10,
      path: '/hmes/work-transaction-report/platform',
      component: '@/routes/transactionReport/WorkTransactionReportPlatform',
    },
    // 盘点工作台
    {
      path: '/hmes/inventory/inventory-workbench',
      priority: 10,
      routes: [
        {
          path: '/hmes/inventory/inventory-workbench/list',
          component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchList',
          priority: 10,
        },
        {
          path: '/hmes/inventory/inventory-workbench/detail/:id',
          component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchDetail',
          priority: 10,
        },
      ],
    },
    // 站点维护
    {
      path: '/hmes/organization-modeling/site',
      priority: 10,
      routes: [
        {
          path: '/hmes/organization-modeling/site/list',
          component: '@/routes/org/Site/SiteList',
          priority: 10,
        },
        {
          path: '/hmes/organization-modeling/site/detail/:siteId',
          component: '@/routes/org/Site/SiteDetail',
          priority: 10,
        },
      ],
    },
    // 库位维护
    {
      path: '/hmes/organization-modeling/locator',
      priority: 10,
      routes: [
        {
          path: '/hmes/organization-modeling/locator/list',
          component: '@/routes/org/Locator/LocatorList',
          priority: 10,
        },
        {
          path: '/hmes/organization-modeling/locator/detail/:locatorId',
          component: '@/routes/org/Locator/LocatorDetail',
          priority: 10,
        },
      ],
    },
    // 检验项目维护
    {
      path: '/hwms/inspect-item-maintain',
      priority: 999,
      routes: [
        {
          path: '/hwms/inspect-item-maintain/list',
          priority: 999,
          component: '@/routes/InspectItemMaintain/InspectItemList',
        },
        {
          path: '/hwms/inspect-item-maintain/dist/:id',
          priority: 999,
          component: '@/routes/InspectItemMaintain/InspectItemDetail',
        },
      ],
    },
    // 物料维护
    {
      priority: 10,
      path: '/hmes/product/material-manager',
      routes: [
        {
          priority: 10,
          path: '/hmes/product/material-manager/list',
          component: '@/routes/product/Material/MaterialList',
        },
        {
          priority: 10,
          path: '/hmes/product/material-manager/dist/:id',
          component: '@/routes/product/Material/MaterialDetail',
        },
        {
          priority: 10,
          path: '/hmes/product/material-manager/site-assignment/:id',
          component: '@/routes/product/Material/MaterialSiteAssignment',
        },
      ],
    },
    // 组织关系维护
    {
      path: '/hmes/organization-modeling/relation-maintenance',
      component: '@/routes/org/RelationMaintain',
      model: '@/models/org/relationMaintain',
      priority: 10,
    },
    // 打印模板配置
    {
      path: '/hmes/print-template-configuration',
      authorized: true,
      routes: [
        {
          path: '/hmes/print-template-configuration/list',
          component: '@/routes/PrintTemplateConfiguration/index.js',
          authorized: true,
        },
        {
          path: '/hmes/print-template-configuration/detail/:templateId',
          component: '@/routes/PrintTemplateConfiguration/detail.js',
          authorized: true,
        },
      ],
    },
    // 事务类型接口关系
    {
      path: '/hmes/event-type-interface-relationship',
      component: '@/routes/EventTypeInterfaceRelationship/index',
      authorized: true,
    },
    // 产品加工履历查询报表
    {
      path: '/hmes/product-processing-history-query-report',
      component: '@/routes/ProductProcessingHistoryQueryReport',
    },
    // 装炉图平台
    {
      path: '/hmes/furnace-loading-diagram-platform',
      routes: [
        {
          path: '/hmes/furnace-loading-diagram-platform/list',
          component: '@/routes/FurnaceLoadingDiagramPlatform',
        },
        {
          path: '/hmes/furnace-loading-diagram-platform/detail/:id',
          component: '@/routes/FurnaceLoadingDiagramPlatform/Detail',
        },
      ],
    },
    // 容器管理平台
    {
      path: '/hmes/product/container-management-platform',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/container-management-platform/list',
          component: '@/routes/product/ContainerManagePlatform',
          priority: 10,
        },
      ],
    },

    // 杂项工作台
    {
      path: '/hwms/in-library/miscellaneous',
      routes: [
        {
          path: '/hwms/in-library/miscellaneous/list',
          component: '@/routes/inLibrary/miscellaneous',
        },
        {
          path: '/hwms/in-library/miscellaneous/detail/:id',
          component: '@/routes/inLibrary/miscellaneous/Detail',
        },
      ],
    },
    {
      path: '/hmes/inventory/so-stock-transfer',
      title: '供应商库存转移',
      routes: [
        {
          path: '/hmes/inventory/so-stock-transfer/list',
          component: '@/routes/inventory/SOStockTransfer/SOStockTransferList',
        },
      ],
    },
    // 新采购订单管理
    {
      path: '/hmes/purchase/order-management',
      priority: 10,
      routes: [
        {
          path: '/hmes/purchase/order-management/list',
          component: '@/routes/purchase/OrderNew',
          priority: 10,
        },
      ],
    },

    // 库存调拨平台
    {
      path: '/hwms/in-library/send-receive-doc-new',
      routes: [
        {
          path: '/hwms/in-library/send-receive-doc-new/list',
          component: '@/routes/inLibrary/SendReceiveDoc/SendReceiveList',
        },
        {
          path: '/hwms/in-library/send-receive-doc-new/detail/:id',
          component: '@/routes/inLibrary/SendReceiveDoc/SendReceiveDetail',
        },
      ],
    },

    // 库存初始化
    {
      path: '/hmes/inventory/initial',
      priority: 10,
      component: '@/routes/inventory/InventoryInitial',
    },

    // 煅后焦检测数据表
    {
      path: '/hmes/post-calcined-test-data-report',
      component: '@/routes/PostCalcinedTestDataReport',
    },

    // 事物报表平台
    {
      path: '/hmes/transaction-report/platform',
      priority: 10,
      component: '@/routes/transactionReport/TransactionReportPlatform',
    },

    // 事物明细报表
    {
      path: '/hmes/transaction-report/transaction-detail-report',
      priority: 10,
      component: '@/routes/transactionReport/TransactionDetailReport',
    },

    // 外协管理平台
    {
      path: '/hmes/purchase/outsourcing-manage',
      priority: 10,
      routes: [
        {
          path: '/hmes/purchase/outsourcing-manage/list',
          component: '@/routes/purchase/Outsourcing/OutsourcingList/index',
          priority: 10,
        },
        // 创建外协发料单
        {
          path: '/hmes/purchase/outsourcing-manage/outsourcing-doc/create',
          component: '@/routes/purchase/Outsourcing/OrderCreate',
          priority: 10,
        },
        // 创建外协退料单
        {
          path: '/hmes/purchase/outsourcing-manage/outsourcing-returns/create',
          component: '@/routes/purchase/Outsourcing/OutsourcingReturn/index',
          priority: 10,
        },
        // 创建外协补料单
        {
          path: '/hmes/purchase/outsourcing-manage/supplement/:id',
          component: '@/routes/purchase/Outsourcing/SupplementBill/index',
          priority: 10,
        },
      ],
    },

    // 送货单管理
    {
      path: '/hmes/purchase/delivery-management',
      priority: 10,
      routes: [
        {
          path: '/hmes/purchase/delivery-management/list',
          component: '@/routes/purchase/Delivery',
          priority: 10,
        },
        {
          path: '/hmes/purchase/delivery-management/detail/:id',
          component: '@/routes/purchase/Delivery/Detail',
          priority: 10,
        },
      ],
    },

    // 静默期维护
    {
      path: '/hwms/silent-period-maintenance',
      component: '@/routes/SilentPeriodMaintenance',
    },

    // 销售发运平台
    {
      path: '/hwms/so-delivery/so-delivery-platform',
      routes: [
        {
          path: '/hwms/so-delivery/so-delivery-platform/list',
          component: '@/routes/soDelivery/SoDeliveryPlatform/SoDeliveryList',
        },
        {
          path: '/hwms/so-delivery/so-delivery-platform/detail/:id',
          component: '@/routes/soDelivery/SoDeliveryPlatform/SoDeliveryDetail',
        },
      ],
    },

    // 领退料平台
    {
      path: '/hwms/receive/receive-return',
      routes: [
        {
          path: '/hwms/receive/receive-return/list',
          component: '@/routes/receive/ReceiveReturn',
        },
        {
          path: '/hwms/receive/receive-return/detail/:id/:docType/:docTypeTag',
          component: '@/routes/receive/ReceiveReturn/Detail',
        },
      ],
    },
    // 用户权限维护
    {
      path: '/hmes/mes/user-rights',
      priority: 10,
      component: '@/routes/hmes/UserRights',
    },
    // 工艺与工作单元维护
    {
      path: '/hmes/process/unit-work',
      priority: 1000,
      component: '@/routes/process/UnitWork',
    },
    // 工作单元维护
    {
      path: '/hmes/organization-modeling/work-cell',
      priority: 1000,
      routes: [
        {
          priority: 1000,
          path: '/hmes/organization-modeling/work-cell/list',
          component: '@/routes/org/WorkCell/WorkCellList',
        },
        {
          priority: 1000,
          path: '/hmes/organization-modeling/work-cell/detail/:workcellId',
          component: '@/routes/org/WorkCell/WorkCellDetail',
        },
      ],
    },
    // 检验数据展示报表
    {
      path: '/hmes/check-presentation-report',
      component: '@/routes/CheckPresentationReport',
      authorized: true,
    },
    // 工单管理平台
    {
      path: '/hmes/workorder-management-platform',
      routes: [
        {
          title: '工单管理平台',
          path: '/hmes/workorder-management-platform/list',
          component: '@/routes/WorkOrderManagementPlatform',
        },
      ],
    },
    // 数据收集项维护
    {
      path: '/hmes/acquisition/new-data-item',
      priority: 10,
      routes: [
        {
          path: '/hmes/acquisition/new-data-item/list',
          priority: 10,
          component: '@/routes/acquisition/NewDataItem',
        },
        {
          path: '/hmes/acquisition/new-data-item/detail/:id',
          priority: 10,
          component: '@/routes/acquisition/NewDataItem/Detail',
        },
      ],
    },
    // 数据收集组维护
    {
      path: '/hmes/acquisition/data-collection',
      priority: 10,
      routes: [
        {
          path: '/hmes/acquisition/data-collection/list',
          priority: 10,
          component: '@/routes/acquisition/Collection/CollectionList',
        },
        {
          path: '/hmes/acquisition/data-collection/detail/:id',
          priority: 10,
          component: '@/routes/acquisition/Collection/CollectionDetail',
        },
      ],
    },
    /**
     * 我的服务申请
     */
    // {
    //   path: '/amtc/service-apply-current',
    //   priority: 10,
    //   routes: [
    //     // 我的服务申请列表
    //     {
    //       path: '/amtc/service-apply-current/list',
    //       component: '@/pages/ServiceApplyCurrent',
    //       priority: 10,
    //     },
    //     // 我的服务申请新建
    //     {
    //       path: '/amtc/service-apply-current/create',
    //       models: ['@/models/serviceApply', '@/models/woMalfunction', '@/models/frameWork'],
    //       component: '@/pages/ServiceApplyCurrent/Detail',
    //       priority: 10,
    //     },
    //     // 我的服务申请编辑
    //     {
    //       path: '/amtc/service-apply-current/detail/:srId',
    //       models: ['@/models/serviceApply', '@/models/woMalfunction', '@/models/frameWork'],
    //       component: '@/pages/ServiceApplyCurrent/Detail',
    //       priority: 10,
    //     },
    //   ],
    // },
    /**
     * 标准作业
     */
    {
      path: '/amtc/act',
      priority: 10,
      routes: [
        // 标准作业列表
        {
          path: '/amtc/act/list',
          models: ['@/models/frameWork'],
          component: '@/pages/Act',
          priority: 10,
        },
        // 导入
        {
          authorized: true,
          path: '/ammt/act/data-import/:code',
          component: '@/pages/Import/CommentImport',
          priority: 10,
        },
        // 标准作业创建
        {
          path: '/amtc/act/create',
          models: ['@/models/frameWork'],
          component: '@/pages/Act/Detail',
          priority: 10,
        },
        // 标准作业详情
        {
          path: '/amtc/act/detail/:id',
          models: ['@/models/frameWork'],
          component: '@/pages/Act/Detail',
          priority: 10,
        },
      ],
    },
    // 送电曲线模板维护
    {
      path: '/hmes/power-curve-template-maintain',
      routes: [
        {
          path: '/hmes/power-curve-template-maintain/list',
          component: '@/routes/PowerCurveTemplateMaintain',
        },
        {
          path: '/hmes/power-curve-template-maintain/:id',
          component: '@/routes/PowerCurveTemplateMaintain/Create',
        },
      ],
    },
    // 送电曲线查询平台
    {
      path: '/hmes/power-curve-template-query',
      routes: [
        {
          path: '/hmes/power-curve-template-query/list',
          component: '@/routes/PowerCurveTemplateQuery',
        },
        {
          path: '/hmes/power-curve-template-query/:id',
          component: '@/routes/PowerCurveTemplateQuery/Create',
        },
      ],
    },
    // 坩埚批次查询
    {
      path: '/hmes/crucible-batch-query',
      component: '@/routes/CrucibleBatchQuery',
    },
    // 装炉图模板定义
    {
      path: '/hmes/load-draw-template',
      component: '@/routes/LoadDrawTemplate',
    },
    // 电价维护平台
    {
      path: '/hmes/price-maintenance-platform',
      component: '@/routes/PriceMaintenancePlatform',
    },
    // 计划产销量功能维护
    {
      path: '/hmes/plan-sales/list',
      component: '@/routes/PlanSalesList',
    },
    // 参数采集报表
    {
      path: '/hmes/parameter-collection-report',
      component: '@/routes/ParameterCollectionReport',
    },
    // 完工报表
    {
      path: '/hmes/complete-report',
      component: '@/routes/CompleteReport',
    },
    // 增碳剂检测数据表
    {
      path: '/hmes/carburant-detection-report',
      component: '@/routes/CarburantDetectionReport',
    },
    // 电费差异调整平台
    {
      path: '/hmes/tariff-differ-adjustment-platform',
      component: '@/routes/TariffDifferAdjustmentPlatform',
    },
    // 无价值坩埚报表
    {
      path: '/hmes/no-value-crucible-report',
      component: '@/routes/NoValueCrucibleReport',
    },
    {
      path: '/hmes/workshop/production-order-mgt',
      priority: 10,
      routes: [
        {
          path: '/hmes/workshop/production-order-mgt/list',
          priority: 10,
          component: '@/routes/workshop/ProductionOrderMgt/ProductionOrderMgtList',
        },
        {
          path: '/hmes/workshop/production-order-mgt/detail/:id',
          priority: 10,
          component: '@/routes/workshop/ProductionOrderMgt/ProductionOrderMgtDetail',
        },
      ],
    },
    // 投入产出报表
    {
      path: '/hmes/input-output-report',
      component: '@/routes/InputOutputReport',
    },
    // 编码规则维护功能
    {
      path: '/hmes/mes/maintain-number',
      priority: 10,
      routes: [
        // {
        //   path: '/hmes/mes/maintain-number/list',
        //   component: '@/routes/hmes/MaintainNumber/MaintainNumberList',
        //   model: '@/models/hmes/maintainNumber',
        //   priority: 10,
        // },
        {
          path: '/hmes/mes/maintain-number/detail/:id',
          component: '@/routes/hmes/MaintainNumber/MaintainNumberDetail',
          model: '@/models/hmes/maintainNumber',
          priority: 10,
        },
      ],
    },
    // 设备接口调用记录
    {
      path: '/hmes/device-interface-call-record',
      routes: [
        {
          path: '/hmes/device-interface-call-record/list',
          component: '../routes/DeviceInterface',
        },
      ],
    },
    {
      path: '/hmes/inventory/journal/query',
      component: '@/routes/inventory/JournalQuery/JournalQueryList',
      priority: 10,
    },
    {
      path: '/hmes/graphite-schedule-report',
      component: '@/routes/GraphiteScheduleReport',
    },    
    // COA基础数据维护
    {
      path: '/hmes/coa-basic-list',
      component: '@/routes/CoaBasicList',
      authorized: true,
    },
    // 产销日报表
    {
      path: '/hmes/production-and-sales-daily-report',
      component: '@/routes/ProductionAndSalesDailyReport',
    },
    // 制程/成品检验看板
    {
      path: '/hmes/final-inspection-dashboard',
      component: '@/routes/FinalInspectionDashboard',
    },
    // 来料检验管理看板
    {
      // authorized: true,
      path: '/hmes/income-inspection-management',
      component: '@/routes/IncomeInspectionManagement',
    },


    // 制造装配清单
    {
      path: '/hmes/product/manufacture-list',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/manufacture-list/list',
          component: '@/routes/product/C7nManufactureBom/index',
          priority: 10,
        },
        {
          path: '/hmes/product/manufacture-list/dist/:id',
          component: '@/routes/product/C7nManufactureBom/detail',
          priority: 10,
        },
      ],
    },
    // 生产日报表
    {
      path: '/hmes/production-daily-report',
      component: '@/routes/report/ProductionDailyReport',
    },
    // 送电任务用电量报表
    {
      path: '/hmes/power-transmission-task-report',
      component: '@/routes/PowerTransmissionTaskReport',
    },
  ],
  hash: true,
  hzeroMicro: {
    // microConfig: {
    //   registerRegex: '\\/.*',
    // },
  },
  // 如果存在发布 lib 包需求,可以解开该配置，对应 babelrc 中的内容
  // 注意若父模块与子模块都配置了module-resolver插件,请保证数组的第三个参数不能为同一个字符串或者都为空
  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          '@': './src',
        },
      },
    ],
  ],
});
