import React, { useMemo } from 'react';
import { Tooltip } from 'choerodon-ui/pro';
import DashboardCard from '../DashboardCard.jsx';
import ScrollBoard from '../ScrollBoard.jsx';
import styles from '../../index.module.less';

const EmergencyTask = ({data}) => {
  const tableData = [];
  if (data.length) {
    data.forEach((val) => {
      const {
        siteName,
        inspectBusinessTypeDesc,
        inspectInfoCreationDate,
        materialName,
        supplierNameAlt,
      } = val;
      tableData.push([
        `<Tooltip title='${siteName}'><span>${siteName}</span></Tooltip>`,
        `<Tooltip title='${inspectBusinessTypeDesc}'><span>${inspectBusinessTypeDesc}</span></Tooltip>`,
        `<Tooltip title='${inspectInfoCreationDate}'><span>${inspectInfoCreationDate}</span></Tooltip>`,
        `<Tooltip title='${materialName}'><span>${materialName}</span></Tooltip>`,
        `<Tooltip title='${supplierNameAlt}'><span>${supplierNameAlt}</span></Tooltip>`,
      ]);
    });
  }
  const config = useMemo(
    () => ({
      header: ['基地名称', '任务类型', '报检时间', '物料描述', '供应商'],
      data: tableData,
      // data: [
      //   ['01', 'S123123123', '物料名称', '到货时间', '采购员', '异常原因'],
      // ],
      rowNum: 7,
      align: ['center'],
      oddRowBGC: 'rgba(22,66,127,0.3)',
      headerBGC: 'rgb(3, 157, 206,0.3)',
      evenRowBGC: 'rgba(3,28,60, 0.3)',
      headerHeight: 40,
      columnWidth: [120, 100, 110, 100, 90],
    }),
    [tableData],
  );
  return (
    <DashboardCard height="100%">
      <div style={{ width: '100%', height: '100%' }}>
        <div className={styles['my-scroll-board-title']}>
        紧急检验任务
        </div>
        <div className={styles['my-scroll-board-table']}>
          <ScrollBoard config={config} style={{ width: '100%', height: '100%', marginLeft: '10px' }} />
        </div>
      </div>
    </DashboardCard>
  );
};

export default EmergencyTask;