import React, { useEffect, useRef, useState, useMemo } from 'react';
import { ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { DataSet, Select, YearPicker,MonthPicker,DateTimePicker } from 'choerodon-ui/pro';
import DashboardCard from '../DashboardCard.jsx';
import styles from '../../index.module.less';
import Chart from '../NotPassProfile/chart';
import {filterDS}  from '../../Stores';

const CpChart = ({timers}) => {
  const filterDs = useMemo(() => new DataSet(filterDS()), []);
  const [timeDimension, setTimeDimension] = useState<any>();
  const [filterData, setFilterData] = useState<any>();  
  const filterDataRef= useRef<any>();

  useEffect(() => {
    const time =setInterval(() => {
      setQueryParam();
    }, (timers)*60000)
    filterDs.addEventListener('update', handleUpdate);
    return () => {
      clearTimeout(time)
      filterDs.removeEventListener('update', handleUpdate);
    };
  }, [timers]);

  const handleUpdate = ({record, name,value}) => {
    if(name === 'timeDimension' && value ){
      record.set('timeDimension',value);
      record.set('year',null);
      record.set('month',null);
      record.set('date',null);
      record.set('season',null);
      record.set('week',null);
      setFilterData(filterDs.toData()[0]);
    }
    if((name === 'year'||name === 'month'||name === 'date' ||name === 'season'|| name==="week") && value){
      setQueryParam();
    }
  }
  const setQueryParam =()=>{
    setFilterData(filterDs.toData()[0]);
    const {timeDimension, timeDimensionMeaning, year, month, date,season,week} = filterDs.toData()[0];
    const params ={
      timeDimension: timeDimensionMeaning|| (filterDs?.current?.getField('timeDimension')?.getLookupData(timeDimension)?.meaning)?.charAt(0),
      year,
      date: timeDimension === '1'? year : timeDimension === '2' ? month:
        timeDimension === '3' ?date:timeDimension === '4'?season: `${`${month}/${week}`}`,
    };
    filterDataRef.current=params;
  }
  const onFilterChange = (e) => {
    console.log('eeeee', e);
    setTimeDimension(e);
  };

  return (
    <DashboardCard height="100%">
      <div style={{ width: '100%', height: '100%', display: 'flex' }}>
        <div className={styles['my-bottom-right-chart']}> 
          <div className={styles['my-bottom-chart-title']}>
           成品工序不良分布图
          </div>
          <div className={styles['my-chart-filter']}>
            <div className={styles['container-inventory-select']}>
              <Select
                dataSet={filterDs}
                style={{backgroundColor: 'rgba(0,0,0,0)'}}
                name="timeDimension"
                onChange={onFilterChange}
                showValidation={ShowValidation.tooltip}
              />
              {timeDimension === '1' ? (
                <YearPicker name="year" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
              ) : timeDimension === '2' ? (
                <MonthPicker name="month" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
              ) : timeDimension === '3' ? (
                <DateTimePicker name="date"  dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                
              ) : timeDimension === '4' ? (
                <><YearPicker name="year" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                  <Select name="season" 
                    style={{backgroundColor: 'rgba(0,0,0,0)'}}
                    dataSet={filterDs} />
                </>
              ):
                <><MonthPicker name="month" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                  <Select name="week" 
                    style={{backgroundColor: 'rgba(0,0,0,0)'}}
                    dataSet={filterDs} />
                </>
              }
            </div>
          </div>
          <div className={styles['my-chart']}>
            {filterData &&
            <Chart filterDataRef={filterDataRef} operation='CP'/>
            }
          </div>
        </div>
      </div>
    </DashboardCard>
  );
};
export default CpChart;
