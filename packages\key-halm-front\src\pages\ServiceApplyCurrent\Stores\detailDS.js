/**
 * @since 2020/12/01
 * <AUTHOR> <kejie.lu@hand-china>
 */
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { expandOrShrinkAll } from 'alm/utils/utils';
import { HALM_MTC } from 'alm/utils/config';
import {
  detailCommonFields,
  detailMoreFields,
  detailTransport,
  locationTreeDS,
  locationModalDS,
} from '../../ServiceApply/Stores/detailDS';

const organizationId = getCurrentOrganizationId();

const prompt = 'amtc.serviceApply';
const promptCode = `${prompt}.model.serviceApply`;

const fields = evalHierarchyList => {
  return [...detailCommonFields(), ...detailMoreFields(evalHierarchyList)];
};

function detailFormDS(evalHierarchyList) {
  return {
    autoQuery: false,
    fields: fields(evalHierarchyList),
    transport: detailTransport,
    events: {
      update: ({ record, name }) => {
        if (
          evalHierarchyList &&
          evalHierarchyList.length > 0 &&
          name === evalHierarchyList[0].code
        ) {
          const evalLov1 = record.get(evalHierarchyList[0].code);
          record.set(evalHierarchyList[1].code, {
            asmtCodeId: null,
            asmtCodeName: null,
          });
          if (evalLov1 && evalLov1.asmtCodeName) {
            record.set('description', `${evalLov1.asmtCodeName}`);
          } else {
            record.set('description', null);
          }
        } else if (
          evalHierarchyList &&
          evalHierarchyList.length > 1 &&
          name === evalHierarchyList[1].code
        ) {
          const evalLov1 = record.get(evalHierarchyList[0].code);
          const evalLov2 = record.get(evalHierarchyList[1].code);
          if (evalLov2?.asmtCodeName) {
            if (evalLov1?.asmtCodeName) {
              record.set('description', `${evalLov1.asmtCodeName}/${evalLov2.asmtCodeName}`);
            } else {
              record.set('description', `${evalLov2.asmtCodeName}`);
            }
          } else if (evalLov1 && evalLov1.asmtCodeName) {
            record.set('description', `${evalLov1.asmtCodeName}`);
          } else {
            record.set('description', null);
          }
        }
      },
    },
  };
}

// 故障DS
function malfunctionDS(onChangeSelectNode) {
  return {
    autoQuery: false,
    primaryKey: 'asmtCodeId',
    selection: 'single',
    idField: 'asmtCodeId',
    parentField: 'parentCodeId',
    expandField: 'expand',
    fields: [
      {
        name: 'asmtCodeName',
        type: 'string',
        label: intl.get(`${promptCode}.faultSet`).d('故障集'),
      },
    ],
    transport: {
      read: ({ params, data }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/asmt-codes/${data.evalItemId}`,
          method: 'GET',
          params: {
            ...params,
            tenantId: organizationId,
          },
          transformResponse: res => {
            const originData = JSON.parse(res);
            const content = originData.content || [];
            const topList = content.map(item => {
              return {
                ...item,
                children: item.lowerFlag === 'Y' ? [] : null,
                childFlag: item.lowerFlag === 'Y',
              };
            });
            const newContent = expandOrShrinkAll({
              type: 'expand',
              data: topList,
              parentField: 'parentCodeId',
              idField: 'asmtCodeId',
            });
            return {
              ...originData,
              content: newContent,
            };
          },
        };
      },
    },
    events: {
      select: ({ record }) => {
        onChangeSelectNode(record, true);
      },
      unSelect: ({ record }) => {
        onChangeSelectNode(record, false);
      },
    },
  };
}

export { detailFormDS, locationTreeDS, locationModalDS, malfunctionDS };
