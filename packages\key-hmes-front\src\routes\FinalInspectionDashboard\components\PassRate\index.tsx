import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import { ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { DataSet, Select, YearPicker,MonthPicker,DateTimePicker } from 'choerodon-ui/pro';
import axios from 'axios';
import { BASIC } from '@utils/config';
import DashboardCard from '../DashboardCard.jsx';
import {filterDS}  from '../../Stores';
import styles from '../../index.module.less';

const tenantId = getCurrentOrganizationId();
// 制程合格率趋势图URL
const url = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qms-oper-inspects/pass-rate`;

const PassRate = ({timers}) => {
  const chartRef:any = useRef(null);
  const filterDs = useMemo(() => new DataSet(filterDS()), []);
  const [timeDimension, setTimeDimension] = useState<any>();
  const [xData, setXData] = useState<any>();
  const [fsData, setFsData] = useState<any>();
  const [rbData, setRbData] = useState<any>();
  const [cpData, setCpData] = useState<any>();

  useEffect(() => {
    const time = setInterval(() => {
      fetchData();
    }, (timers)*60000)
    return () => {
      clearTimeout(time)
    }
  }, [timers]);

  useEffect(() => {
    filterDs.addEventListener('update', handleUpdate);
    return () => {
      filterDs.removeEventListener('update', handleUpdate);
    };
  }, [xData]);
  
  const fetchData = useCallback(async () => {
    const {timeDimension, timeDimensionMeaning, year, month, date,season,week} = filterDs.toData()[0];
    const params ={
      timeDimension: timeDimensionMeaning||(filterDs?.current?.getField('timeDimension')?.getLookupData(timeDimension)?.meaning)?.charAt(0),
      year,
      date: timeDimension === '1'? year : timeDimension === '2' ? month:
        timeDimension === '3' ?date:timeDimension === '4'?season: `${`${month}/${week}`}`,
    };
    const res = await axios.post<any, any>(url,params);
    const xTemp:any=[];
    const fTemp:any=[];
    const rTemp:any=[];
    const cTemp:any=[];
    if(res?.length){      
      res.forEach((i)=>{
        xTemp.push(i.dateVar);
        fTemp.push(i.fsData);
        rTemp.push(i.rbData);
        cTemp.push(i.cpData);
      })
    }    
    setXData(xTemp);
    setFsData(fTemp);
    setRbData(rTemp);
    setCpData(cTemp);
  }, []);
  
  const option = useMemo(() => {   
    return {
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        left: '4%',
        right: '4%',
        bottom:'21%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisTick: { show: false },// 坐标刻度是否显示
        data: xData,
        // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        axisLine: { // 控制轴线样式
          lineStyle: {
            color: '#fff', // 设置轴线颜色
          },
        },
        axisLabel: { // 控制轴标签样式
          textStyle: {
            color: '#fff', // 设置轴标签文字颜色
          },
        },
      },
      yAxis: {
        type: 'value',
        axisTick: { show: false },// 坐标刻度是否显示
        splitLine: {show:false}, // 是否显示背景分隔线
        axisLine: { // 控制轴线样式
          lineStyle: {
            color: '#fff', // 设置轴线颜色
          },
        },
        axisLabel: { // 控制轴标签样式
          textStyle: {
            color: '#fff', // 设置轴标签文字颜色
          },
        },
      },
      legend: {
        data: ['粉碎工序', '热包工序', '成品工序'],
        right: '5%',
        top: '5%',
        itemWidth: 14,
        itemHeight: 14,
        textStyle: {
          color: '#fff',
        },
      },
      series: [
        {
          name: '粉碎工序',
          type: 'line',
          // data: [0, 132, 101, 134, 90, 230, 210],
          data: fsData,         
          // smooth: true,
          color:'#0077ff',
          label: {
            show: true,
            position: 'top',
          },
        },
        {
          name: '热包工序',
          type: 'line',
          // data: [220, 182, 191, 234, 290, 330, 310],          
          data:rbData,  
          // smooth: true,
          color:'#FFA126',
          label: {
            show: true,
            position: 'top',
          },
        },          
        {
          name: '成品工序',
          type: 'line',
          // data: [20, 12, 161, 84, 70, 30, 100],          
          data:cpData,  
          // smooth: true,
          color:'#00FFF4',
          label: {
            show: true,
            position: 'top',
          },
        },
      ],
    };
  }, [xData]);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [xData]);

  const handleUpdate = ({record, name,value,oldValue}) => {
    if(name === 'timeDimension' && value && value!==oldValue){
      record.set('timeDimension',value);
      record.set('timeDimensionMeaning',null);
      record.set('year',null);
      record.set('month',null);
      record.set('date',null);
      record.set('season',null);
      record.set('week',null);
    }
    if((name === 'year'||name === 'month'||name === 'date' ||name === 'season'|| name==="week") && value && value!==oldValue){
      record.set('timeDimensionMeaning',null);
      fetchData();
    }
  }

  const onFilterChange = (e) => {
    setTimeDimension(e);
  };

  return (
    <DashboardCard height="100%">
      <div style={{ width: '100%', height: '100%' }}>
        <div className={styles['my-top-chart']}> 
          <div className={styles['my-scroll-board-title']}>
           制程合格率趋势
          </div>
          <div className={styles['my-chart-filter']}>
            <div className={styles['container-inventory-select']}>
              <Select
                dataSet={filterDs}
                style={{backgroundColor: 'rgba(0,0,0,0)'}}
                name="timeDimension"
                onChange={onFilterChange}
                showValidation={ShowValidation.tooltip}
              />
              {timeDimension === '1' ? (
                <YearPicker name="year" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
              ) : timeDimension === '2' ? (
                <MonthPicker name="month" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
              ) : timeDimension === '3' ? (
                <DateTimePicker name="date"  dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                
              ) : timeDimension === '4' ? (
                <><YearPicker name="year" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                  <Select name="season" 
                    style={{backgroundColor: 'rgba(0,0,0,0)'}}
                    dataSet={filterDs} />
                </>
              ):
                <><MonthPicker name="month" dataSet={filterDs} style={{backgroundColor: 'rgba(0,0,0,0)'}}/>
                  <Select name="week" 
                    style={{backgroundColor: 'rgba(0,0,0,0)'}}
                    dataSet={filterDs} />
                </>
              }
            </div>
          </div>
          {xData?.length &&   
            <div className={styles['my-chart']}>
              <div style={{ width: '100%', height: '100%' }}>
                <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
              </div>
            </div>
          }
        </div>
      </div>
    </DashboardCard>
  );
};
export default PassRate;
