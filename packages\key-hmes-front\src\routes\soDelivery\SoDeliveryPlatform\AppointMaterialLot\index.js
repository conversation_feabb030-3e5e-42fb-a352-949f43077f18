/**
 * 销售发运平台-指定物料批页面
 * @date 2023-07-026
 * <AUTHOR> <<EMAIL>>
 */
import { BASIC } from '@utils/config';
import {Collapse} from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import {Button, DataSet, Table, Modal, Form, TextField} from 'choerodon-ui/pro';
import {ColumnAlign, ColumnLock, TableQueryBarType} from 'choerodon-ui/pro/lib/table/enum';
import React, { useEffect, useState } from 'react';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import withProps from 'utils/withProps';
import { TemplatePrintButton } from '../../../../components/tarzan-ui';
import { headerBindTableDS, lineBindTableDS, customerLotDS } from '../stories/AppointMaterialLotDS';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.soDelivery.soDeliveryPlatform';

// const lugeUrl = '-30607';
const lugeUrl = '';

const modalKey = Modal.key();

const AppointMaterialLotPage = props => {
  const {
    headerBindTableDs,
    lineBindTableDs,
    customerLotDs,
    appointProps,
  } = props;

  const [headerSaveDisabled, setHeaderSaveDisabled] = useState(false); // 头保存disabled
  const [lineSaveDisabled, setLineSaveDisabled] = useState(false); // 行保存disabled

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  const listener = flag => {
    // 列表交互监听
    if (headerBindTableDs) {
      const handler = flag ? headerBindTableDs.addEventListener : headerBindTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(headerBindTableDs, 'batchSelect', handleHeaderSelectUpdate);
      handler.call(headerBindTableDs, 'batchUnSelect', handleHeaderSelectUpdate);
    }
    if (lineBindTableDs) {
      const line = flag ? lineBindTableDs.addEventListener : lineBindTableDs.removeEventListener;
      // 头选中和撤销选中事件
      line.call(lineBindTableDs, 'batchSelect', handleLineSelectUpdate);
      line.call(lineBindTableDs, 'batchUnSelect', handleLineSelectUpdate);
    }
  };

  // 头勾选
  const handleHeaderSelectUpdate = () => {
    setHeaderSaveDisabled(headerBindTableDs.selected.length);
  };

  // 行勾选
  const handleLineSelectUpdate = () => {
    setLineSaveDisabled(lineBindTableDs.selected.length);
  };

  // 根据id查询
  useEffect(() => {
    lineBindTableDs.loadData([]);
    lineBindTableDs.queryDataSet.loadData([]);
    headerBindTableDs.loadData([]);
    headerBindTableDs.queryDataSet.loadData([]);
    setTimeout(() => {
      queryDetail();
    }, 10);
  }, []);

  const queryDetail = () => {
    // 行默认查询条件
    lineBindTableDs.setQueryParameter('functionName', 'product_delivery');
    lineBindTableDs.setQueryParameter('instructionDocId', appointProps.instructionDocId);
    lineBindTableDs.setQueryParameter('instructionDocType', appointProps.instructionDocType);
    lineBindTableDs.setQueryParameter('instructionId', appointProps.instructionId);
    lineBindTableDs.setQueryParameter('instructionDocLineId', appointProps.instructionDocLineId);
    // 头默认查询条件
    headerBindTableDs.setQueryParameter('functionName', 'product_delivery');
    headerBindTableDs.setQueryParameter('instructionDocId', appointProps.instructionDocId);
    headerBindTableDs.setQueryParameter('instructionDocType', appointProps.instructionDocType);
    headerBindTableDs.setQueryParameter('instructionId', appointProps.instructionId);
    headerBindTableDs.setQueryParameter('instructionDocLineId', appointProps.instructionDocLineId);
    headerBindTableDs.setQueryParameter('materialId', appointProps.materialId);
    headerBindTableDs.setQueryParameter('revisionCode', appointProps.revisionCode);
    headerBindTableDs.setQueryParameter('siteId', appointProps.siteId);
    headerBindTableDs.setQueryParameter('locatorId', appointProps.locatorId);
    headerBindTableDs.queryDataSet.loadData([{
      ownerType: appointProps.ownerType || null,
      ownerId: appointProps.ownerId || null,
    }]);
    lineBindTableDs.query();
    headerBindTableDs.query();
    setHeaderSaveDisabled(false);
    setLineSaveDisabled(false);
  };

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'identification',
    },
    {
      name: 'materialLotCode',
    },
    {
      name: 'productionBatch',
    },
    {
      name: 'primaryUomQty',
    },
    {
      name: 'primaryUomCode',
    },
    {
      name: 'locatorCode',
    },
    {
      name: 'lot',
    },
    {
      name: 'customerLot',
    },
    {
      name: 'qualityStatusDesc',
    },
    {
      name: 'enableFlag',
    },
    {
      name: 'materialLotStatus',
    },
    {
      name: 'productionDate',
    },
    {
      name: 'expirationDate',
    },
    {
      name: 'extendedShelfLifeTimes',
    },
    {
      name: 'supplierLot',
    },
    {
      name: 'supplierCode',
    },
    {
      name: 'supplierDesc',
    },
    {
      name: 'supplierSiteCode',
    },
    {
      name: 'supplierSiteDesc',
    },
    {
      name: 'customerCode',
    },
    {
      name: 'customerDesc',
    },
    {
      name: 'customerSiteCode',
    },
    {
      name: 'customerSiteDesc',
    },
    {
      name: 'reservedFlag',
    },
    {
      name: 'reservedObjectTypeDesc',
    },
    {
      name: 'reservedObjectCode',
    },
    {
      name: 'createReasonDesc',
    },
    {
      name: 'inLocatorTime',
    },
    {
      name: 'inSiteTime',
    },
    {
      name: 'ownerTypeDesc',
    },
    {
      name: 'ownerCode',
    },
    {
      name: 'ownerDesc',
    },
    {
      name: 'createdUsername',
    },
    {
      name: 'creationDate',
    },
    {
      name: 'lastUpdatedUsername',
    },
    {
      name: 'lastUpdateDate',
    },
  ];

  // 保存
  const handleSave = async record => {
    const params = {
      materialLotIds: [record?.get('materialLotId')],
      customerLot: record?.get('customerLot'),
    }
    return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/mt-product-delivery-platform/customer-lot/update/for/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res?.success) {
        queryDetail();
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  // 取消
  const handleCancel = record => {
    record.reset();
    record.setState('editing', false);
  };

  // 编辑
  const handleEdit = record => {
    record.setState('editing', true);
  };

  // 行信息表配置
  const lineTableColumns = [
    {
      name: 'identification',
      width: 150,
    },
    {
      name: 'materialLotCode',
      width: 150,
    },
    {
      name: 'primaryUomQty',
      width: 150,
    },
    {
      name: 'primaryUomCode',
      width: 150,
    },
    {
      name: 'locatorCode',
    },
    {
      name: 'productionBatch',
    },
    {
      name: 'lot',
      width: 150,
    },
    {
      name: 'customerLot',
      editor: record => record.getState('editing'),
    },
    {
      name: 'qualityStatus',
    },
    {
      name: 'enableFlag',
    },
    {
      name: 'materialLotStatus',
      width: 150,
    },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      align: ColumnAlign.center,
      width: 170,
      lock: ColumnLock.right,
      renderer: ({ record }) => {
        return (
          <>
            {record?.getState('editing') ? (
              <>
                <a onClick={() => handleSave(record)} style={{ marginRight: '0.1rem' }}>
                  {intl.get('tarzan.common.button.save').d('保存')}
                </a>
                <a onClick={() => handleCancel(record)} style={{ marginRight: '0.16rem' }}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </a>
              </>
            ) : (
              <PermissionButton
                type="text"
                onClick={() => handleEdit(record)}
                permissionList={[
                  {
                    code: `hzero.tarzan.les.so-delivery.so-delivery-platform.ps.line-bind-table.button.edit`,
                    type: 'button',
                    meaning: '绑定-编辑按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </PermissionButton>
            )}
          </>
        );
      },
    },
  ];

  // 头保存
  const handleBindMaterial = () => {
    const materialLotIdList =
      headerBindTableDs?.selected?.map(item => {
        return item?.get('materialLotId');
      }) || [];
    return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/mt-product-delivery-platform/line/detail-save/for/ui`, {
      method: 'POST',
      body: {
        instructionId: appointProps.instructionId,
        instructionDocLineId: appointProps.instructionDocLineId,
        toleranceFlag: appointProps.toleranceFlag,
        toleranceType: appointProps.toleranceType,
        toleranceMaxValue: appointProps.toleranceMaxValue,
        toleranceMinValue: appointProps.toleranceMinValue,
        quantity: appointProps.quantity,
        functionName: 'product_delivery',
        materialLotIdList,
      },
    }).then(res => {
      if (res?.success) {
        queryDetail();
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  // 行保存
  const handleCancelMaterial = () => {
    const instructionDetailIdList =
      lineBindTableDs?.selected?.map(item => {
        return item?.get('instructionDetailId');
      }) || [];
    return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/mt-product-delivery-platform/delete/ui`, {
      method: 'POST',
      body: instructionDetailIdList,
    }).then(res => {
      if (res?.success) {
        queryDetail();
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  // 行保存
  const handleBatchModifyCustomerBatches = () => {
    const customerLotModal = Modal.open({
      key: modalKey,
      title: intl.get(`${modelPrompt}.customerLot`).d('客户批次'),
      className: 'hcmp-modal',
      destroyOnClose: true,
      children: (
        <Form dataSet={customerLotDs} columns={1} labelWidth={112}>
          <TextField name="customerLot" />
        </Form>
      ),
      onCancel: () => {
        customerLotModal.close();
      },
      onOk: async () => {
        const params = {
          materialLotIds: lineBindTableDs?.selected?.map(item => {
            return item?.get('materialLotId');
          }) || [],
          customerLot: customerLotDs.toData()[0].customerLot,
        }
        return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/mt-product-delivery-platform/customer-lot/update/for/ui`, {
          method: 'POST',
          body: params,
        }).then(res => {
          if (res?.success) {
            customerLotModal.close();
            queryDetail();
            notification.success({
              message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
            });
          } else {
            notification.error({
              message: res?.message,
            });
          }
        });
      },
    });
  };

  const headerTableButtons = [
    <Button
      disabled={!headerSaveDisabled}
      onClick={() => {
        return handleBindMaterial();
      }}
      style={headerSaveDisabled ? {
        color: 'rgb(255, 255, 255)',
        backgroundColor: 'rgb(8, 64, 248)',
        border: '0.01rem solid rgb(8, 64, 248)',
      } : {
        color: 'rgb(140, 140, 140)',
        backgroundColor: 'rgb(245, 245, 245)',
        borderColor: '0.01rem solid rgb(230, 230, 230)',
      }}
    >
      {intl.get('hzero.common.button.save').d('保存')}
    </Button>,
  ]

  const lineTableButtons = [
    <TemplatePrintButton
      disabled={!lineSaveDisabled}
      printButtonCode='SO_DELIVERY_PLATFORM_IN'
      printParams={{
        materialLotIdList:lineBindTableDs.selected
          .map(item => item.toData().materialLotId)
          .join(','),
        instructionDocLineId:appointProps.instructionDocLineId,
      }}
    />,
    <PermissionButton
      disabled={!lineSaveDisabled}
      onClick={() => {
        return handleBatchModifyCustomerBatches();
      }}
      style={lineSaveDisabled ? {
        color: 'rgb(255, 255, 255)',
        backgroundColor: 'rgb(8, 64, 248)',
        border: '0.01rem solid rgb(8, 64, 248)',
        marginLeft:"10px",
      } : {
        color: 'rgb(140, 140, 140)',
        backgroundColor: 'rgb(245, 245, 245)',
        borderColor: '0.01rem solid rgb(230, 230, 230)',
        marginLeft:"10px",
      }}
      permissionList={[
        {
          code: `hzero.tarzan.les.so-delivery.so-delivery-platform.ps.line-bind-table.button.customer-lot-change`,
          type: 'button',
          meaning: '批量修改客户批次按钮',
        },
      ]}
    >
      {intl.get(`${modelPrompt}.button.batchModifyCustomerBatches`).d('批量修改客户批次')}
    </PermissionButton>,
    // <PermissionButton
    //   disabled={!lineSaveDisabled}
    //   // onClick={() => {
    //   //   return handleCancelMaterial();
    //   // }}
    //   style={lineSaveDisabled ? {
    //     color: 'rgb(255, 255, 255)',
    //     backgroundColor: 'rgb(8, 64, 248)',
    //     border: '0.01rem solid rgb(8, 64, 248)',
    //     marginLeft: '10px',
    //   } : {
    //     color: 'rgb(140, 140, 140)',
    //     backgroundColor: 'rgb(245, 245, 245)',
    //     borderColor: '0.01rem solid rgb(230, 230, 230)',
    //     marginLeft: '10px',
    //   }}
    //   permissionList={[
    //     {
    //       code: `hzero.tarzan.les.so-delivery.so-delivery-platform.ps.line-bind-table.button.bind-print`,
    //       type: 'button',
    //       meaning: '打印按钮',
    //     },
    //   ]}
    // >
    //   {intl.get(`${modelPrompt}.button.bindPrint`).d('打印')}
    // </PermissionButton>,
    <PermissionButton
      disabled={!lineSaveDisabled}
      onClick={() => {
        return handleCancelMaterial();
      }}
      style={lineSaveDisabled ? {
        color: 'rgb(255, 255, 255)',
        backgroundColor: 'rgb(8, 64, 248)',
        border: '0.01rem solid rgb(8, 64, 248)',
        marginLeft: '10px',
      } : {
        color: 'rgb(140, 140, 140)',
        backgroundColor: 'rgb(245, 245, 245)',
        borderColor: '0.01rem solid rgb(230, 230, 230)',
        marginLeft: '10px',
      }}
      permissionList={[
        {
          code: `hzero.tarzan.les.so-delivery.so-delivery-platform.ps.line-bind-table.button.cancel-appoint`,
          type: 'button',
          meaning: '取消指定按钮',
        },
      ]}
    >
      {intl.get(`${modelPrompt}.button.cancelAppoint`).d('取消指定')}
    </PermissionButton>,
  ]

  return (
    <div>
      <Table
        searchCode="soDeliveryPlatformAppointOne"
        customizedCode="soDeliveryPlatformAppointOne"
        queryBar={TableQueryBarType.filterBar}
        queryBarProps={{
          fuzzyQuery: false,
        }}
        dataSet={headerBindTableDs}
        columns={headerTableColumns}
        buttons={headerTableButtons}
        highLightRow={false}
      />
      <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.line.bindMaterialLot`).d('已指定物料批')}
          key="basicInfo"
          dataSet={lineBindTableDs}
        >
          {lineBindTableDs && (
            <Table
              searchCode="soDeliveryPlatformAppointTwo"
              customizedCode="soDeliveryPlatformAppointTwo"
              dataSet={lineBindTableDs}
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
              }}
              highLightRow={false}
              buttons={lineTableButtons}
              columns={lineTableColumns}
            />
          )}
        </Panel>
      </Collapse>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.soDelivery.soDeliveryPlatform', 'tarzan.common'],
})(
  withProps(
    () => {
      const headerBindTableDs = new DataSet({ ...headerBindTableDS() });
      const lineBindTableDs = new DataSet({ ...lineBindTableDS() });
      const customerLotDs = new DataSet({ ...customerLotDS() });
      return {
        headerBindTableDs,
        lineBindTableDs,
        customerLotDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(AppointMaterialLotPage),
);
