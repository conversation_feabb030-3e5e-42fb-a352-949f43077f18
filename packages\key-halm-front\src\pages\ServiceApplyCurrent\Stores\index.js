/**
 * @since 2020-07-08
 * <AUTHOR>
 * @copyright Copyright (c) 2020, Hand
 */
import intl from 'utils/intl';
import { HALM_MTC } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

import { strategy, listFields } from '../../ServiceApply/Stores/listDS';

const organizationId = getCurrentOrganizationId();

const promptCode = 'amtc.serviceApply.model.serviceApply';

const queryFields = () => {
  const fields = strategy();
  return [...fields.basic, ...fields.reportDate, ...fields.asset, ...fields.location];
};

const fields = () => {
  return [
    ...listFields(),
    {
      name: 'srStatusMeaning',
      type: 'string',
      label: intl.get(`${promptCode}.srStatusMeaning`).d('申请进度'), // 我的服务申请和服务中心不同名
    },
  ];
};

// 列表页table
function tableDS() {
  return {
    autoQuery: true,
    selection: false,
    queryFields: queryFields(),
    fields: fields(),
    transport: {
      read: ({ params }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/sr/report`,
          method: 'GET',
          params: {
            ...params,
            tenantId: organizationId,
          },
        };
      },
      destroy: ({ data }) => {
        return {
          data,
          url: `${HALM_MTC}/v1/${organizationId}/sr`,
          method: 'DELETE',
        };
      },
    },
  };
}

// 撤回
function recallDS() {
  return {
    autoQuery: false,
    dataKey: 'content',
    transport: {
      submit: ({ params, data }) => {
        return {
          url: `${HALM_MTC}/v1/${organizationId}/sr/withdraw`,
          method: 'PUT',
          params: {
            ...params,
            tenantId: organizationId,
          },
          data: {
            ...data[0],
            tenantId: organizationId,
          },
        };
      },
    },
  };
}

export { tableDS, recallDS };
