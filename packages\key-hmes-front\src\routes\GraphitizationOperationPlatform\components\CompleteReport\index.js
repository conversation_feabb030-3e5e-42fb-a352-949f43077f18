// 新加工件
import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Form, Button, Table,Output, NumberField, Modal, Select, TextField } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import { TemplatePrintButton } from '@components/tarzan-ui';
import intl from 'utils/intl';
import { connect } from 'dva';
import { observer } from 'mobx-react';
import { formDS, detailDS } from './stores/CompleteReportDS';
import { CardLayout } from '../commonComponents';
import { Report, GetProductionBatch, CompleteCancel, QueryDetail } from './services';
import { namespace } from '../../model';
import styles from './index.modules.less';

const chooseList = ['吨袋', '管道'];
const modelPrompt = 'tarzan.hmes.graphitizationOperationPlatform.CompleteReport';

const CompleteReport = observer(props => {
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );

  const formDs = useMemo(
    () =>
      new DataSet({
        ...formDS(),
      }),
    [],
  );
  const { run: report, loading: reportLoading } = useRequest(Report(), {
    manual: true,
    needPromise: true,
  });
  const { run: queryDetail, loading: queryDetailLoading } = useRequest(QueryDetail(), {
    manual: true,
    needPromise: true,
  });

  const { run: getProductionBatch, loading: getProductionBatchLoading } = useRequest(GetProductionBatch(), {
    manual: true,
    needPromise: true,
  });
  const { run: completeCancel, loading: completeCancelLoading } = useRequest(CompleteCancel(), {
    manual: true,
    needPromise: true,
  });

  const [showBarCode, setShowBarCode] = useState(false);
  const [showReport, setShowReport] = useState(true);
  const [catchEoId, setCatchEoId] = useState(null);
  const [selectDiv, setSelectDiv] = useState('');
  const [productionBatchFlag, setProductionBatchFlag] = useState(false);
  const { modelState = {} } = props;
  const { refreshStoveData = [] } = modelState;
  useEffect(() => {
    if (refreshStoveData&&showBarCode) {
      handleBarcode();
      props.handleRefresh({
        refreshStoveData: false,
      });
    }
  }, [refreshStoveData]);

  useEffect(() => {
    if (props.modelState?.selectEo?.eoId && catchEoId !== props.modelState?.selectEo?.eoId) {
      setCatchEoId(props.modelState?.selectEo?.eoId);
      setSelectDiv('');
      formDs.current.init('eoCount', 1);
      formDs.current.init('singleWeight', null);
      formDs.current.init('productionBatch', null);
      formDs.current.init('sumWeight', null);
      formDs.current.init('completionLocationCode', null);
      handelQuery()
      if(showBarCode){
        handleBarcode();
      }
    }else{
      setCatchEoId(null);
    }
  }, [props.modelState?.selectEo?.eoId]);

  const handelQuery = async () => {
    const res = await getProductionBatch({
      params: {
        ...props.modelState?.selectEo,
      },
    })
    if(res&&res.failed){
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'FAIL',
        recordType: 'save',
        message: res.message,
      });
    }else{
      setProductionBatchFlag(res.flag)
      formDs.current.init('productionBatchFlag', res.flag||false);
      formDs.current.init('productionBatch', res.productionBatch);
    }
  }

  const columnsDetail = [
    {
      name: 'serialNumber',
      width: 70,
      renderer: ({record}) => {
        return record.index + 1;
      },
    },
    {
      name: 'layerMeaning',
      width: 100,
    },
    {
      name: 'materialLotCode',
      width: 400,
    },
    {
      name: 'workOrderNum',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'productionBatch',
    },
    {
      name: 'primaryUomQty',
      width: 70,
    },
    {
      name: 'creationDate',
    },
  ];
  const handleBarcode = async () => {
    setShowBarCode(true);
    setShowReport(false);

    if(!props.modelState?.selectEo?.eoId)return
    detailDs.setQueryParameter('eoId', props.modelState?.selectEo?.eoId)
    detailDs.setQueryParameter('stoveCode', props.modelState?.selectEo?.stoveCode)
    detailDs.query();
  };
  const handleBack = () => {
    setShowBarCode(false);
    setShowReport(true);
    detailDs.loadData([]);
  };

  const handleReport = async () => {
    if (!selectDiv) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'FAIL',
        recordType: 'complete',
        message: '请选择类型!',
      });
      return
    }
    if(!await formDs.current.validate())return
    const qty = props?.modelState?.selectEo?.qty;
    const sumWeight = formDs?.current?.get('sumWeight');
    if(Number(sumWeight)>Number(qty))
    {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'FAIL',
        recordType: 'complete',
        message: `当前最大完工数量为${qty}，不可超量完工!`,
      });
      return
    }
    const newSingleWeight = formDs.current.get('singleWeight');
    Modal.confirm({
      title: <span style={{ color: 'white' }}>您录入的单包重量为{newSingleWeight},是否确认?</span>,
      contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
      okProps: {
        style: {
          background: '#00D4CD',
          color: 'white',
          borderColor: '#00d4cd',
        },
      },
      cancelProps: {
        style: {
          background: '#50819c',
          color: 'white',
        },
      },
      onOk: async () => {
        const eoList = props.modelState.selectEo;
        eoList.eoCount = formDs.current.get('eoCount');
        eoList.singleWeight = formDs.current.get('singleWeight');
        const res = await report({
          params: {
            ...props.modelState,
            completionLocationCode: formDs.current.get('completionLocationCode'),
            eoList: [eoList],
            productionBatch: formDs.current?.get('productionBatch'),
          },
        });
        if (res && !res.success) {
          props.handleAddRecords({
            cardId: props.cardId,
            messageType: 'FAIL',
            recordType: 'save',
            message: res.message,
          });
        } else {
          props.handleAddRecords({
            cardId: props.cardId,
            messageType: 'SUCCESS',
            recordType: 'end',
            message: '报工，操作成功',
          });
          // 刷新单据
          props.handleRefresh({
            refreshStoveData: true,
          });
          formDs.current.init('eoCount', 1);
          formDs.current.init('singleWeight', null);
          // 不清除工艺批次
          // formDs.current.init('productionBatch', null);
          formDs.current.init('sumWeight', null);
          formDs.current.init('completionLocationCode', null);
          setSelectDiv('');
          return true
        }
      },
    });
    return true
  };

  const selectType = value => {
    if (selectDiv === value) {
      setSelectDiv('');
      formDs.current.set('completionLocationCode', null);
    } else {
      setSelectDiv(value);
      formDs.current.set('completionLocationCode', null);
      formDs.setState('type', value);
    }
  };

  const handleRevoke = async () => {
    const res = await completeCancel({
      params: {
        hmeWorkStationVO3: props.modelState,
        list: detailDs.selected.map(item => item.data),
      },
    })
    if(res && res.failed){
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'FAIL',
        recordType: 'save',
        message: res.message,
      });
    }else{
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'end',
        message: '完工撤销成功',
      });
      // 刷新单据
      props.handleRefresh({
        refreshStoveData: true,
      });
    }
  }

  return (
    <CardLayout.Layout className={styles.CompleteReport} spinning={reportLoading||getProductionBatchLoading}>
      <CardLayout.Header
        title={intl.get(`${modelPrompt}.title`).d('完工')}
        help={props?.cardUsage?.meaning}
        content={
          <>
            {/* <Button color="primary" style={{fontSize: '16px'}} >{intl.get(`${modelPrompt}.report`).d('打印')}</Button> */}
            <TemplatePrintButton
              disabled={detailDs.selected.length === 0}
              printButtonCode="HME.GRAPHITIZATION_DATA_TEMP"
              printParams={{
                materialLotIdList: detailDs.selected.map(e => e.get('materialLotId')).join(','),
              }}
            />
            {!showBarCode && (
              <Button color="primary" onClick={handleBarcode}>
                {intl.get(`${modelPrompt}.barCode`).d('条码')}
              </Button>
            )}

            {showReport && (
              <Button
                onClick={handleReport}
                color="primary"
                style={
                  props.modelState?.selectEo?.eoId && {
                    background: 'rgba(0, 212, 205, 1)',
                    color: '#fff',
                    borderColor: 'rgba(0, 212, 205, 1)',
                  }
                }
                disabled={!props.modelState?.selectEo?.eoId}
              >
                {intl.get(`${modelPrompt}.report`).d('完工')}
              </Button>
            )}
            {showBarCode && (
              <>
              <Button color="primary" disabled={detailDs.selected.length === 0} onClick={handleRevoke}>
              {intl.get(`${modelPrompt}.rework`).d('完工撤销')}
              </Button>
              <Button color="primary" onClick={handleBack} style={{ fontSize: '16px' }}>
                {intl.get(`${modelPrompt}.back`).d('返回')}
              </Button>
              </>
            )}
          </>
        }
      />
      <CardLayout.Content>
        {showReport && (
          <div style={{ display: 'inline-flex', alignItems: 'center', height: '50%' }}>
            <Form dataSet={formDs} columns={1} labelWidth={200} style={{'height': '100%'}}>
              <NumberField name="singleWeight" style={{ width: '50%' }} />
              <NumberField
                style={{ width: '50%' }}
                name="eoCount"
                suffix={<span style={{ color: '#fff' }}>个</span>}
              />
              <Output
                  name="sumWeight"
                  renderer={({ value }) => {
                    return (
                      <div
                        style={{
                          color: '#fff',
                          background: 'rgba(0, 212, 205, 1)',
                          width: '50%',
                          textAlign: 'center',
                        }}
                      >
                        {value}
                      </div>
                    );
                  }}
                />
              {/* <NumberField
                style={{ width: '50%' }}
                name="singleWeight"
                suffix={<span style={{ color: '#fff' }}>KG</span>}
              /> */}
              <div style={{ display: 'flex' }}>
                {chooseList.map(i => {
                  return (
                    <div
                      className={selectDiv === i ? styles.selectDiv : styles.normalDiv}
                      onClick={() => selectType(i)}
                    >
                      {i}
                    </div>
                  );
                })}
              </div>
              {selectDiv === '管道' && (
                <Select
                  style={{ width: '50%' }}
                  name="completionLocationCode"
                  getPopupContainer={() =>
                    document.getElementById('graphitizationOperationPlatform') || document.body
                  }
                />
              )}
              {productionBatchFlag&&<TextField name="productionBatch" style={{ width: '50%', color: '#fff' }} />}
            </Form>
          </div>
        )}
        {showBarCode && <Table dataSet={detailDs} columns={columnsDetail} />}
      </CardLayout.Content>
    </CardLayout.Layout>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.graphitizationOperationPlatform.CompleteReport', 'model.org.monitor'],
})(
  connect(state => {
    return {
      modelState: state[namespace],
    };
  })(CompleteReport),
);
