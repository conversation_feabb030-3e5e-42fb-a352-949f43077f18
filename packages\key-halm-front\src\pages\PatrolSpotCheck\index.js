/**
 * 点巡检单
 * @date 2021/10/08
 * <AUTHOR>
 * @copyright Copyright (c) 2021,Hand
 */
import React, { Component } from 'react';
import { Button, Table, DataSet, Select, Modal } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Bind } from 'lodash-decorators';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { dateTimeRender } from 'utils/renderer';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import PointLov from 'alm/components/PointLov';
import IconSelect from 'alm/components/IconSelect';
import EmployeesLov from 'alm/components/EmployeesLov';
import { statusColors } from 'alm/utils/constants';

import getLangs from './Langs';
import { tableDS } from './Stores/listDs';
import { deleteApi } from './api';

@formatterCollections({
  code: ['alm.common', 'alm.component', 'aori.patrolSpotCheck', 'alm.checklistEditModal'],
})
@withProps(
  () => {
    const listDS = new DataSet(tableDS());
    return {
      listDS,
    };
  },
  { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true }
)
export default class PatrolSpotCheckList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      delCanClick: false,
    };
    this.props.listDS.setDelBtnStatus = this.setDelBtnStatus;
  }

  @Bind
  setDelBtnStatus() {
    this.setState({
      delCanClick: this.props.listDS.selected.length > 0,
    });
  }

  @Bind()
  handleCreate() {
    this.props.history.push({
      pathname: `/aori/patrol-spot-check/create`,
    });
  }

  @Bind()
  handleViewOrEidt(record, isEdit = false) {
    this.props.history.push({
      pathname: `/aori/patrol-spot-check/detail/${record.get('woId')}`,
      state: { isEdit },
    });
  }

  @Bind()
  handleDelete() {
    Modal.confirm({
      title: getLangs('NOTICE'),
      children: getLangs('DELETE_CONFIRM'),
      onOk: async () => {
        const data = this.props.listDS.selected.map(i => i.get('woId'));
        deleteApi(data).then(res => {
          if (res && !res.failed) {
            this.props.listDS.query();
          } else {
            notification.error({
              message: res?.message,
            });
          }
        });
      },
    });
  }

  @Bind()
  handleStatusFilter(record) {
    return ['DRAFT', 'APPROVED', 'INPRG', 'WRD', 'PAUSE', 'COMPLETED', 'CANCELED', 'SKIP'].includes(
      record.get('value')
    );
  }

  @Bind
  handleChangePlannerGroup(e) {
    const { queryDataSet } = this.props.listDS;
    this.props.listDS.setQueryParameter('plannerGroupId', e.workCenterId);
    queryDataSet.current.set('plannerGroupName', e.workCenterName);
    if (e.workCenterId) {
      this.props.listDS.setQueryParameter('plannerId', e.employeeId);
      queryDataSet.current.set('plannerName', e.employeeName);
    }
  }

  @Bind
  handleChangePlanner(e) {
    this.props.listDS.setQueryParameter('plannerId', e.employeeId);
    this.props.listDS.queryDataSet.current.set('plannerName', e.employeeName);
  }

  @Bind
  handleChangeOwnerGroup(e) {
    const { queryDataSet } = this.props.listDS;
    this.props.listDS.setQueryParameter('ownerGroupId', e.workCenterId);
    queryDataSet.current.set('ownerGroupName', e.workCenterName);
    if (e.workCenterId) {
      this.props.listDS.setQueryParameter('ownerId', e.employeeId);
      queryDataSet.current.set('ownerName', e.employeeName);
    }
  }

  @Bind
  handleChangeOwner(e) {
    this.props.listDS.setQueryParameter('ownerId', e.employeeId);
    this.props.listDS.queryDataSet.current.set('ownerName', e.employeeName);
  }

  get columns() {
    return [
      {
        name: 'woTypeIcon',
        width: 75,
        align: 'center',
        renderer: ({ record }) => {
          const { woTypeIcon, woTypeIconTypeCode } = record.toData();
          return (
            <IconSelect
              border
              title={woTypeIcon}
              iconTypeCode={woTypeIconTypeCode}
              type={woTypeIcon}
              style={{
                width: 36,
                height: 36,
              }}
            />
          );
        },
      },
      {
        name: 'woNum',
        align: 'center',
        width: 120,
        renderer: ({ value, record }) => {
          return <a onClick={() => this.handleViewOrEidt(record, false)}>{value}</a>;
        },
      },
      {
        name: 'woName',
      },
      {
        name: 'spotCheckObjType',
      },
      {
        name: 'spotCheckObjName',
      },
      {
        name: 'woStatusMeaning',
        width: 110,
        align: 'center',
        renderer: ({ record }) => {
          const {
            woStatus,
            woStatusMeaning,
            woopStatus,
            woopStatusMeaning,
            spotCheckObjType,
          } = record.toData();
          const isWo = spotCheckObjType === 'CHECKROUTE';
          const status = isWo ? woStatus : woopStatus;
          const statusMeaning = isWo ? woStatusMeaning : woopStatusMeaning;
          return (
            <Tag
              style={{
                color: (statusColors[status] && statusColors[status].fontColor) || '#000',
                border: 0,
              }}
              color={(statusColors[status] && statusColors[status].bgColor) || '#fff'}
            >
              {statusMeaning}
            </Tag>
          );
        },
      },
      {
        name: 'woTypeName',
      },
      {
        name: 'scheduledStartDate',
        width: 150,
        align: 'center',
        renderer: ({ value }) => dateTimeRender(value),
      },
      {
        name: 'scheduledFinishDate',
        width: 150,
        align: 'center',
        renderer: ({ value }) => dateTimeRender(value),
      },
      {
        name: 'plannerName',
        width: 100,
      },
      {
        name: 'ownerName',
        width: 100,
      },
      {
        name: 'maintSiteName',
        width: 120,
      },
      {
        header: getLangs('OPTION'),
        width: 60,
        renderer: ({ record }) => {
          // 仅拟定 需改派的可以点击
          const { woStatus } = record.toData();
          const isDisabled = !['DRAFT', 'WRD'].includes(woStatus);
          return (
            <a onClick={() => this.handleViewOrEidt(record, true)} disabled={isDisabled}>
              {getLangs('EDIT')}
            </a>
          );
        },
        lock: 'right',
      },
    ];
  }

  render() {
    const { delCanClick } = this.state;
    const { listDS } = this.props;

    const queryData = listDS.queryDataSet?.current?.toData() || {};
    const { routeId, pointName } = queryData;

    return (
      <React.Fragment>
        <Header title={getLangs('TITLE')}>
          <Button icon="add" color="primary" onClick={this.handleCreate} key="create">
            {getLangs('CREATE')}
          </Button>
          <Button icon="delete" disabled={!delCanClick} onClick={this.handleDelete} key="delete">
            {getLangs('DELETE')}
          </Button>
        </Header>
        <Content>
          <Table
            key="patrolSpotCheckList"
            searchCode="AORI.PATROL_SPOT_CHECKLIST.LIST"
            customizedCode="AORI.PATROL_SPOT_CHECKLIST.LIST"
            dataSet={listDS}
            columns={this.columns}
            queryBar="filterBar"
            queryFields={{
              pointName: (
                <PointLov
                  name="pointName"
                  isQuery
                  value={pointName}
                  disabled={!!routeId}
                  listDS={listDS}
                />
              ),
              plannerGroupName: <EmployeesLov onOk={this.handleChangePlannerGroup} />,
              plannerName: <EmployeesLov onOk={this.handleChangePlanner} />,
              ownerGroupName: <EmployeesLov onOk={this.handleChangeOwnerGroup} />,
              ownerName: <EmployeesLov onOk={this.handleChangeOwner} />,
              woopStatus: <Select optionsFilter={this.handleStatusFilter} />,
            }}
            queryBarProps={{
              fuzzyQueryPlaceholder: getLangs('KEYWORD_PLACEHOLDER'),
              onReset: () => {
                this.props.listDS.setQueryParameter('plannerGroupId', null);
                this.props.listDS.setQueryParameter('plannerId', null);
                this.props.listDS.setQueryParameter('ownerGroupId', null);
                this.props.listDS.setQueryParameter('ownerId', null);
                this.props.listDS.setQueryParameter('pointId', null);
              },
            }}
          />
        </Content>
      </React.Fragment>
    );
  }
}
