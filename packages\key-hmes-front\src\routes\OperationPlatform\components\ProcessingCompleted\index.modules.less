.ProcessingCompletedTitle {
  :global {

    .c7n-pro-btn-wrapper {
      font-size: 16px !important;
    }
  }
}

.ProcessingCompleted {

  :global {
    .c7n-pro-modal-body{
      background-color: rgb(91, 136, 160) !important;

    }
    .c7n-pro-output-wrapper{
      color: #fff !important;
    }
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content .c7n-pro-table-row {
      height: 45px !important;
    }

    .icon-refresh {
      background-color: rgb(91, 136, 160) !important;
      color: white !important;
    }

    .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-cell {
      background-color: #3c87ad !important;
      color: white !important;
      border: none !important;
      font-size: 17px !important;
    }

    .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
      background-color: #3c87ad !important;
      color: white !important;
      font-size: 17px !important;
    }

    .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
      font-size: 16px !important;
    }

    .c7n-pro-btn-wrapper {
      background-color: #3c87ad !important;
    }
    .c7n-pro-input-wrapper input{
      background-color: #3c87ad !important;
      color: white !important;
    }

    .icon {
      color: white !important;
    }

    .c7n-pro-pagination-perpage {
      color: white !important;
    }

    .c7n-pro-pagination-page-info {
      color: white !important;
    }
  }
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content .c7n-pro-table-row {
      height: 45px !important;
    }

    .icon-refresh {
      background-color: rgb(91, 136, 160) !important;
      color: white !important;
    }

    // 表头
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content table .c7n-pro-table-tfoot tr th,
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content table .c7n-pro-table-thead tr th {
      background-color: #3c87ad !important;
      color: rgba(147, 203, 255, 1) !important;
      border: none !important;
      font-size: 17px !important;
    }

    // .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-cell {
    //   background:red !important;

    // }
    // 表格内容
    .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
      background-color: #3c87ad !important;
      color: white !important;
      font-size: 17px !important;
    }

    .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .c7n-pro-btn-wrapper {
      color: white !important;
      font-size: 16px !important;
    }

    .icon {
      color: white !important;
    }

    .c7n-pro-pagination-perpage {
      color: white !important;
    }

    .c7n-pro-pagination-page-info {
      color: white !important;
    }
  }
}

.modalForm {
  :global {
    .c7n-pro-field-label {
      color: white !important;
    }

    .c7n-pro-select-wrapper {
      background: #50819c !important;
      color: white !important;
    }

    .c7n-pro-select {
      color: white !important;
    }
  }
}

.contentForm {
  :global {
    .c7n-pro-input-number-wrapper {
      input {
        color: #fff !important;
      }
    }
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label input, .c7n-pro-select-wrapper.c7n-pro-select-wrapper label > span {
      padding-top: 0px;
      padding-bottom: 0px;
      z-index: 5;
      border-radius: 2px;
      font-size: 12px;
      color: #fff !important;
    }
  }
  :global {
    .c7n-pro-input{
      color: #fff !important;
    }
  }



  :global {
    .c7n-pro-output-wrapper {
      color: #fff !important;
    }
  }

  :global {
    .c7n-pro-field-label {
      color: rgba(112, 187, 243, 1) !important;
      font-size: 16px !important;
    }
  }

  :global {
    .c7n-pro-field {
      font-size: 16px !important;

      input {
        font-size: 16px !important;
      }
    }
  }

}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70%;
}
