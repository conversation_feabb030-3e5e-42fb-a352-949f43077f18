import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.purchase.delivery';
const tenantId = getCurrentOrganizationId();

const headerTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: false,
  primaryKey: 'instructionDocId',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-delivery-doc/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST.HEAD`,
        method: 'POST',
      };
    },
  },
  queryFields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('送货单号'),
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('送货单状态'),
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS_PURCHASE`,
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'supplier',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierId',
      type: FieldType.string,
      bind: 'supplier.supplierId',
    },
    {
      name: 'supplierSite',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierSite`).d('供应商地点'),
      lovCode: 'MT.MODEL.SUPPLIER_SITE',
      noCache: true,
      ignore: 'always',
      dynamicProps: {
        disabled({ record }) {
          return !record.get('supplierId');
        },
        lovPara({ record }) {
          return {
            tenantId,
            supplierId: record.get('supplierId'),
          };
        },
      },
    },
    {
      name: 'supplierSiteId',
      type: FieldType.string,
      bind: 'supplierSite.supplierSiteId',
    },
    {
      name: 'po',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.poNumber`).d('采购订单号'),
      lovCode: 'MT.PO',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'poHeaderId',
      type: FieldType.string,
      bind: 'po.poHeaderId',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      textField: 'materialCode',
      valueField: 'materialId',
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'material.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
    },
    {
      name: 'expectedArrivalTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.estimatedArrivalFrom`).d('预计到货时间从'),
      max: 'expectedArrivalTimeTo',
    },
    {
      name: 'expectedArrivalTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.estimatedArrivalTo`).d('预计到货时间至'),
      min: 'expectedArrivalTimeFrom',
    },
    {
      name: 'urgentFlag',
      type: FieldType.string,
      lookupCode: 'MT.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.urgentFlag`).d('是否加急'),
    },
    {
      name: 'printFlag',
      type: FieldType.string,
      lookupCode: 'MT.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`tarzan.common.printFlag`).d('打印标识'),
    },
    {
      name: 'createdLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdLov`).d('创建人'),
      lovCode: 'HME.USER_INFO',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      bind: 'createdLov.id',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
  ],
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('送货单号'),
    },
    {
      name: 'instructionDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatusDesc`).d('送货单状态'),
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocTypeDesc`).d('送货单类型'),
    },
    {
      name: 'sourceSystemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceSystemDesc`).d('来源系统'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
    },
    {
      name: 'supplierSiteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierSiteName`).d('供应商地点'),
    },
    {
      name: 'expectedArrivalTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.demandTime`).d('预计到货时间'),
    },
    {
      name: 'specialFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specialFlag`).d('特殊标识'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'printTimes',
      type: FieldType.string,
      label: intl.get(`tarzan.common.printTimes`).d('打印次数'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loginName`).d('创建人'),
    },
    {
      name: 'createdDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('更新时间'),
    },
  ],
  record: {
    dynamicProps: {
      // 关闭类型的单据不可选择
      selectable: record => !['CANCEL', 'CLOSED'].includes(record?.get('instructionDocStatus')),
    },
  },
});

const lineTableDS = () => {
  return {
    cacheSelection: false,
    autoQuery: false,
    autoCreate: false,
    pageSize: 10,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    primaryKey: 'instructionDocLineId',
    transport: {
      read: () => {
        return {
          // 采购订单列表 接口待替换
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-delivery-doc/line/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST.LINE`,
          method: 'POST',
        };
      },
    },
    fields: [
      {
        name: 'lineNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      },
      {
        name: 'identifyType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'siteCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'quantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.quantity`).d('制单数量'),
      },
      {
        name: 'actualQuantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.actualQuantity`).d('已创建物料批数量'),
      },
      {
        name: 'weighingQuantity',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.weighingQuantity`).d('称重数量'),
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'instructionStatusDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.instructionStatusDesc`).d('状态'),
      },
      {
        name: 'weighingTime',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.weighingTime`).d('称重时间'),
      },
      {
        name: 'numberPlate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.numberPlate`).d('车牌号'),
      },
      {
        name: 'poNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.poNumber`).d('采购订单号'),
      },
      {
        name: 'lineNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lineNum`).d('采购订单行号'),
      },
      {
        name: 'actualQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.actualQty`).d('已接收数量'),
      },
      {
        name: 'storageActualQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.storageActualQty`).d('已入库数量'),
      },
      {
        name: 'locatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.locatorCode`).d('接收仓库'),
      },
      {
        name: 'urgentFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.urgentFlag`).d('加急标识'),
      },
      {
        name: 'toleranceFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
        trueValue: 'Y',
        falseValue: 'N',
      },
      {
        name: 'toleranceType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
        textField: 'description',
        valueField: 'typeCode',
        lovPara: { tenantId },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=INSTRUCTION_TOLERANCE_TYPE`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'toleranceMinValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差'),
      },
      {
        name: 'toleranceMaxValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差'),
      },
      {
        name: 'soNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.soNum`).d('销售订单号'),
      },
      {
        name: 'soLineNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
      },
      {
        name: 'demandTime',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.demandDate`).d('需求日期'),
      },
    ],
  };
};

export { headerTableDS, lineTableDS };
