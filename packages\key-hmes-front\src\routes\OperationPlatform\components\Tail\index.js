// 尾料
import React, { useState, useEffect, useMemo } from 'react';
import {
  Button,
  Row,
  Col,
  NumberField,
  TextField,
  DataSet,
  Table,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { isArray } from 'lodash';
import { useRequest } from '@components/tarzan-hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { TemplatePrintButton } from '@components/tarzan-ui';
import { ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import cardSvg from './check.png';
import { CardLayout } from '../commonComponents';
import styles from './index.modules.less';
import { CompleteManual, QueryMaterialLot, CompleteCancel } from './services/index.js';
import { detailDS } from './stores/LineSideSiloDS';


const modelPrompt = 'tarzan.hmes.operationPlatform.LineSideSilo';

const LineSideSilo = observer(props => {

  const { run: completeManual, loading: getInfoManualLoading } = useRequest(CompleteManual(), {
    manual: true,
    needPromise: true,
  });

  const { run: queryMaterialLot, loading: queryMaterialLotLoading } = useRequest(QueryMaterialLot(), {
    manual: true,
    needPromise: true,
  });
  const { run: completeCancel, loading: completeCancelLoading } = useRequest(CompleteCancel(), {
    manual: true,
    needPromise: true,
  });



  const [selectLot, setSelectLot] = useState([]);
  const [title, setTitle] = useState('尾料')
  const [loading, setLoading] = useState(false);
  const [materialDataManual, setMaterialDataManual] = useState([]); // 线边料仓数据
  const [originMaterialDataManual, setOriginMaterialDataManual] = useState([]); // 线边料仓数据
  const [showBarCode, setShowBarCode] = useState(false) // 展示条码

  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );

  useEffect(() => {
    if (props.treeData.length > 0) {
      setMaterialDataManual([...props.treeData])
      setOriginMaterialDataManual([...props.treeData])
    } else {
      setMaterialDataManual([])
      setOriginMaterialDataManual([])
    }
  }, [props.treeData]);



  useEffect(() => {
    setLoading(props.spin);
  }, [props.spin]);

  // 物料完工
  const completeMaterial = async () => {
    const params = {
      eo: props.eoData.eo,
      materialList: selectLot,
      locatorId: props.loginWkcInfo.locatorId,
      workOrderId: props.loginWkcInfo.workOrderId,
      workOrderNum: props.loginWkcInfo.workOrderNum,
      workStationId: props.loginWkcInfo.workStationId,
      workStationCode: props.loginWkcInfo.workStationCode,
      workStationName: props.loginWkcInfo.workStationName,
      productionLineId: props.loginWkcInfo.productionLineId,
      productionLineCode: props.loginWkcInfo.productionLineCode,
      routerStepId: props.eoData.routerStepId,
    }
    const res = await completeManual({
      params,
    })
    if (res && res.message) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'ERROR',
        recordType: 'query',
        message: res.message,
      });
    } else {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'query',
        message: '尾料成功完工',
      });
      setSelectLot([])

      const data = originMaterialDataManual.map(e=>{
        return {
          ...e,
          tailUnitWeightQty: null,
          tailBarcodeQty: null,
        }
      })
      setMaterialDataManual(data);
    }
  };

  const handleChangeEndQty = (val, item, name) => {
    if(name === "productionBatch"){
      // 校验工艺批次不能包含空格或中文特殊字符
      const pattern = new RegExp('[\u4e00-\u9fa5\\（，。\\）]');
      if (pattern.test(val)) {
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'SUCCESS',
          recordType: 'query',
          message: '尾料-工艺批次不允许输入中文',
        });
        return;
      }
    }
    const newList = materialDataManual
    newList.forEach(listItem => {
      if (listItem.materialCode === item.materialCode) {
        if(name === "productionBatch"){
          // 去除空格
          const value = val?.replace(/\s*/g,"");
          listItem[name] = value;
        }else{
          listItem[name] = val;
        }
      }
    })

    const data = newList.filter(e => e.tailUnitWeightQty && e.tailBarcodeQty);

    setSelectLot(data);
    setMaterialDataManual(newList)
  }

  const handleBack = () => {
    setShowBarCode(false)
    detailDs.loadData([])
  }

  const handleBarCode = async () => {
    setShowBarCode(true);
    const res = await queryMaterialLot({
      params: {
        ...props.eoData,
      },
    })
    if (res && res.failed) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'ERROR',
        recordType: 'query',
        message: res.message,
      });
    } else {
      detailDs.loadData(res)
    }
  }

  const column = [
    {
      name: 'serialNumber',
      width: 70,
      renderer: ({ record }) => {
        if (record) {
          return record?.index + 1;
        }
      },
    },
    {
      name: 'materialLotCode',
    },
    {
      name: 'primaryUomQty',
      width: 70,
      align: 'left',
    },
    {
      name: 'productionBatch',
      width: 200,
    },
    {
      name: 'creationDate',
      width: 200,
    },
  ]

  const handleCancel = async() => {
    const res = await completeCancel({
      params: {
        hmeWorkStationVO3: props.eoData,
        list: detailDs.selected.map(item => item.data),
      },
    })
    if (res && res.failed) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'ERROR',
        recordType: 'query',
        message: res.message,
      });
    } else {
      handleBarCode();
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'query',
        message: '完工撤销操作成功',
      });
    }
  }

  return (
    <CardLayout.Layout
      spinning={
        getInfoManualLoading ||
        queryMaterialLotLoading ||
        completeCancelLoading||
        loading
      } className={styles.lineSideSilo}>
      <CardLayout.Header
        title={title}
        addonAfter={
          <>
            <TemplatePrintButton
              disabled={detailDs.selected.length === 0}
              printButtonCode='HME.WORK_STATION_TEMP'
              printParams={{ materialLotIdList: detailDs.selected.map(e=>e.get("materialLotId")).join(',') }}
            />
            {
              showBarCode &&
              <>
                <Button
                  onClick={handleBack}
                  color="primary"
                >
                返回
                </Button>
                <Button
                  onClick={handleCancel}
                  disabled={detailDs.selected.length === 0}
                  style={{
                    background: 'rgb(0, 212, 205)', color: '#fff', borderColor: 'rgb(0, 212, 205)',
                  }}>
                  {intl.get(`${modelPrompt}.button.finishRework`).d('完工撤销')}
                </Button>
              </>
            }
            <Button
              color="primary"
              onClick={handleBarCode}
            >
              条码
            </Button>
            <Button
              onClick={completeMaterial}
              disabled={selectLot.length === 0}
              style={{
                background: 'rgb(0, 212, 205)', color: '#fff', borderColor: 'rgb(0, 212, 205)',
              }}>
              {intl.get(`${modelPrompt}.button.finish`).d('完工')}
            </Button>
          </>
        }
      />
      <CardLayout.Content>
        {(!showBarCode)&&isArray(materialDataManual) &&
          materialDataManual.map(item => (
            <Row className={styles.materialContent}>
              <Col className={styles.materialLot} span={24}>
                <div className={styles.materialLotContent} style={{ padding: 0 }} >
                  <div style={{ marginBottom: '1px', backgroundColor: 'rgb(59,131,173)', height: '40%', display: 'flex', alignItems: 'center', paddingLeft: '10%' }}>
                    {item.materialCode} &nbsp; <span style={{ color: 'rgb(163, 197, 216)', fontSize: '13px' }}>{item.materialName}</span>
                  </div>
                  <div>
                    <Row style={{ marginTop: '9px' }}>
                      <Col span={10} offset={1} style={{ display: 'flex' }}>
                        <div
                          className={[`${styles.textAuto}`, `${styles.label}`].join(' ')}>单包重量:</div>
                        <NumberField
                          min={0}
                          onChange={(val) => handleChangeEndQty(val, item, "tailUnitWeightQty")}
                          value={item.tailUnitWeightQty}
                          className={styles.textAutoValue}
                          style={{ backgroundColor: 'rgba(56, 112, 143, 1)', margin: '0 10px' }} />
                      </Col>
                      <Col span={10} offset={1} style={{ display: 'flex' }}>
                        <div
                          className={[`${styles.textAuto}`, `${styles.label}`].join(' ')}>条码个数:</div>
                        <NumberField
                          min={0}
                          precision={0}
                          onChange={(val) => handleChangeEndQty(val, item, "tailBarcodeQty")}
                          value={item.tailBarcodeQty}
                          suffix={<span className={styles.textAuto}>个</span>}
                          className={styles.textAutoValue}
                          style={{ backgroundColor: 'rgba(56, 112, 143, 1)', margin: '0 10px' }} />
                      </Col>
                    </Row>
                    <Row style={{ marginTop: '9px' }}>
                      <Col span={10} offset={1} style={{ display: 'flex' }}>
                        <div
                          className={[`${styles.textAuto}`, `${styles.label}`].join(' ')}>工艺批次:</div>
                        {props.eoData?.productionBatchFlag!=='N'&&
                        <TextField
                          onChange={(val) => handleChangeEndQty(val, item, "productionBatch")}
                          value={item.productionBatch}
                          className={styles.textAutoValue}
                          showValidation={ShowValidation.tooltip}
                          style={{ backgroundColor: 'rgba(56, 112, 143, 1)', margin: '0 10px' }} />
                        }
                      </Col>
                    </Row>
                    {selectLot.some(e => e.materialId === item.materialId) && <img style={{ position: 'absolute', right: 0, bottom: 0, width: 30, height: 30 }} src={cardSvg} alt='' />}
                  </div>
                </div>
              </Col>
            </Row>
          ))}
        {/* 展示完工条码 */}
        {showBarCode && <div className={styles.ProcessingCompleted}>
          <Table
            dataSet={detailDs}
            columns={column} />
        </div>}
      </CardLayout.Content>
    </CardLayout.Layout>
  );
});

export default formatterCollections({ code: ['tarzan.hmes.operationPlatform.LineSideSilo', 'model.org.monitor'] })(LineSideSilo);
