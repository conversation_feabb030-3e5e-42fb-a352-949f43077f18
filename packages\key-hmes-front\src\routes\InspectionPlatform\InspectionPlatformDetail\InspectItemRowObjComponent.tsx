/**
 * @Description: 检验平台-检验项目行模式带对象
 * @Author: <<EMAIL>>
 * @Date: 2023-02-14 15:23:08
 * @LastEditTime: 2023-05-18 16:59:21
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect } from 'react';
import intl from 'utils/intl';
import {
  Col,
  DateTimePicker,
  Form,
  NumberField,
  Currency,
  Row,
  Select,
  Table,
  TextField,
  Lov,
  Attachment,
  Button,
} from 'choerodon-ui/pro';
import { Popconfirm, Tag } from 'choerodon-ui';
import uuid from 'uuid/v4';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/lib/form/enum';
import { isNumber } from 'lodash';
import { FuncType, ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useDataSetEvent } from 'utils/hooks';
import { BASIC } from '@utils/config';
import NcRecordComponent from './NcRecordComponent';
import styles from './index.modules.less';

const { Option } = Select;
const { ItemGroup, Item } = Form;

const modelPrompt = 'tarzan.qms.inspectionPlatform';

const InspectItemRowObjComponent = props => {
  const {
    NcRecordDimension,
    docLineData,
    path,
    canEdit,
    inspectInfoDS,
    inspectItemRowDS,
    inspectItemRowObjValueDS,
    inspectLovDS,
    customizeTable,
    cacheMinWidth,
    setCacheMinWidth,
    onRenderValueList,
    onAutoEnterDefaultValue,
    handleChangeValueColor,
    setDefaultValue,
    handleComputedQty,
    handleChangeInspectItem,
    handleItemScanInspectObj,
    handleChangeNgQty,
    handleBatchChangeData,
  } = props;

  // 是否为自定义抽样显示加减操作
  const [userDefinedFlag, setUserDefinedFlag] = useState(false);

  useEffect(() => {
    const _samplingType = inspectItemRowDS.current?.get('samplingType');
    setUserDefinedFlag(_samplingType === 'USER_DEFINED_SAMPLING');
  }, [inspectItemRowDS.current]);

  const handleInspectValueUpdate = ({ dataSet }) => {
    const _dataType = inspectItemRowDS.current?.get('dataType');
    if (_dataType === 'CALCULATE_FORMULA') {
      inspectItemRowDS.current.set('taskLineObjects', dataSet.data || []);
    }
  };
  useDataSetEvent(inspectItemRowObjValueDS, 'update', handleInspectValueUpdate);

  // 切换当前检验项行
  const handleChangeCurrentLine = () => {
    const _dataType = inspectItemRowDS.current?.get('dataType');
    const _samplingType = inspectItemRowDS.current?.get('samplingType');
    setUserDefinedFlag(_samplingType === 'USER_DEFINED_SAMPLING');
    if (_dataType === 'CALCULATE_FORMULA') {
      const _taskLineObjects = inspectItemRowDS.current.get('taskLineObjects') || [];
      inspectItemRowObjValueDS.loadData(_taskLineObjects);
    } else {
      let _dataQty = 0;
      if (
        inspectItemRowDS.current?.get('dataQtyDisposition') === 'DATA' ||
        inspectItemRowDS.current?.get('dataQtyDisposition') === 'SAMPLE'
      ) {
        _dataQty = 1;
      } else {
        _dataQty = Number(inspectItemRowDS.current?.get('dataQty') || 0);
      }

      if (_dataQty > 0) {
        setCacheMinWidth(_dataQty * 71 + 32);
      }
      // 当前检验对象存在时切换检验项当前行，自动添加对应数据
      const _sourceObjectObj = inspectLovDS.current?.get('sourceObjectObj');
      if (_sourceObjectObj?.inspectObjectId) {
        const _existCurObjFlag = inspectItemRowObjValueDS.records.find(
          item => item.get('sourceObjectCode') === _sourceObjectObj.sourceObjectCode,
        );
        if (!_existCurObjFlag) {
          setDefaultValue();
          // 判断是不是有值的行和抽样数相等
          const _emptyRecords = inspectItemRowObjValueDS.records.filter(
            item => !item.get('sourceObjectCode'),
          );
          if (_emptyRecords.length > 0) {
            _emptyRecords[0].set('inspectObjectId', _sourceObjectObj.inspectObjectId);
            _emptyRecords[0].init('sourceObjectCode', _sourceObjectObj.sourceObjectCode);
            _emptyRecords[0].set('sequence', _sourceObjectObj.sequence);
            _emptyRecords[0].init('productionBatch', _sourceObjectObj.productionBatch);
            _emptyRecords[0].init('numberPlate', _sourceObjectObj.numberPlate);
            _emptyRecords[0].init('stoveFloorDesc', _sourceObjectObj.stoveFloorDesc);
            _emptyRecords[0].init('layerLevelDesc', _sourceObjectObj.layerLevelDesc);
            // onAutoEnterDefaultValue([_emptyRecords[0]]);
          } else if (_samplingType === 'USER_DEFINED_SAMPLING') {
            handleAdd(
              {
                inspectObjectId: _sourceObjectObj.inspectObjectId,
                sourceObjectCode: _sourceObjectObj.sourceObjectCode,
                sequence: _sourceObjectObj.sequence,
                productionBatch: _sourceObjectObj.productionBatch,
                numberPlate: _sourceObjectObj.numberPlate,
                stoveFloorDesc: _sourceObjectObj.stoveFloorDesc,
                layerLevelDesc: _sourceObjectObj.layerLevelDesc,
              },
              true,
            );
          }
        }
      }
    }
  };

  // 添加检验对象
  const handleAdd = (params = {}, selectFlag = false) => {
    const _fieldName = inspectItemRowDS.current?.get('fieldName');
    const _requiredFlag = inspectItemRowDS.current?.get('requiredFlag');
    const _dataType = inspectItemRowDS.current?.get('dataType');
    const newRecord = inspectItemRowObjValueDS.create({
      cacheInspectObjectId: uuid(),
      fieldName: _fieldName,
      requiredFlag: _requiredFlag,
      ...params,
    });
    inspectItemRowDS.current?.set('samplingQty', inspectItemRowObjValueDS.length);
    handleComputedQty(_fieldName, _dataType, true, true, false, false);
    if (selectFlag) {
      setDefaultValue();
      onAutoEnterDefaultValue([newRecord]);
    }
  };

  // 删除检验对象
  const handleDelete = record => {
    // 记录删除dtlId
    const _itemColUpdateCacheObj = inspectInfoDS.getState('itemColUpdateCacheObj') || {};
    const _inspectDocLineActDtlIds = _itemColUpdateCacheObj?.deleteDtlIds || [];
    const _dtlIds = record.get('dtlIds') || [];
    _dtlIds.forEach(dtlId => {
      _inspectDocLineActDtlIds.push(dtlId);
    });
    _itemColUpdateCacheObj.deleteDtlIds = _inspectDocLineActDtlIds;
    inspectInfoDS.setState('itemColUpdateCacheObj', _itemColUpdateCacheObj);

    inspectItemRowObjValueDS.remove(record);
    inspectItemRowDS.current?.set('samplingQty', inspectItemRowObjValueDS.length);
    const _fieldName = inspectItemRowDS.current?.get('fieldName');
    const _dataType = inspectItemRowDS.current?.get('dataType');
    handleComputedQty(_fieldName, _dataType, true, true, false, false);
  };

  const attachmentProps: any = {
    bucketName: 'qms',
    bucketDirectory: 'inspection-platform',
    accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*'],
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const columns: ColumnProps[] = [
    {
      name: 'sequence',
      width: 60,
      align: ColumnAlign.center,
    },
    {
      name: 'inspectItemDesc',
      minWidth: 120,
      renderer: ({ value, record }) => {
        return (
          <span>
            {record?.get('requiredFlag') === 'Y' && <span style={{ color: 'red' }}>*&nbsp;</span>}
            {value}
          </span>
        );
      },
    },
    {
      name: 'inspectItemTypeDesc',
    },
    {
      name: 'inspectStandardSource',
    },
    {
      name: 'inspectToolAndMethod',
    },
    {
      name: 'acceptStandard',
    },
    {
      name: 'samplingQty',
      minWidth: 150,
      align: ColumnAlign.left,
      renderer: ({ value, record }) => {
        if (!record) {
          return;
        }
        return (
          <span>
            {value}&nbsp;{record?.get('uomName')}
          </span>
        );
      },
    },
    {
      name: 'valueRange',
      width: 180,
      title: intl.get(`${modelPrompt}.model.line.valueRange`).d('符合值/不符合值/预警值'),
      renderer: ({ record }) => {
        return (
          <>
            {(record?.get('trueValues') || []).map(item => (
              <Tag color="green">{item}</Tag>
            ))}
            {(record?.get('falseValues') || []).map(item => (
              <Tag color="red">{item}</Tag>
            ))}
            {(record?.get('warningValues') || []).map(item => (
              <Tag color="yellow">{item}</Tag>
            ))}
          </>
        );
      },
    },
    {
      name: 'okQty',
    },
    {
      name: 'ngQty',
      editor: record =>
        inspectInfoDS.getState('canEdit') &&
        ['TEXT', 'DATE'].includes(record?.get('dataType')) && (
          <NumberField
            onChange={(newValue, oldValue) => handleChangeNgQty(newValue, oldValue, '', record)}
          />
        ),
    },
    {
      name: 'inspectResult',
      header: (...args: Array<any>) => {
        return (
          <a onClick={() => handleBatchChangeData('BATCH_RESULT')} style={{ cursor: 'pointer' }}>
            {args[2]}
          </a>
        );
      },
      align: ColumnAlign.center,
      width: 120,
      renderer: ({ value, record }) => {
        if (!record) {
          return '';
        }
        return (
          <Select
            record={record}
            name="inspectResult"
            readOnly={!inspectInfoDS.getState('canEdit')}
            style={{
              verticalAlign: 'baseline',
              backgroundColor:
                value === 'OK' ? 'rgb(230, 255, 234)' : value === 'NG' ? 'rgb(255, 240, 240)' : '',
            }}
          />
        );
      },
    },
    {
      name: 'sourceInspectValue',
      hidden: `${inspectInfoDS.current?.get('inspectTimes')}` === '1',
    },
    {
      name: 'inspectValueRecord',
    },
    {
      name: 'remark',
      editor: () => inspectInfoDS.getState('canEdit'),
      width: 120,
    },
    {
      header: intl.get('tarzan.common.label.action').d('操作'),
      align: ColumnAlign.center,
      width: 380,
      renderer: ({ record }) => {
        return (
          <>
            <Button
              funcType={FuncType.link}
              color={ButtonColor.primary}
              disabled={['CANCEL', 'COMPLETED'].includes(
                inspectInfoDS.current?.get('inspectTaskStatus'),
              )}
              onClick={() => {
                handleAddInspectValue(record);
              }}
            >
              {intl.get(`${modelPrompt}.add`).d('新增')}
            </Button>
            <Button
              funcType={FuncType.link}
              color={ButtonColor.primary}
              disabled={
                Number(record?.get('dataQty')) === 1 ||
                ['CANCEL', 'COMPLETED'].includes(inspectInfoDS.current?.get('inspectTaskStatus'))
              }
              onClick={() => {
                handleDeleteInspectValue(record);
              }}
            >
              {intl.get(`${modelPrompt}.delete`).d('删除')}
            </Button>
            <Attachment
              {...attachmentProps}
              record={record}
              name="enclosure"
              readOnly
              disabled={!inspectInfoDS.getState('canEdit')}
            />
            <Attachment
              {...attachmentProps}
              record={record}
              name="actEnclosure"
              disabled={!inspectInfoDS.getState('canEdit')}
            />
            <NcRecordComponent
              canEdit={inspectInfoDS.getState('canEdit')}
              type="text"
              visible={
                inspectInfoDS.current?.get('inspectNcRecordDimension') ===
                  NcRecordDimension.itemNc && !!record?.get('inspectTaskLineId')
              }
              customizeTable={customizeTable}
              queryParams={{
                inspectTaskId: inspectInfoDS.current?.get('inspectTaskId'),
                inspectDocId: inspectInfoDS.current?.get('inspectDocId'),
                inspectTaskLineId: record?.get('inspectTaskLineId'),
                inspectItemId: record?.get('inspectItemId'),
                inspectDocLineId: record?.get('inspectDocLineId'),
                inspectItemDesc: record?.get('inspectItemDesc'),
                inspectNcRecordDimension: inspectInfoDS.current?.get('inspectNcRecordDimension'),
              }}
              style={{ marginLeft: '0.16rem' }}
            />
          </>
        );
      },
    },
  ];

  const objColumns: ColumnProps[] = [
    {
      header: ({ dataSet }) => {
        const fieldName = dataSet.toData().length ? dataSet.toData()[0].fieldName : '';
        const parentList = dataSet.parent.toData();
        const list = fieldName ? parentList.filter(item => item.fieldName === fieldName) : [];
        const disabled = !!(
          list.length && list[0].samplingQty >= inspectItemRowDS.getState('inspectSumQty')
        );

        return (
          <PermissionButton
            type="c7n-pro"
            icon="add"
            onClick={handleAdd}
            disabled={disabled || !canEdit}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `inspectionPlatform.dist.button.line.add`,
                type: 'button',
                meaning: '详情页-添加删除行',
              },
            ]}
          />
        );
      },
      name: 'add',
      align: ColumnAlign.center,
      width: 50,
      hidden: !userDefinedFlag,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDelete(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
            disabled={!canEdit}
            permissionList={[
              {
                code: `inspectionPlatform.dist.button.line.add`,
                type: 'button',
                meaning: '详情页-添加删除行',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'sourceObjectCode',
      width: 150,
      renderer: ({ value, record }) => {
        const _canEdit = inspectInfoDS.getState('canEdit');
        const _resultDimension = inspectInfoDS.current?.get('resultDimension');
        const _dataType = inspectItemRowDS.current?.get('dataType');
        if (record?.get('cacheInspectObjectId') && _resultDimension === 'RECORD_SAMPLE_VALUE') {
          return (
            <TextField
              record={record}
              name="sourceObjectCode"
              disabled={!_canEdit || _dataType === 'CALCULATE_FORMULA'}
              onChange={(val, oldValue) => handleChangeInspectItem(val, oldValue, record)}
              onEnterDown={event => handleItemScanInspectObj(event, record)}
              style={{ verticalAlign: 'baseline', width: '100%' }}
            />
          );
        }
        return value;
      },
    },
    {
      name: 'productionBatch',
      width: 120,
    },
    {
      name: 'numberPlate',
      width: 120,
    },
    {
      name: 'stoveFloorDesc',
      width: 120,
    },
    {
      name: 'layerLevelDesc',
      width: 120,
    },
    {
      name: 'inspectValueRecord',
      width: cacheMinWidth,
      minWidth: 180,
      defaultWidth: cacheMinWidth,
      renderer: ({ record }) => {
        if (!record) {
          return;
        }
        const _canEdit = inspectInfoDS.getState('canEdit');
        const _dataType = inspectItemRowDS.current?.get('dataType');
        const _decimalNumber = inspectItemRowDS.current?.get('decimalNumber');

        let _dataQty = 0;
        if (
          inspectItemRowDS.current?.get('dataQtyDisposition') === 'DATA' ||
          inspectItemRowDS.current?.get('dataQtyDisposition') === 'SAMPLE'
        ) {
          _dataQty = 1;
        } else {
          _dataQty = Number(inspectItemRowDS.current?.get('dataQty') || 0);
        }

        const _fieldName = inspectItemRowDS.current?.get('fieldName');
        const _count =
          _dataType === 'CALCULATE_FORMULA'
            ? Number(record.get(`${_fieldName}_FORMULA_COUNT`) || 0)
            : _dataQty;
        const _dataQtyValue: Array<any> = [];
        for (let i = 0; i < _count; i++) {
          _dataQtyValue.push(`${_fieldName}_VALUE${i}`);
        }
        const _samplingQty = Number(inspectItemRowDS.current?.get('samplingQty') || 0);
        const _trueValues = inspectItemRowDS.current?.get('trueValues') || [];
        const _falseValues = inspectItemRowDS.current?.get('falseValues') || [];
        const _valueLists = inspectItemRowDS.current?.get('valueLists') || [];

        if(_dataType === 'DECISION_VALUE' || _dataType === 'VALUE_LIST'){
          const valueList = _dataType === 'DECISION_VALUE'? _trueValues.concat(_falseValues):_valueLists;
          _dataQtyValue.forEach((valueName) => {
            const value = record.get(valueName);
            if(valueList.length && !valueList?.includes(value) && value !== undefined){
              record.set(valueName, null);
              inspectItemRowDS.current?.set('inspectValueRecord', null);
            }
          });
        }

        if(_dataType === 'VALUE'){
          _dataQtyValue.forEach((valueName) => {
            const value = record.get(valueName);
            if(value !== undefined){
              handleChangeValueColor(_fieldName, value, valueName, record, _dataType);
            }
          });
        }

        // 存在检验对象时需先扫描检验对象才可录入
        const _resultDimension = inspectInfoDS.current?.get('resultDimension');
        const _objectDisabled =
          _resultDimension === 'RECORD_SAMPLE_VALUE' && !record.get('inspectObjectId');
        const _disabled = !!record.get(`${_fieldName}_DISABLED`) || _objectDisabled;

        // 计算列宽
        const _valueWidth = `${100 / _count}%`;

        return (
          <ItemGroup>
            {_dataQtyValue.map((valueName, index) => {
              if (_dataType === 'VALUE' && !_decimalNumber && _decimalNumber !== 0) {
                return (
                  <Item name={valueName}>
                    <NumberField
                      record={record}
                      name={valueName}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      className={
                        record?.get(`${valueName}_COLOR`)
                          ? `${styles[`input-color-${record?.get(`${valueName}_COLOR`)}`]} ${
                              styles['number-input-left']
                            }`
                          : styles['number-input-left']
                      }
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={(value, oldValue) => {
                        if (
                          !(
                            (value === undefined || value === null) &&
                            (oldValue === undefined || oldValue === null)
                          )
                        ) {
                          handleChangeValueColor(_fieldName, value, valueName, record, _dataType);
                        }
                      }}
                    />
                  </Item>
                );
              }
              if (_dataType === 'VALUE' && isNumber(_decimalNumber) && _decimalNumber >= 0) {
                return (
                  <Item name={valueName}>
                    <Currency
                      record={record}
                      name={valueName}
                      precision={_decimalNumber > 6 ? 6 : _decimalNumber}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      className={
                        record?.get(`${valueName}_COLOR`)
                          ? `${styles[`input-color-${record?.get(`${valueName}_COLOR`)}`]} ${
                              styles['number-input-left']
                            }`
                          : styles['number-input-left']
                      }
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={(value, oldValue) => {
                        if (
                          !(
                            (value === undefined || value === null) &&
                            (oldValue === undefined || oldValue === null)
                          )
                        ) {
                          handleChangeValueColor(_fieldName, value, valueName, record, _dataType);
                        }
                      }}
                    />
                  </Item>
                );
              }
              if (_dataType === 'DECISION_VALUE') {
                return (
                  <Item name={valueName}>
                    <Select
                      record={record}
                      name={valueName}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      className={
                        record?.get(`${valueName}_COLOR`)
                          ? styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                          : ''
                      }
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={value =>
                        handleChangeValueColor(_fieldName, value, valueName, record, _dataType)
                      }
                    >
                      {_trueValues.concat(_falseValues).map(item => (
                        <Option value={item} key={item}>
                          {item}
                        </Option>
                      ))}
                    </Select>
                  </Item>
                );
              }
              if (_dataType === 'TEXT') {
                return (
                  <Item name={valueName}>
                    <TextField
                      record={record}
                      name={valueName}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={() => handleComputedQty(_fieldName, _dataType)}
                    />
                  </Item>
                );
              }
              if (_dataType === 'VALUE_LIST') {
                return (
                  <Item name={valueName}>
                    <Select
                      record={record}
                      name={valueName}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      className={
                        record?.get(`${valueName}_COLOR`)
                          ? styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                          : ''
                      }
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={value =>
                        handleChangeValueColor(_fieldName, value, valueName, record, _dataType)
                      }
                    >
                      {_valueLists.map(item => (
                        <Option value={item} key={item}>
                          {item}
                        </Option>
                      ))}
                    </Select>
                  </Item>
                );
              }
              if (_dataType === 'DATE') {
                return (
                  <Item name={valueName}>
                    <DateTimePicker
                      record={record}
                      name={valueName}
                      style={{
                        width: _valueWidth,
                        verticalAlign: 'baseline',
                        marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                      }}
                      disabled={!_canEdit || _samplingQty === 0 || _disabled}
                      onChange={() => handleComputedQty(_fieldName, _dataType)}
                    />
                  </Item>
                );
              }
              if (_dataType === 'CALCULATE_FORMULA') {
                const _formulaValue = record?.get(valueName);
                return (
                  (_formulaValue || _formulaValue === 0) && (
                    <Item name={valueName}>
                      <TextField
                        record={record}
                        name={valueName}
                        style={{
                          width: _valueWidth,
                          verticalAlign: 'baseline',
                          marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                        }}
                        className={
                          record?.get(`${valueName}_COLOR`)
                            ? styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                            : ''
                        }
                        disabled
                      />
                    </Item>
                  )
                );
              }
              return '';
            })}
          </ItemGroup>
        );
      },
    },
    {
      name: 'remark',
      editor: record => {
        // 存在检验对象时需先扫描检验对象才可录入
        const _resultDimension = inspectInfoDS.current?.get('resultDimension');
        const _dataType = inspectItemRowDS.current?.get('dataType');
        const _objectDisabled =
          _dataType !== 'CALCULATE_FORMULA' &&
          _resultDimension === 'RECORD_SAMPLE_VALUE' &&
          !record.get('inspectObjectId');
        return inspectInfoDS.getState('canEdit') && !_objectDisabled;
      },
    },
  ];

  const handleAddInspectValue = record => {
    const _fieldName = record?.get('fieldName');
    const _dataQty = record?.get('dataQty');

    record?.set('dataQty', Number(_dataQty) + 1);

    inspectItemRowObjValueDS.addField(`${_fieldName}_VALUE${_dataQty}`, {
      name: `${_fieldName}_VALUE${_dataQty}`,
      type: 'string',
      label: `${intl.get(`${modelPrompt}.model.line.inspectValue`).d('检测值')}`,
    });
    setDefaultValue();
  };

  const handleDeleteInspectValue = record => {
    const _fieldName = record?.get('fieldName');
    const _dataQty = record?.get('dataQty');
    record?.set('dataQty', Number(_dataQty) - 1);
    record?.set(`${_fieldName}_VALUE${Number(_dataQty) - 1}`, null);
  };

  return (
    <>
      <Row>
        <Col span={16} />
        <Col span={8}>
          <Form
            dataSet={inspectLovDS}
            columns={1}
            labelWidth={90}
            labelLayout={LabelLayout.horizontal}
            disabled={!inspectInfoDS.getState('canEdit')}
          >
            <Lov name="sourceObjectObj" />
          </Form>
        </Col>
      </Row>
      <Row>
        <Col span={16}>
          <div>
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.BLINE`,
              },
              <Table
                dataSet={inspectItemRowDS}
                columns={columns}
                highLightRow
                rowHeight={30}
                style={{ width: '98%', height: 360 }}
                onRow={() => {
                  return {
                    onClick: () => handleChangeCurrentLine(),
                  };
                }}
                customizedCode="jyptbl"
                virtual
                virtualCell
              />,
            )}
          </div>
        </Col>
        <Col span={8}>
          {customizeTable(
            {
              code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_ITEM.VALUE`,
            },
            <Table
              dataSet={inspectItemRowObjValueDS}
              columns={objColumns}
              rowHeight={30}
              customizedCode="jyptv"
              virtual
              virtualCell
              style={{ height: 360 }}
            />,
          )}
        </Col>
      </Row>
    </>
  );
};

export default InspectItemRowObjComponent;
