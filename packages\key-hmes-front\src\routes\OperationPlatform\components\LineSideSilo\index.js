// 机台物料
import React, { useState, useEffect, useRef } from 'react';
import {
  Button,
  Row,
  Col,
  NumberField,
  TextField,
  Modal,
  Pagination,
} from 'choerodon-ui/pro';
// import moment from 'moment';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import { Icon } from 'hzero-ui';
import { isArray, isEmpty } from 'lodash';
import notification from 'utils/notification';
import uuid from 'uuid/v4';
import { useRequest } from '@components/tarzan-hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import cardSvg from './check.png';
// import { detailDS, tableDs, saveTableDs } from './stores/LineSideSiloDS';
import { CardLayout } from '../commonComponents';
import styles from './index.modules.less';
import { GetInfoManual, GetInfoAuto, FeedManual, FeedAuto, StartTime, EndTime, ReSave } from './services';

const modelPrompt = 'tarzan.hmes.operationPlatform.LineSideSilo';

const LineSideSilo = observer(props => {

  const { run: getInfoManual, loading: getInfoManualLoading } = useRequest(GetInfoManual(), {
    manual: true,
    needPromise: true,
  });
  const { run: getInfoAuto, loading: getInfoAutoLoading } = useRequest(GetInfoAuto(), {
    manual: true,
    needPromise: true,
  });
  const { run: startTime, loading: startTimeLoading } = useRequest(StartTime(), {
    manual: true,
    needPromise: true,
  });
  const { run: endTime, loading: endTimeLoading } = useRequest(EndTime(), {
    manual: true,
    needPromise: true,
  });
  const { run: reSave, loading: reSaveLoading } = useRequest(ReSave(), { manual: true, needPromise: true });
  const { run: feedManual, loading: feedManualLoading } = useRequest(FeedManual(), {
    manual: true,
    needPromise: true,
  });
  const { run: feedAuto, loading: feedAutoLoading } = useRequest(FeedAuto(), {
    manual: true,
    needPromise: true,
  });

  const [selectLot, setSelectLot] = useState([]);
  const [title, setTitle] = useState('')
  const [loading, setLoading] = useState(false);
  const [cacheEoId, setCacheEoId] = useState(null); // 工单数据
  const [materialDataManual, setMaterialDataManual] = useState([]); // 线边料仓数据
  const [materialDataAuto, setMaterialDataAuto] = useState([]); // 计时投料数据
  const [operationInputMethod, setOperationInputMethod] = useState(''); // 投料方式
  const [reInputFlag, setReInputFlag] = useState(false); // 是否有重新录入数据的权限
  const inputRef = useRef(null)
  // // 字体大小控制
  // useEffect(() => {
  //   // document.getElementById('lineSideSilo').style.fontSize = `${10 +
  //   //   props.newLayout?.filter(item => item.i === '2')[0]?.w}px`;
  // }, [props.newLayout]);


  useEffect(() => {
    // console.log('props.loginWkcInfo.operationDTOList[0]?.operationInputMethod', props.loginWkcInfo.operationDTOList[0]?.operationInputMethod)
    if (props.eoData?.eo?.eoId && cacheEoId !== props.eoData?.eo?.eoId) {
      setCacheEoId(props.eoData?.eoId);
      if (props.loginWkcInfo.operationDTOList[0]?.operationInputMethod === 'MANUAL') {
        setMaterialDataAuto([])
        queryMaterialManual();
      } else if (props.loginWkcInfo.operationDTOList[0]?.operationInputMethod === 'AUTO') {
        // 计时投料
        setMaterialDataManual([])
        queryMaterialAuto()
      }
    }
    if (!props.eoData?.eo?.eoId) {
      setMaterialDataManual([])
      setMaterialDataAuto([])
    }
  }, [props.eoData?.eo?.eoId]);

  useEffect(() => {
    setOperationInputMethod(props.loginWkcInfo.operationDTOList[0]?.operationInputMethod)
    setReInputFlag(props.loginWkcInfo.saveFlag)
    if (props.loginWkcInfo.operationDTOList[0]?.operationInputMethod === 'MANUAL') {
      setTitle(intl.get(`${modelPrompt}.title`).d('线边料仓'))
    } else if (props.loginWkcInfo.operationDTOList[0]?.operationInputMethod === 'AUTO') {
      setTitle(intl.get(`${modelPrompt}.title.auto`).d('计时投料'))
    }
  }, [props.loginWkcInfo.operationDTOList])


  useEffect(() => {
    setLoading(props.spin);
  }, [props.spin]);

  // 查询物料
  const queryMaterialManual = async () => {
    const res = await getInfoManual({
      params: props.eoData,
    })
    if (res && res.failed) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'ERROR',
        recordType: 'query',
        message: res.message,
      });
    } else {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'query',
        message: '线边料仓信息获取成功',
      });
      res.forEach(item => {
        if (item.materialLotList) {
          item.materialLotList.forEach(lot => {
            lot.uuid = uuid();
          })
        }
      })
      setMaterialDataManual(res.map(e => ({ ...e, page: 1 })));
    }
  };
  const queryMaterialAuto = async () => {
    const res = await getInfoAuto({
      params: props.eoData,
    })
    if (res && res.failed) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'ERROR',
        recordType: 'query',
        message: res.message,
      });
    } else {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'query',
        message: '计时投料卡片信息获取成功',
      });
      res.forEach(item => {
        item.hmeEoAutoAssemble = item.hmeEoAutoAssemble || {}
        if (!isEmpty(item.hmeEoAutoAssemble)) {
          if (Number(item.hmeEoAutoAssemble.startQty) && Number(item.hmeEoAutoAssemble.endQty)) {
            item.difQty = Number(item.hmeEoAutoAssemble.endQty) - Number(item.hmeEoAutoAssemble.startQty)
          }
        }
      })
      setMaterialDataAuto(res);
    }
  }

  // 选中物料批
  const handleCheckLot = (e, materialItem, lotItem) => {
    let flag = false;
    if (selectLot.includes(lotItem.uuid)) {
      flag = false
      setSelectLot(selectLot.filter(item => item !== lotItem.uuid))
    } else {
      flag = true
      setSelectLot([...selectLot, lotItem.uuid])
    }
    const materialDataManualNew = [...(materialDataManual || [])];
    materialDataManualNew.forEach(item => {
      if (item.materialCode === materialItem.materialCode) {
        item.materialLotList.forEach(lot => {
          if (lot.uuid === lotItem.uuid) {
            lot.inputQty = lot.inputQty || lot.primaryUomQty;
            lot.checked = flag;
          }
        })
      }
    })
    setMaterialDataManual(materialDataManualNew)
  }

  // 输入数量
  const handleNumber = (val, materialItem, lotItem) => {
    const materialDataManualNew = [...(materialDataManual || [])];
    if (val > lotItem.primaryUomQty && lotItem.stoveFlag !== "Y") {
      notification.error({
        message: '数量不能大于现有量',
      });
      materialDataManualNew.forEach(item => {
        if (item.materialCode === materialItem.materialCode) {
          item.materialLotList.forEach(lot => {
            if (lot.uuid === lotItem.uuid) {
              lot.inputQty = lot.primaryUomQty;
            }
          })
        }
      })
    } else {
      materialDataManualNew.forEach(item => {
        if (item.materialCode === materialItem.materialCode) {
          item.materialLotList.forEach(lot => {
            if (lot.uuid === lotItem.uuid) {
              lot.inputQty = val;
            }
          })
        }
      })
    }
    setMaterialDataManual(materialDataManualNew)
  }

  const handleFeedValidate = async ()=>{
    const contentList=[];
    const materialDataManualNew = [...(materialDataManual || [])];
    materialDataManualNew.forEach(item => {
      const lotList=[];
      (item.materialLotList || []).forEach(lot => {
        if (lot.checked) {
          if((Math.round(lot.inputQty/lot.rate))===0 || (Math.round(lot.primaryUomQty/lot.rate))===0){
            lotList.push({
              lot:lot.lot,
            })
          }
        }
      })
      if(lotList.length){
        contentList.push({
          materialName:item.materialName,
          lot:lotList,
        })
      }
    })
    const html = contentList.map(e=>{
      const lotHtml=e.lot.map(item=>{
        return <span>批次:{item.lot};</span>
      })
      return <p>物料名称:{e.materialName};{lotHtml}</p>
    });
    if(contentList.length){
      Modal.confirm({
        title:(
          <span style={{ color: 'white' }}>
            <span>坩埚物料<br></br>
              {html}</span>
            <span>本次投料数量为0，是否继续操作？</span>
          </span>
        ),
        style: {
          width: 960,
        },
        contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
        okProps: {
          style: {
            background: '#00D4CD',
            color: 'white',
            borderColor: '#00d4cd',
          },
        },
        cancelProps: {
          style: {
            background: '#50819c',
            color: 'white',
          },
        },
        onOk: async () => {
          await handleFeedManual();
        },
      });
    }
    else{
      await handleFeedManual();
    }
  }

  const handleFeedManual = async()=>{
    const feedList = []
    let res = null;
    const materialDataManualNew = [...(materialDataManual || [])];
    materialDataManualNew.forEach(item => {
      (item.materialLotList || []).forEach(lot => {
        if (lot.checked) {
          const obj = { ...item, ...lot }
          delete obj.materialLotList
          feedList.push(obj)
        }
      })
    })
    res = await feedManual({
      params: feedList,
    })
    if (res && !res.success) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'ERROR',
        recordType: 'query',
        message: res.message || '投料失败',
      });
    } else {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'query',
        message: '操作成功',
      });
      // 刷新变量
      props.handleRefresh({
        refresh: true,
      })
      if (operationInputMethod === 'MANUAL') {
        setSelectLot([]);
        queryMaterialManual()
      }
    }
  }

  // 投料
  const handleFeed = async () => {
    const feedList = []
    let res = null;
    if (operationInputMethod === 'MANUAL') {
      // 校验是否弹框
      handleFeedValidate();
    } else if (operationInputMethod === 'AUTO') {
      const materialDataAutoNew = materialDataAuto
      materialDataAutoNew.forEach(item => {
        if (item.difQty) {
          const obj = {
            ...item,
            locatorId: props.loginWkcInfo.locatorId,
          }
          feedList.push(obj)
        }
      })
      res = await feedAuto({
        params: feedList,
      })
      if (res && !res.success) {
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'ERROR',
          recordType: 'query',
          message: res.message || '投料失败',
        });
      } else {
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'SUCCESS',
          recordType: 'query',
          message: '操作成功',
        });
        // 刷新变量
        props.handleRefresh({
          refresh: true,
        })
        if (operationInputMethod === 'AUTO') {
          queryMaterialAuto()
        }
      }
    }
  }

  const handeReSave = async (item) => {
    if (!item.hmeEoAutoAssemble?.startQty || !item.hmeEoAutoAssemble?.endQty) {
      return
    }
    const res = await reSave({
      params: item,
    })
    if (res && res.failed) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'ERROR',
        recordType: 'query',
        message: res.message,
      });
    } else {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'query',
        message: '操作成功',
      });
      queryMaterialAuto()
    }
  }

  const handleReckonTime = async (item, flag) => {
    let res = null;
    if (flag === 'start') {
      if (Number(item.hmeEoAutoAssemble?.startQty)) {
        res = await startTime({
          params: item,
        })
      } else {
        return
      }
    } else if (flag === 'end') {
      if (Number(item.hmeEoAutoAssemble?.endQty)) {
        res = await endTime({
          params: item,
        })
      } else {
        return
      }
    }
    if (res && res.failed) {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'ERROR',
        recordType: 'query',
        message: res.message,
      });
    } else {
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'query',
        message: '操作成功',
      });
      queryMaterialAuto()
    }
  }

  const handleChangeEndQty = (val, item) => {
    const newList = [...materialDataAuto]
    newList.forEach(listItem => {
      if (listItem.materialCode === item.materialCode) {
        if (item.hmeEoAutoAssemble?.startQty < val) {
          listItem.hmeEoAutoAssemble.endQty = val;
        } else {
          listItem.hmeEoAutoAssemble.endQty = null;
        }
      }
    })
    setMaterialDataAuto(newList)
  }
  const handleChangeStartQty = (val, item) => {
    const newList = materialDataAuto
    newList.forEach(listItem => {
      if (listItem.materialCode === item.materialCode) {
        listItem.hmeEoAutoAssemble.startQty = val;
      }
    })
    setMaterialDataAuto(newList)
  }

  const handleSearchProductionBatch = (value) => {
    if(!value){
      if (operationInputMethod === 'MANUAL') {
        setSelectLot([]);
        queryMaterialManual()
      } else if (operationInputMethod === 'AUTO') {
        queryMaterialAuto()
      }
    }else{
      const newData = [...(materialDataManual || [])];
      const searchMaterialList = [];
      for (let i = 0; i < (newData || []).length; i++) {
        const line = newData[i];
        const searchMaterialLotList =[];
        for (let index = 0; index < (line?.materialLotList || []).length; index++) {
          const materialLot = line.materialLotList[index];
          if (materialLot?.productionBatch === value) {
            searchMaterialLotList.push(materialLot);
          }
        }
        if(searchMaterialLotList.length){
          newData[i].materialLotList=searchMaterialLotList;
          searchMaterialList.push(newData[i]);
        }
      }
      if(searchMaterialList.length){
        setMaterialDataManual(searchMaterialList.map(e => ({ ...e, page: 1 })))
        inputRef.current.focus()
        return;
      }
      notification.error({
        message: `扫描工艺批次${value}不存在，请检查！`,
      });
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'ERROR',
        recordType: 'query',
        message: `扫描工艺批次${value}不存在，请检查！`,
      });
      inputRef.current.focus()
    }
  }

  const handleSearchMaterialLot = (value) => {
    const newData = [...(materialDataManual || [])];
    for (let i = 0; i < (newData || []).length; i++) {
      const line = newData[i];
      for (let index = 0; index < (line?.materialLotList || []).length; index++) {
        const materialLot = line.materialLotList[index];
        if (materialLot.materialLotCode === value) {
          materialLot.checked = true;
          materialLot.inputQty = materialLot.primaryUomQty;
          const newList = [materialLot, ...line.materialLotList.filter(item => item.materialLotCode !== value)]
          line.materialLotList = newList;
          setMaterialDataManual(newData.map(e => ({ ...e, page: 1 })))
          setSelectLot([...new Set([...selectLot, materialLot.uuid])])
          inputRef.current.focus()
          return;
        }
      }
    }
    notification.error({
      message: `扫描条码${value}不存在，请检查！`,
    });
    props.handleAddRecords({
      cardId: props.cardId,
      messageType: 'ERROR',
      recordType: 'query',
      message: `扫描条码${value}不存在，请检查！`,
    });
    inputRef.current.focus()
  }

  const handleChangeCurrentMaterialPagination = (page, pageSize, item) => {
    setMaterialDataManual(materialDataManual.map(e => item.materialId === e.materialId ? ({
      ...e,
      page,
    }) : e));
  };

  return (
    <CardLayout.Layout
      spinning={
        getInfoManualLoading ||
        getInfoAutoLoading ||
        feedManualLoading ||
        feedAutoLoading ||
        startTimeLoading ||
        endTimeLoading ||
        reSaveLoading ||
        loading
      } className={styles.lineSideSilo}>
      <CardLayout.Header
        title={title}
        addonAfter={
          <>
            {(operationInputMethod === 'MANUAL' && props.eoData?.completeAreaFlag === 'Y')&&<TextField
              ref={inputRef}
              onEnterDown={e => handleSearchProductionBatch(e.target.value)}
              style={{ marginRight: '8px' }}
              placeholder="请输入工艺批次"
              suffix={<Icon type="scan" style={{ fontSize: 14, color: 'white' }} />}
            />}
            {operationInputMethod === 'MANUAL' && <TextField
              ref={inputRef}
              onEnterDown={e => handleSearchMaterialLot(e.target.value)}
              style={{ marginRight: '8px' }}
              placeholder="请输入条码"
              suffix={<Icon type="scan" style={{ fontSize: 14, color: 'white' }} />}
            />}

            <Button
              onClick={handleFeed}
              disabled={
                operationInputMethod === 'MANUAL' && selectLot.length === 0
                || operationInputMethod === 'AUTO' && materialDataAuto.every(item => !Number(item.difQty))
              }
              style={{
                background: 'rgb(255, 195, 0)', color: '#fff', borderColor: 'rgb(255, 195, 0)',
              }}>
              {intl.get(`${modelPrompt}.button.feed`).d('投料')}
            </Button>
            {/* {operationInputMethod === 'AUTO'&&<Button
              onClick={handleFeed}
              disabled={}
              style={{
                background: 'rgb(255, 195, 0)',color: '#fff',borderColor: 'rgb(255, 195, 0)',
              }}>
              {intl.get(`${modelPrompt}.button.feed`).d('投料')}
            </Button>} */}
          </>
        }
      />
      <CardLayout.Content>
        {isArray(materialDataManual) && operationInputMethod === 'MANUAL' &&
          materialDataManual.map((item) => {
            return (
              <>
                <div className={styles.materialContent}>
                  <div className={styles.materialName}>
                    {item.materialName}
                  </div>
                  <div className={styles.materialLot}>
                    <div className={styles.materialLotContent}>
                      <div>
                        {item.materialCode}/{item.materialName}/{item.revisionCode}
                      </div>
                      <div className={styles.materialLotContentBottom}>
                        {
                          item.materialLotList
                          && item.materialLotList.length
                          && item.materialLotList.slice((item.page - 1) * 6, (item.page * 6)).map((lotItem) => {
                            return (
                              <div className={styles.materialLotItem} onClick={(e) => handleCheckLot(e, item, lotItem)}>
                                <div className={styles.itemTop}>
                                  <div className={styles.lot} >{lotItem.lot}</div>
                                  <NumberField
                                    min={0}
                                    // max={lotItem.primaryUomQty}
                                    value={lotItem.inputQty || lotItem.primaryUomQty}
                                    precision={3}
                                    style={{
                                      background: '#00D4CD', width: '100px', borderColor: '#00D4CD',
                                    }}
                                    onChange={(val) => handleNumber(val, item, lotItem)} />
                                </div>
                                <div className={styles.itemBottom}>
                                  条码：{lotItem.materialLotCode}
                                </div>
                                <div className={styles.itemBottom}>
                                  工艺批次：{lotItem.productionBatch}
                                </div>
                                <div className={styles.itemBottom}>
                                  {lotItem.equipmentCode}-{lotItem.stoveCount}-{lotItem.layerLevelMeaning}
                                </div>
                                {selectLot.includes(lotItem.uuid) && <img src={cardSvg} alt='' />}
                              </div>
                            );
                          })
                        }
                        <Pagination
                          pageSize={6}
                          page={(item.page || 0)}
                          total={item?.materialLotList?.length}
                          showSizeChanger={false}
                          onChange={(page, pageSize) => handleChangeCurrentMaterialPagination(page, pageSize, item)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </>
            );
          })}
        {isArray(materialDataAuto) && operationInputMethod === 'AUTO' &&
          materialDataAuto.map(item => (
            <Row className={styles.materialContent}>
              <Col className={styles.materialName} span={4}>{item.materialName}</Col>
              <Col className={styles.materialLot} span={20}>
                <div className={styles.materialLotContent}>
                  <div style={{ marginBottom: '5px' }}>
                    {item.materialCode}/{item.materialName}/{item.revisionCode}
                  </div>
                  <div>
                    <Row>
                      <Col span={10} style={{ display: 'flex' }} >
                        <div
                          className={[`${styles.textAuto}`, `${styles.label}`].join(' ')}>开始时间</div>
                        <span style={{ marginLeft: '10px' }}
                          className={styles.textAutoValue}>
                          {item.hmeEoAutoAssemble?.startTime}
                        </span>
                      </Col>
                      <Col span={9} style={{ display: 'flex' }}>
                        <div
                          className={[`${styles.textAuto}`, `${styles.label}`].join(' ')}>开始重量</div>
                        <NumberField
                          readOnly={
                            !(
                              (reInputFlag && item.hmeEoAutoAssemble?.endTime) ||
                              !item.hmeEoAutoAssemble?.startTime
                            )
                          }
                          min={0}
                          precision={2}
                          onChange={(val) => handleChangeStartQty(val, item)}
                          value={item.hmeEoAutoAssemble?.startQty}
                          suffix={<span className={styles.textAuto}>kg</span>}
                          className={styles.textAutoValue}
                          style={{ backgroundColor: 'rgba(56, 112, 143, 1)', margin: '0 10px' }} />
                      </Col>
                      <Col span={4} offset={1}>
                        {
                          item.hmeEoAutoAssemble?.startQty &&
                          item.hmeEoAutoAssemble?.endQty && (
                            <Button
                              style={{
                                background: 'rgb(255, 195, 0)', color: '#fff', borderColor: 'rgb(255, 195, 0)',
                              }}>
                              {/* 差异 */}
                              {/* {item.difQty} */}
                              {item.hmeEoAutoAssemble?.endQty - item.hmeEoAutoAssemble?.startQty}
                            </Button>
                          )
                        }

                      </Col>
                    </Row>
                    <Row style={{ marginTop: '5px' }}>
                      <Col span={10} style={{ display: 'flex' }}>
                        <div
                          className={[`${styles.textAuto}`, `${styles.label}`].join(' ')}>结束时间</div>
                        <span style={{ marginLeft: '10px' }}
                          className={styles.textAutoValue}>{item.hmeEoAutoAssemble?.endTime}</span>
                      </Col>
                      <Col span={9} style={{ display: 'flex' }}>
                        <div
                          className={[`${styles.textAuto}`, `${styles.label}`].join(' ')}>结束重量</div>
                        <NumberField
                          readOnly={!reInputFlag && item.hmeEoAutoAssemble?.endTime || !item.hmeEoAutoAssemble?.startTime}
                          min={item.hmeEoAutoAssemble?.startQty}
                          precision={2}
                          onChange={(val) => handleChangeEndQty(val, item)}
                          value={item.hmeEoAutoAssemble?.endQty}
                          suffix={<span className={styles.textAuto}>kg</span>}
                          className={styles.textAutoValue}
                          style={{ backgroundColor: 'rgba(56, 112, 143, 1)', margin: '0 10px' }} />
                      </Col>
                      <Col span={4} offset={1}>
                        {!item.hmeEoAutoAssemble?.startQty && !item.hmeEoAutoAssemble?.startTime &&
                          <Button color='primary'
                            onClick={() => handleReckonTime(item, 'start')}
                            style={{ background: '#00D4CD', color: '#fff', borderColor: '#00D4CD' }}
                          >
                            计时开始
                          </Button>}
                        {item.hmeEoAutoAssemble?.startQty && item.hmeEoAutoAssemble?.startTime && !item.difQty &&
                          <Button
                            onClick={() => handleReckonTime(item, 'end')}
                            style={{ background: 'red', color: '#fff', borderColor: 'red' }}>
                            计时结束
                          </Button>
                        }
                        {
                          item.difQty && reInputFlag && (
                            <Button color='primary' onClick={() => handeReSave(item)} style={{ margin: 0 }}>
                              重新录入
                            </Button>
                          )
                        }
                      </Col>
                    </Row>
                  </div>
                </div>
              </Col>
            </Row>
          ))}
      </CardLayout.Content>
    </CardLayout.Layout>
  );
});

export default formatterCollections({ code: ['tarzan.hmes.operationPlatform.LineSideSilo', 'model.org.monitor'] })(LineSideSilo);
