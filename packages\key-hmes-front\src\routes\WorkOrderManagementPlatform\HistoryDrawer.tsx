import React, { useMemo } from 'react';
import { Table } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';

export default ({ ds }) => {
  const columns: ColumnProps[] = useMemo(
    () => [
      // {
      //   name: 'eventId',
      //   align: ColumnAlign.left,
      //   width: 150,
      // },
      // {
      //   name: 'eventTypeCode',
      //   width: 150,
      // },
      // {
      //   name: 'eventTypeDesc',
      //   width: 150,
      // },
      // {
      //   name: 'eventRequestId',
      //   width: 150,
      // },
      // {
      //   name: 'requestTypeCode',
      //   width: 150,
      // },
      // {
      //   name: 'requestTypeDesc',
      //   width: 150,
      // },
      {
        name: 'eventUserName',
        width: 150,
      },
      {
        name: 'eventTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'workOrderNum',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'siteName',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'workOrderTypeDesc',
        align: ColumnAlign.left,
        width: 150,
      },
      {
        name: 'statusDesc',
        width: 120,
      },
      {
        name: 'lastWoStatusDesc',
        width: 150,
      },
      {
        name: 'prodLineCode',
        width: 150,
      },
      {
        name: 'prodLineName',
        width: 150,
      },
      {
        name: 'materialCode',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'materialName',
        width: 120,
      },
      {
        name: 'qty',
        width: 120,
      },
      {
        name: 'uomName',
        width: 120,
      },
      {
        name: 'priority',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'planEndTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'planStartTime',
        align: ColumnAlign.right,
        width: 150,
      },
      {
        name: 'locatorCode',
        width: 150,
      },
      {
        name: 'locatorName',
        width: 150,
      },
      {
        name: 'bomName',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'routerName',
        width: 120,
      },
      {
        name: 'completeControlQty',
        width: 120,
      },
      {
        name: 'completeControlTypeDesc',
        width: 120,
      },
    ],
    [],
  );

  return (
    <>
      <Table customizedCode="workshopHistory" dataSet={ds} columns={columns} />
    </>
  );
};
