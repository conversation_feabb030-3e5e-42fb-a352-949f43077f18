/* eslint-disable no-unused-vars */
import React, { useEffect, useState, useMemo, useRef } from 'react';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import { isEmpty } from 'lodash';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import request from 'utils/request';
import { observer } from 'mobx-react';
import moment from 'moment';
import {
  DataSet,
  Form,
  Lov,
  Button,
  TextField,
  Modal,
  TextArea,
  Table,
  Select,
  DatePicker,
} from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import { Header, Content } from 'components/Page';
import { useRequest } from '@components/tarzan-hooks';
import { TarzanSpin } from '@components/tarzan-ui';
import html2Canvas from 'html2canvas';
import JsPDF from 'jspdf';
import cs from 'classnames';
import { queryMapIdpValue } from 'services/api';
import { BASIC } from '@utils/config';
import {
  enterModalDS,
  detailDS,
  remarkFormDS,
  materialDS,
  materialDetailDS,
  addMaterialDetailDS,
  lotDS,
  stoveDS,
  submitDS,
} from '../stores';
import { QueryTemplate, Save, LineSave, PointGenerate, QueryDetail, Submit, Abandon, CrucibleRelease } from '../services';
import FurnaceDiagram from '../FurnaceDiagram';
import styles from '../index.module.less';

const API = `${BASIC.HMES_BASIC}`;

const modelPrompt = 'tarzan.hmes.FurnaceLoadingDiagramPlatform';
const { Option } = Select;

let enterModal;
let materialModal = null;
let lotModal = null;
let selectBgColorModal = null;
let selectFontColorModal = null;
let stoveModal = null;
let submitModal = null;

// 删除物料数据
const deleteStoveDataUrl = `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/lot/delete/ui`;

const tableHeader = [
  intl.get(`${modelPrompt}.furnaceLoadingEnterprise`).d('装炉企业'),
  intl.get(`${modelPrompt}.equipmentCode`).d('炉号'),
  intl.get(`${modelPrompt}.stoveCount`).d('炉使用次数'),
  intl.get(`${modelPrompt}.templateName`).d('装炉图模版'),
  intl.get(`${modelPrompt}.chargingDateStr`).d('装炉日期'),
  intl.get(`${modelPrompt}.sumNumber`).d('装炉坩锅数'),
  intl.get(`${modelPrompt}.sumWeight`).d('装炉重量'),
  intl.get(`${modelPrompt}.chargingLayer`).d('装炉层数'),
  intl.get(`${modelPrompt}.chargingMethod`).d('装炉方式'),
  intl.get(`${modelPrompt}.dischargingDateStr`).d('出炉日期'),
  intl.get(`${modelPrompt}.userName`).d('制表'),
];
// 装炉平台
const FurnaceLoadingDiagramPlatform = observer(props => {
  const furnaceDiagramRef = useRef([]);

  const pdfWrapRef = useRef(null);

  const [printLoading, setPrintLoading] = useState(false);

  const enterModalDs = useMemo(() => {
    return new DataSet(enterModalDS());
  }, []);
  const detailDs = useMemo(() => {
    return new DataSet(detailDS());
  }, []);
  const remarkFormDs = useMemo(() => {
    return new DataSet(remarkFormDS());
  }, []);
  const materialDs = useMemo(() => {
    return new DataSet(materialDS());
  }, []);
  const materialDetailDs = useMemo(() => {
    return new DataSet(materialDetailDS());
  }, []);

  const addMaterialDetailDs = useMemo(() => {
    return new DataSet(addMaterialDetailDS());
  }, []);

  const lotDs = useMemo(() => {
    return new DataSet(lotDS());
  }, []);
  const stoveDs = useMemo(() => {
    return new DataSet(stoveDS());
  }, []);
  const submitDs = useMemo(() => {
    return new DataSet(submitDS());
  }, []);

  // 预设背景颜色
  const [preBg, setPreBg] = useState();
  // 预设字体颜色
  const [preFontColor, setPreFontColor] = useState();
  const {
    match: {
      params: { id },
    },
  } = props;

  const { run: abandon, loading: abandonLoading } = useRequest(Abandon(), {
    manual: true,
    needPromise: true,
  });
  const { run: submit, loading: submitlLoading } = useRequest(Submit(), {
    manual: true,
    needPromise: true,
  });
  const { run: queryDetail, loading: queryDetailLoading } = useRequest(QueryDetail(), {
    manual: true,
    needPromise: true,
  });
  const { run: pointGenerate, loading: pointGenerateLoading } = useRequest(PointGenerate(), {
    manual: true,
    needPromise: true,
  });
  const { run: save, loading: saveLoading } = useRequest(Save(), {
    manual: true,
    needPromise: true,
  });
  const { run: crucibleRelease, loading: crucibleReleaseLoading } = useRequest(CrucibleRelease(), {
    manual: true,
    needPromise: true,
  });

  const { run: lineSave, loading: lineSaveLoading } = useRequest(LineSave(), {
    manual: true,
    needPromise: true,
  });

  const { run: queryTemplate, loading: queryTemplateLoading } = useRequest(QueryTemplate(), {
    manual: true,
    needPromise: true,
  });
  const { data: lovData } = useRequest({ lovCode: 'HME.STOVE_LOADING_METHOD' });
  const [templateList, setTemplateList] = useState([]);
  const [currentLotOccupyList, setCurrentLotOccupyList] = useState([]);

  const [chargingDate, setChargingDate] = useState('');
  const [dischargingDate, setDisChargingDate] = useState('');

  const [clientWidth, setClientWidth] = useState(1000);
  const [tableHeaderValue, setTableHeaderValue] = useState();

  const [layLevelMeaningList, setLayLevelMeaningList] = useState([]);
  const [materialBtnCanEdit, setMaterialBtnCanEdit] = useState(false); // 选择物料按钮是否可以使用
  const [currentMaterialBgColor, setCurrentMaterialBgColor] = useState('');
  const [currentLotFontColor, setCurrentLotFontColor] = useState('');
  const [currentMaterialCodeObj, setCurrentMaterialCodeObj] = useState({});
  const [currentLotCodeObj, setCurrentLotCodeObj] = useState({});
  const [maxCol, setMaxCol] = useState(40);
  const [bgColorMap] = useState(new Map());
  const [fontColorMap] = useState(new Map());

  useEffect(() => {
    if (!id) return;
    handleQueryValue();
    if (id === 'create') {
      handleOpen();
    } else {
      handleQueryDetail();
    }
  }, [id]);

  useEffect(() => {
    if (templateList?.length === 0) return;
    const arr = [];
    templateList.forEach(item => {
      arr.push(item[0].length);
    });
    const [max] = [...arr].sort((a, b) => b - a);
    setMaxCol(max);
    // 全部打平-》获取物料和颜色的map
    bgColorMap.clear();
    fontColorMap.clear();
    handleMapColor();
    setTimeout(() => {
      setClientWidth(max * 28);
      // setClientWidth(document.getElementById('tableContent').clientWidth)
    }, 100);
  }, [templateList]);

  // 背景颜色和字体颜色值集
  const handleQueryValue = async () => {
    const res = await queryMapIdpValue({
      tenantId: getCurrentOrganizationId(),
      fontColor: 'HME.FEED_FONTCOLOR',
      bgColor: 'HME.FEED_BACKGROUNDCOLOR',
    });
    setPreBg(res?.bgColor?.map(item => item.value) || []);
    setPreFontColor(res?.fontColor?.map(item => item.value) || []);
  };

  const handleMapColor = () => {
    templateList
      .flat()
      .flat()
      .forEach(item => {
        if (item.materialId) {
          setBgColorMap(`${item.materialId}_${item.productionBatch}`, item.backgroundColor);
        }
        if (item.stoveLot) {
          setFontColorMap(item.stoveLot, item.fontColor);
        }
      });
    canSelectBgColor();
    canSelectFontColor();
  };
  const setBgColorMap = (key, value) => {
    bgColorMap.set(key, value);
  };
  const setFontColorMap = (key, value) => {
    fontColorMap.set(key, value);
  };
  const canSelectBgColor = () => {
    const arr = JSON.parse(JSON.stringify(preBg));
    const newPreBg = [];
    for (const val of bgColorMap.values()) {
      newPreBg.push(val);
    }
    const diff = arr.filter(function (val) {
      return newPreBg.indexOf(val) === -1;
    });
    // setPreBg(diff)
  };
  const canSelectFontColor = () => {
    const arr = JSON.parse(JSON.stringify(preFontColor));
    const newPreBg = [];
    for (const val of fontColorMap.values()) {
      newPreBg.push(val);
    }
    const diff = arr.filter(function (val) {
      return newPreBg.indexOf(val) === -1;
    });
    setPreFontColor(diff);
  };
  const handleQueryDetail = async () => {
    const res = await queryDetail({
      params: {
        stoveHeadId: id,
      },
    });
    if (res && res?.detailList) {
      const {
        detailList,
        layLevelMeaningList,
        lineUpdateList,
        selectMaterialEnableFlag,
        ...others
      } = res;
      lineUpdateList.forEach(item => {
        item.aveNum = Number(item.stoveNumber) ? (item.weight / item.stoveNumber).toFixed(2) : null;
      });
      detailDs.loadData(lineUpdateList);
      setLayLevelMeaningList(layLevelMeaningList || []);
      setMaterialBtnCanEdit(selectMaterialEnableFlag === 'Y');
      remarkFormDs.current?.set('remark', others?.remark);
      setTemplateList(handleRowNumber(detailList || [], '+'));

      others.chargingDate = others.chargingDate ? moment(others.chargingDate).format('YYYY-MM-DD HH:mm:ss') : undefined;
      others.dischargingDate = others.dischargingDate ? moment(others.dischargingDate).format('YYYY-MM-DD HH:mm:ss') : undefined;
      setChargingDate(others.chargingDate ? moment(others.chargingDate) : null)
      setDisChargingDate(others.dischargingDate ? moment(others.dischargingDate) : null)
      setTableHeaderValue(others);
    }
  };

  // 处理模版数据中的row 后端需要0开始， 前端1开始
  const handleRowNumber = (data, type) => {
    const detailList = data;
    if (type === '+') {
      detailList.forEach(item => {
        item.forEach((x, index) => {
          x.forEach((y, i) => {
            y.row = index + 1;
            y.col = i + 1;
          });
        });
      });
    } else if (type === '-') {
      detailList.forEach(item => {
        item.forEach((x, index) => {
          x.forEach((y, i) => {
            y.row = index;
            y.col = i;
          });
        });
      });
    }
    return detailList;
  };

  const handleOpen = () => {
    enterModal = Modal.open({
      title: intl.get(`${modelPrompt}.equipmentCode.enter`).d('炉位登陆'),
      destroyOnClose: true,
      closable: false,
      children: (
        <Form
          className="enterModalForm"
          dataSet={enterModalDs}
          labelLayout="placeholder"
          labelWidth={80}
        >
          <TextField name="equipmentCode" style={{ width: '80%' }} />
          <Lov name="templeLov" style={{ width: '80%' }} />
        </Form>
      ),
      okButton: true,
      cancelButton: true,
      onCancel: handleCancel,
      onOk: async () => {
        if (await enterModalDs.validate()) {
          const { stoveTemplateHeadId, equipmentCode } = enterModalDs.toJSONData()[0];
          const res = await queryTemplate({
            params: { stoveTemplateHeadId, equipmentCode },
          });
          enterModalDs.reset();
          if (res && res?.detailList) {
            if (res.stoveHeadId) {
              props.history.push(
                `/hmes/furnace-loading-diagram-platform/detail/${res.stoveHeadId}`,
              );
              return true;
            }
            const {
              detailList,
              layLevelMeaningList,
              lineUpdateList,
              selectMaterialEnableFlag,
              ...others
            } = res;
            detailDs.loadData(lineUpdateList || []);
            setLayLevelMeaningList(layLevelMeaningList || []);
            remarkFormDs.current?.set('remark', '');
            setTemplateList(handleRowNumber(detailList || [], '+'));

            others.chargingDate = others.chargingDate ? moment(others.chargingDate).format('YYYY-MM-DD HH:mm:ss') : null;
            others.dischargingDate = others.dischargingDate ? moment(others.dischargingDate).format('YYYY-MM-DD HH:mm:ss') : null;
            setChargingDate(others.chargingDate ? moment(others.chargingDate) : '')
            setDisChargingDate(others.dischargingDate ? moment(others.dischargingDate) : '')
            setTableHeaderValue(others);
            setMaterialBtnCanEdit(selectMaterialEnableFlag === 'Y');
            return true;
          }
          return false;
        }
        return false;
      },
    });
  };
  const handleCancel = () => {
    enterModal.close();
    props.history.push(`/hmes/furnace-loading-diagram-platform/list`);
  };
  const renderHeaderTR = list => {
    return (
      <tr>
        {list.map(item => (
          <td style={item === intl.get(`${modelPrompt}.userName`).d('制表') ? { borderTop: '1px solid black' } : {}}>{item}</td>
        ))}
      </tr>
    );
  };
  const handleChangeMethod = val => {
    setTableHeaderValue({
      ...tableHeaderValue,
      chargingMethod: val,
    });
  };

  const handleChange = (value) => {
    setTableHeaderValue({
      ...tableHeaderValue,
      chargingDate: moment(value).format('YYYY-MM-DD HH:mm:ss'),
    });
    setChargingDate(value)
  }
  const handleDisChargingChange = (value) => {
    setTableHeaderValue({
      ...tableHeaderValue,
      dischargingDate: moment(value).format('YYYY-MM-DD HH:mm:ss'),
    });
    setDisChargingDate(value)
  }
  const renderHeaderTRValue = item => {
    return (
      <tr>
        <td>{item?.enterpriseShortName}</td>
        <td>{item?.equipmentCode}</td>
        <td>{item?.stoveCount}</td>
        <td>{item?.templateName}</td>
        <td>
          {
            ['COMPLETED', 'RELEASED', 'DISUSED'].includes(tableHeaderValue?.status) ?
              item?.chargingDateStr :
              <DatePicker required onChange={handleChange} value={chargingDate} />
          }
        </td>
        <td>{item?.sumNumber}</td>
        <td>{item?.sumWeight}</td>
        <td>{item?.chargingLayer}</td>
        <td>

          {
            tableHeaderValue?.status ?
              item?.chargingMethodDesc :
              <Select
                required
                value={item?.chargingMethod}
                onChange={handleChangeMethod}
              >
                {(lovData || []).map(item => (
                  <Option value={item.value} key={item.value}>
                    {item.meaning}
                  </Option>
                ))}
              </Select>
          }

        </td>
        <td>
          {
            ['COMPLETED', 'RELEASED', 'DISUSED'].includes(tableHeaderValue?.status) ?
              item?.dischargingDateStr :
              <DatePicker required onChange={handleDisChargingChange} value={dischargingDate} />
          }
        </td>
        <td>{item?.userName}</td>
      </tr>
    );
  };

  const materialColumns = [
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
      width: 200,
    },
    {
      name: 'productionBatch',
      width: 200,
    },
    {
      name: 'uomCode',
      width: 100,
    },
    {
      name: 'weight',
      width: 100,
    },
    {
      name: 'qty',
      editor: true,
      width: 100,
    },
    {
      header: intl.get('hzero.common.table.column.option').d('操作'),
      lock: 'right',
      width: 120,
      renderer: ({ record }) => [
        <a onClick={() => handleMaterialDetail(record, 'detail')}>
          {intl.get(`${modelPrompt}.detail`).d('明细数据')}
        </a>,
      ],
    },
  ];

  const materialDetailColumns = [
    {
      name: 'materialLotCode',
      width: 380,
    },
    {
      name: 'uomCode',
      width: 100,
    },
    {
      name: 'weight',
      width: 100,
    },
    {
      name: 'qty',
      editor: true,
    },
  ];

  const lotColumns = [
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'lotCode',
    },
    {
      name: 'totalQty',
    },
    {
      name: 'qty',
    },
    {
      name: 'frequency',
    },
    {
      name: 'creationDate',
      width: 180,
    },
  ];

  /**
   * 返回表格操作按钮组
   * @returns {*[]}
   */
  const buttons = (record, from) => {
    return from === 'line'
      ? [
        <Button icon="playlist_add" onClick={() => handleLineAdd(record)} key="add">
          新增
        </Button>,
        <Button icon="delete" onClick={() => handleLineDelete()} key="delete">
          删除
        </Button>,
      ]
      : [];
  };

  const handleLineAdd = async (record) => {
    // 获取新的全部的物料明细数据
    addMaterialDetailDs.setQueryParameter('locatorId', record?.data?.locatorId);
    addMaterialDetailDs.setQueryParameter('materialId', record?.data?.materialId);
    addMaterialDetailDs.setQueryParameter('productionBatch', record?.data?.productionBatch);

    await addMaterialDetailDs.query();

    materialModal = Modal.open({
      title: intl.get(`${modelPrompt}.please.materialDetail`).d('全部物料明细数据'),
      destroyOnClose: true,
      style: {
        width: '1000px',
      },
      children: (
        <>
          <Form dataSet={detailDs} columns={3}>
            <TextField name="materialCode" disabled />
            <TextField name="materialName" disabled />
            <TextField name="productionBatch" disabled />
          </Form>
          <Table
            dataSet={addMaterialDetailDs}
            columns={materialDetailColumns}
          />
        </>
      ),
      onOk: () => handlelAddMaterialDetail(),
    });
  };

  const handlelAddMaterialDetail = () => {
    if (addMaterialDetailDs.selected.length === 0) {
      notification.warning({
        message: intl
          .get(`${modelPrompt}.materialDetail.select`)
          .d('请先新增的物料批数据！'),
      });
      return false;
    }
    const materialDetailDsData = materialDetailDs?.toData();
    const newMaterialDetailDsData = materialDetailDsData.concat(addMaterialDetailDs.selected)
    materialDetailDs.loadData(newMaterialDetailDsData);
  }

  const handleLineDelete = async () => {
    if (materialDetailDs?.selected?.length === 0) {
      notification.warning({
        message: intl
          .get(`${modelPrompt}.materialDetail.select`)
          .d('请先选择明细数据！'),
      });
      return false;
    }
    const stoveLotIds = materialDetailDs?.selected?.map(item => item?.data?.stoveLotId);
    Modal.confirm({
      title: intl.get(`${modelPrompt}.title.modal.deleteMaterialDetail`).d('确定删除吗？'),
      onOk: () => {
        request(deleteStoveDataUrl, {
          method: 'POST',
          body: stoveLotIds,
        }).then(res => {
          if (res && !res.failed) {
            notification.success({
              message: intl.get(`${modelPrompt}.message.success`).d('操作成功！'),
            });
            materialDetailDs.query();
          } else {
            notification.error({
              message: res.message,
            });
          }
        });
      },
    });
  }

  const handleMaterialDetail = async (record, from) => {
    // 获取新的物料明细数据
    materialDetailDs.queryParameter = {};
    materialDetailDs.loadData([]);
    let formDs = '';
    if (from === 'detail') {
      const newMaterialLineList = handleMaterialData();
      materialDetailDs.setQueryParameter('locatorId', record?.data?.locatorId);
      materialDetailDs.setQueryParameter('materialId', record?.data?.materialId);
      materialDetailDs.setQueryParameter('productionBatch', record?.data?.productionBatch);
      materialDetailDs.setQueryParameter('materialLineList', newMaterialLineList);
      await materialDetailDs.query();
      formDs = materialDs;
    }
    if (from === 'line') {
      materialDetailDs.setQueryParameter('stoveLineId', record?.data?.stoveLineId);
      materialDetailDs.query();
      formDs = detailDs;
    }

    materialModal = Modal.open({
      title: intl.get(`${modelPrompt}.please.materialDetail`).d('物料明细数据'),
      destroyOnClose: true,
      style: {
        width: '1000px',
      },
      children: (
        <>
          <Form dataSet={formDs} columns={4}>
            <TextField name="materialCode" disabled />
            <TextField name="materialName" disabled />
            <TextField name="productionBatch" disabled />
            {from === 'line' ? <TextField name="weight" disabled /> : <TextField name="qty" disabled />}
          </Form>
          <Table
            dataSet={materialDetailDs}
            columns={materialDetailColumns}
            buttons={buttons(record, from)}
            summaryBar={[
              ({ dataSet }) => {
                const sumQty = !dataSet.selected.length
                  ? 0
                  : dataSet.selected?.map((ele) => ele?.get('qty') || 0)?.reduce((pre, cur) => Number(pre) + Number(cur));
                return { label: '当前投入量', value: sumQty };
              },
            ]}
          />
        </>
      ),
      onOk: () => handleConfirmMaterialDetail(record, from),
      afterClose: () => materialDetailDs.batchUnSelect(materialDetailDs.records),
    });
  };
  const handleConfirmMaterialDetail = async (record, from) => {
    // 选择碳化料物料明细数据
    if (from === 'detail') {
      // 校验明细投入量是否等于批次投入量
      if (materialDetailDs.selected.length === 0) {
        notification.warning({
          message: intl
            .get(`${modelPrompt}.materialDetail.select`)
            .d('请先选择明细数据！'),
        });
        return false;
      }
      let total = 0;
      materialDetailDs.selected.forEach(item => total += Number(item?.data?.qty));
      const _qty = record.get('qty'); // 物料的现有量
      if (Number(total) !== Number(_qty)) {
        // 若勾选的明细投入量汇总不等于物料的投入量，则报错
        notification.error({
          message: intl
            .get(`${modelPrompt}.qty.required`)
            .d('选中的明细投入量汇总与行数量不一致，请检查！'),
        });
        return false;
      }
      record.data.newLotList = materialDetailDs.selected.map(item => item.toData());
    }
    // 选择行明细
    if (from === 'line') {
      if (!(await materialDetailDs.validate())) return false;
      Modal.confirm({
        title: intl.get(`${modelPrompt}.title.modal.saveMaterialDetail`).d('确定保存吗？'),
        onOk: async () => {
          if (materialDetailDs.toJSONData()) {
            record.data.lotList = materialDetailDs.toData();
            const params = record.toData();
            const res = await lineSave({ params });
            if (res && res.success) {
              notification.success({
                message: intl.get(`${modelPrompt}.message.success`).d('操作成功！'),
              });
              handleQueryDetail();
              return true;
            }
            return false;
          }
        },
      });
    }
  };

  const handleMaterial = () => {
    if (!handleValidateSelect()) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.select`).d('请先选择数据'),
      });
      return;
    }
    materialDs.queryDataSet.reset();
    materialDs.loadData([]);
    setCurrentMaterialCodeObj({});
    //
    const newMaterialLineList = handleMaterialData();
    materialDs.setQueryParameter('materialLineList', newMaterialLineList);

    materialModal = Modal.open({
      title: intl.get(`${modelPrompt}.please.selectMaterial`).d('选择物料'),
      destroyOnClose: true,
      style: {
        width: '1000px',
      },
      children: (
        <>
          <Table dataSet={materialDs} columns={materialColumns} />
        </>
      ),
      onOk: () => openBgColorModal(),
    });
  };
  const openBgColorModal = async () => {
    const arr = materialDs.toData().filter(item => item.qty);
    if (!(await materialDs.validate())) return false;
    if (arr.length === 0) return false;
    if (
      arr.length &&
      arr.some(
        item =>
          `${item.materialId}_${item.productionBatch}` !==
          `${arr[0].materialId}_${arr[0].productionBatch}`,
      )
    ) {
      // 校验物料是否一致
      notification.error({
        message: intl.get(`${modelPrompt}.materia.inconsistency`).d('物料+工艺批次不一致！'),
      });
      return false;
    }

    if (arr.some(item => item.newLotList === undefined)) {
      notification.error({
        message: intl
          .get(`${modelPrompt}.qty.required`)
          .d('请先勾选物料明细数据！'),
      });
      return false;
    }
    // 校验物料批数量和物料投入量是否一致
    const flag = arr.filter((e) => {
      let total = 0;
      e?.newLotList?.forEach(item => total += Number(item?.qty));
      const _qty = e.qty // 物料的现有量
      return Number(total) !== Number(_qty);
    });
    if (flag?.length) {
      // 若勾选的明细投入量汇总不等于物料的投入量，则报错
      notification.error({
        message: intl
          .get(`${modelPrompt}.qty.required`)
          .d('物料明细投入量汇总与物料投入量不一致，请检查！'),
      });
      return false;
    }
    arr[0].uuid = uuid();
    setCurrentMaterialCodeObj(arr[0]);
    // 判断是否存在物料 存在则在map中找到背景色
    let existColor = '';
    const keys = bgColorMap.keys();
    for (const key of keys) {
      if (key === `${arr[0].materialId}_${arr[0].productionBatch}`) {
        existColor = bgColorMap.get(key);
      }
    }
    // 存在物料 则自动带出背景颜色
    if (existColor) {
      setCurrentMaterialBgColor(existColor);
      handleConfirmMaterial(arr[0], existColor);
    } else {
      selectBgColorModal = Modal.open({
        title: intl.get(`${modelPrompt}.please.selectBackolor`).d('选择背景色'),
        destroyOnClose: true,
        style: {
          width: '480px',
        },
        children: (
          <>
            {preBg.map(item => (
              <div
                onClick={() => handleBgColorClick(item)}
                style={{
                  transform: currentMaterialBgColor === item ? 'scale(1.5)' : 'scale(1.0)',
                  background: item,
                  display: 'inline-block',
                  width: 30,
                  height: 30,
                }}
              ></div>
            ))}
          </>
        ),
        onOk: () => handleConfirmMaterial(),
      });
    }
  };
  useEffect(() => {
    if (currentMaterialBgColor) {
      selectBgColorModal?.update({
        children: (
          <>
            {preBg.map(i => (
              <div
                onClick={() => handleBgColorClick(i)}
                style={{
                  transform: currentMaterialBgColor === i ? 'scale(1.3)' : 'scale(1.0)',
                  background: i,
                  display: 'inline-block',
                  width: 30,
                  height: 30,
                }}
              ></div>
            ))}
          </>
        ),
        onOk: () => handleConfirmMaterial(),
      });
    }
  }, [currentMaterialBgColor]);
  const handleConfirmMaterial = (materialCodeObj, existColor) => {
    // if (!currentMaterialBgColor && !existColor) return false
    setBgColorMap(
      `${materialCodeObj?.materialId ||
      currentMaterialCodeObj?.materialId}_${materialCodeObj?.productionBatch ||
      currentMaterialCodeObj?.productionBatch}`,
      existColor || currentMaterialBgColor,
    );

    const newArray = JSON.parse(JSON.stringify(templateList));
    newArray.forEach(x => {
      x.forEach(y => {
        y.forEach(z => {
          if (z.selectable) {
            z.backgroundColor = z.selectable
              ? existColor || currentMaterialBgColor
              : z.backgroundColor;
            z.materialId = materialCodeObj?.materialId || currentMaterialCodeObj?.materialId;
            z.materialCode = materialCodeObj?.materialCode || currentMaterialCodeObj?.materialCode;
            z.productionBatch =
              materialCodeObj?.productionBatch || currentMaterialCodeObj?.productionBatch;
            z.locatorId = materialCodeObj?.locatorId || currentMaterialCodeObj?.locatorId;
            z.qty = materialCodeObj?.qty || currentMaterialCodeObj?.qty;
            z.uuid = materialCodeObj?.uuid || currentMaterialCodeObj?.uuid;
            z.selectable = false;
            z.lotList = materialCodeObj?.newLotList || currentMaterialCodeObj?.newLotList;
          }
        });
      });
    });
    setTemplateList(newArray);
    setCurrentMaterialBgColor('');
  };
  const handleBgColorClick = item => {
    setCurrentMaterialBgColor(item);
  };
  const handleLot = () => {
    const arr = JSON.parse(JSON.stringify(templateList));
    if (
      arr
        .flat()
        .flat()
        .every(item => !item.serialNumber)
    ) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.reservation`).d('请先生成点位'),
      });
      return;
    }
    if (!handleValidateSelect()) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.select`).d('请先选择数据'),
      });
      return;
    }
    lotDs.queryDataSet.reset();
    lotDs.loadData([]);
    lotDs.setQueryParameter('occupyList', currentLotOccupyList);
    lotDs.setQueryParameter('stoveHeadId', id);

    lotModal = Modal.open({
      title: intl.get(`${modelPrompt}.title.crucibleLot`).d('坩锅批次'),
      destroyOnClose: true,
      style: {
        width: '1000px',
      },
      children: (
        <>
          <Table dataSet={lotDs} columns={lotColumns} />
        </>
      ),
      onOk: () => openFontColorModal(),
    });
  };
  const openFontColorModal = () => {
    if (lotDs.selected.length === 0) return false;
    const _qty = lotDs.selected[0].get('qty'); // 坩锅的现有量
    const selectedPoints = templateList
      .flat()
      .flat()
      .filter(item => item.selectable && item.serialNumber);
    if (selectedPoints.length > _qty) {
      // 若现有量小于选择点位的数量，则报错
      notification.error({
        message: intl
          .get(`${modelPrompt}.lotQty.required`)
          .d('选中坩锅批次现有量应大于选中的点位数量，请检查！'),
      });
      return false;
    }
    const lot = lotDs.selected[0].data;
    setCurrentLotCodeObj(lot);
    // 判断是否存在物料 存在则在map中找到背景色
    let existColor = '';
    const keys = fontColorMap.keys();
    for (const key of keys) {
      if (key === lot.lotCode) {
        existColor = fontColorMap.get(key);
      }
    }
    // 存在批次 则自动带出背景颜色
    if (existColor) {
      setCurrentLotFontColor(existColor);
      handleConfirmLot(lot, existColor);
      existColor = '';
    } else {
      selectFontColorModal = Modal.open({
        title: intl.get(`${modelPrompt}.please.selectFontColor`).d('选择字体颜色'),
        destroyOnClose: true,
        style: {
          width: '480px',
        },
        children: (
          <>
            {preFontColor.map(item => (
              <div
                onClick={() => handleFontColorClick(item)}
                style={{
                  transform: currentMaterialBgColor === item ? 'scale(1.5)' : 'scale(1.0)',
                  background: item,
                  display: 'inline-block',
                  width: 30,
                  height: 30,
                }}
              ></div>
            ))}
          </>
        ),
        onOk: () => handleConfirmLot(),
      });
    }
  };
  useEffect(() => {
    if (currentLotFontColor) {
      selectFontColorModal?.update({
        children: (
          <>
            {preFontColor.map(i => (
              <div
                onClick={() => handleFontColorClick(i)}
                style={{
                  transform: currentLotFontColor === i ? 'scale(1.3)' : 'scale(1.0)',
                  background: i,
                  display: 'inline-block',
                  width: 30,
                  height: 30,
                }}
              ></div>
            ))}
          </>
        ),
        onOk: () => handleConfirmLot(),
      });
    }
  }, [currentLotFontColor]);
  const handleConfirmLot = (lotObj, existColor) => {
    if (isEmpty(currentLotCodeObj)) return false;
    setFontColorMap(
      lotObj?.lotCode || currentLotCodeObj?.lotCode,
      existColor || currentLotFontColor,
    );
    // 缓存点位和勾选的数量
    const arr = currentLotOccupyList;
    let tempQty = 0;
    templateList.forEach(item => {
      item.forEach(x => {
        x.forEach(y => {
          if (y.selectable) {
            tempQty += 1;
          }
        });
      });
    });
    if (tempQty !== 0) {
      arr.push(
        {
          stoveLot: lotObj?.lotCode || currentLotCodeObj?.lotCode,
          qty: tempQty,
        },
      );
    }
    setCurrentLotOccupyList(arr);
    const newArray = JSON.parse(JSON.stringify(templateList));
    newArray.forEach(x => {
      x.forEach(y => {
        y.forEach(z => {
          z.fontColor = z.selectable ? existColor || currentLotFontColor : z.fontColor;
          z.stoveLot = z.selectable ? lotObj?.lotCode || currentLotCodeObj?.lotCode : z.stoveLot;
          z.selectable = false;
        });
      });
    });
    setTemplateList(newArray);
    setCurrentLotFontColor('');
  };
  const handleFontColorClick = item => {
    setCurrentLotFontColor(item);
  };

  // 是否有数据选中
  const handleValidateSelect = () => {
    const arr = [];
    templateList.forEach(item => {
      item.forEach(x => {
        x.forEach(y => {
          if (y.selectable) {
            arr.push(y);
          }
        });
      });
    });
    return arr.length;
  };
  const handleClean = () => {
    if (!handleValidateSelect()) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.select`).d('请先选择数据'),
      });
      return;
    }
    const arr = JSON.parse(JSON.stringify(templateList));
    arr.forEach(item => {
      item.forEach(x => {
        x.forEach(y => {
          if (y.selectable) {
            // y.stoveLot = null
            // y.materialId = null
            // y.locatorId = null
            // y.fontColor =null
            // y.backgroundColor = null
            // y.serialNumber = null
            y.cancelFlag = 'Y';
          }
        });
      });
    });

    Modal.confirm({
      title: intl.get(`${modelPrompt}.prompt`).d('提示'),
      children: (
        <div>
          <p>{intl.get(`${modelPrompt}.reset.confirm`).d('是否确认重置？')}</p>
        </div>
      ),
    }).then(async button => {
      if (button === 'ok') {
        const detailList = handleRowNumber(arr, '-');
        const params = {
          ...tableHeaderValue,
          detailList,
          lineUpdateList: detailDs.toData(),
        };
        const res = await save({
          params,
        });
        if (res && res.success) {
          handleQueryDetail();
        }
      }
    });
  };
  const templateListChange = (newArray, layer) => {
    const arr = templateList;
    arr[layer] = newArray;
    setTemplateList(arr);
  };
  const stoveColumns = [
    {
      name: 'stoveLot',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
      width: 120,
    },
    { name: 'stoveNumber', align: 'left' },
  ];
  const handleStove = async record => {
    stoveDs.loadData([]);
    stoveDs.setQueryParameter('stoveHeadId', record.data?.stoveHeadId);
    stoveDs.setQueryParameter('materialId', record.data?.materialId);
    stoveDs.setQueryParameter('productionBatch', record.data?.productionBatch);
    await stoveDs.query();
    stoveModal = Modal.open({
      title: intl.get(`${modelPrompt}.select.quantity.information`).d('坩埚批次及数量信息'),
      destroyOnClose: true,
      maskClosable: true,
      style: {
        width: '580px',
      },
      children: (
        <>
          <Table dataSet={stoveDs} columns={stoveColumns} />
        </>
      ),
      okButton: false,
    });
  };
  const columns = [
    {
      name: 'sequence',
      width: 60,
      renderer: ({ record }) => {
        return `${record.index + 1}`;
      },
    },
    {
      name: 'backgroundColor',
      width: 90,
      renderer: ({ record }) => {
        return (
          <div style={{ width: 20, height: 20, background: record?.get('backgroundColor') }} />
        );
      },
    },
    {
      name: 'materialCode',
      width: 170,
      renderer: ({ text }) => printLoading ? <div style={{
        width: "100%",
        "word-break": "break-all",
        "white-space": "break-spaces",
      }}>{text}</div> : text,
    },
    {
      name: 'materialName',
      width: 160,
      renderer: ({ text }) => printLoading ? <div style={{
        width: "100%",
        "word-break": "break-all",
        "white-space": "break-spaces",
      }}>{text}</div> : text,
    },
    {
      name: 'productionBatch',
      width: 250,
    },
    {
      name: 'remark',
      editor: tableHeaderValue?.status !== 'COMPLETED',
    },
    {
      name: 'stoveNumber',
      width: 80,
      renderer: ({ record }) => {
        return <a onClick={() => handleStove(record)}>{record?.get('stoveNumber')}</a>;
      },
    },
    {
      name: 'weight',
      editor: tableHeaderValue?.status !== 'COMPLETED',
      width: 80,
    },
    {
      name: 'aveNum',
      width: 90,
    },
    {
      name: 'carbonization',
      editor: tableHeaderValue?.status !== 'COMPLETED',
      width: 90,
      // width:80,
    },
    {
      header: intl.get('hzero.common.table.column.option').d('操作'),
      lock: 'right',
      width: 120,
      renderer: ({ record }) => [
        <a onClick={() => handleMaterialDetail(record, 'line')}>
          {intl.get(`${modelPrompt}.detail`).d('明细数据')}
        </a>,
      ],
    },
  ];
  // 点位生成
  const handlePoint = async () => {
    // 判断是否选择物料
    let flag = false;
    const arr = JSON.parse(JSON.stringify(templateList));
    try {
      arr
        .flat()
        .flat()
        .forEach(item => {
          if (item.backgroundColor && !item.selectable) {
            flag = true;
            throw Error();
          }
        });
      // eslint-disable-next-line no-empty
    } catch (e) { }

    if (!flag) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.select`).d('请先选择物料'),
      });
      return false;
    }
    if (!tableHeaderValue.stoveHeadId) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.saveMaterial`).d('请先选择保存物料'),
      });
      return false;
    }
    let isSave = false;
    try {
      arr
        .flat()
        .flat()
        .forEach(item => {
          // uuid 表示未保存的数据
          if (item.uuid) {
            isSave = true;
            throw Error();
          }
        });
      // eslint-disable-next-line no-empty
    } catch (e) { }
    if (isSave) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.saveData`).d('请先保存数据！'),
      });
      return false;
    }
    const detailList = handleRowNumber(JSON.parse(JSON.stringify(templateList)), '-');
    const res = await pointGenerate({
      params: detailList,
    });
    if (res && res.success) {
      notification.success({
        message: intl.get(`${modelPrompt}.message.success`).d('操作成功！'),
      });
      handleQueryDetail();
    }
  };
  const handleMaterialData = () => {
    const materialLineList = [];
    templateList.forEach(item => {
      item.forEach(row => {
        row.forEach(col => {
          // 有uuid的都是新编辑的数据
          if (col.uuid) {
            materialLineList.push({
              materialId: col.materialId,
              materialCode: col.materialCode,
              locatorId: col.locatorId,
              qty: col.qty,
              uuid: col.uuid,
              productionBatch: col.productionBatch,
              stoveLot: col.stoveLot,
              backgroundColor: col.backgroundColor,
              lotList: col.lotList,
            });
          }
        });
      });
    });
    const map = new Map();
    materialLineList.forEach(item => {
      map.set(item.uuid, item);
    });
    const newMaterialLineList = [...map.values()];
    return newMaterialLineList;
  };
  // 保存
  const handleSave = async () => {
    // Modal.confirm({
    //   title: '提示',
    //   children: (
    //     <div>
    //       <p>是否保存？</p>
    //     </div>
    //   ),
    // }).then(async (button) => {
    //   if(button === 'ok'){
    // 处理物料的数据，需要单独传materialLineList
    // 表格的数据 lineUpdateList
    // 处理row col 都从0开始
    if (!tableHeaderValue.chargingMethod) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.enter.method`).d('请填写装炉方式！'),
      });
      return;
    }
    const newMaterialLineList = handleMaterialData();
    const detailList = handleRowNumber(JSON.parse(JSON.stringify(templateList)), '-');
    const params = {
      ...tableHeaderValue,
      remark: remarkFormDs?.current?.get('remark'),
      detailList,
      materialLineList: newMaterialLineList,
      lineUpdateList: detailDs.toJSONData(),
    };
    const res = await save({
      params,
    });
    if (res && res.success) {
      notification.success({
        message: intl.get(`${modelPrompt}.message.success`).d('操作成功！'),
      });
      if (tableHeaderValue.stoveHeadId) {
        handleQueryDetail();
      } else {
        props.history.push(`/hmes/furnace-loading-diagram-platform/detail/${res?.rows}`);
      }
    }
    //   }
    // });
  };

  // 坩锅释放
  const handleCrucibleRelease = async () => {
    if (!tableHeaderValue.chargingMethod) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.enter.method`).d('请填写装炉方式！'),
      });
      return;
    }
    const newMaterialLineList = handleMaterialData();
    const detailList = handleRowNumber(JSON.parse(JSON.stringify(templateList)), '-');
    const newDetailList = detailList.map((item) => {
      return item.map((i) => {
        return i.map((t) => {
          return {
            ...t,
            releaseFlag: t.selectable ? 'Y' : '',
          }
        })
      })
    });
    // const params = {
    //   ...tableHeaderValue,
    //   remark: remarkFormDs?.current?.get('remark'),
    //   detailList: newDetailList,
    //   materialLineList: newMaterialLineList,
    //   lineUpdateList: detailDs.toJSONData(),
    // };
    const params = [...newDetailList];
    const res = await crucibleRelease({
      params,
    });
    if (res && res.success) {
      notification.success({
        message: intl.get(`${modelPrompt}.message.success`).d('操作成功！'),
      });
      if (tableHeaderValue.stoveHeadId) {
        handleQueryDetail();
      } else {
        props.history.push(`/hmes/furnace-loading-diagram-platform/detail/${res?.rows}`);
      }
    }
  };

  const submitColumns = [
    {
      name: 'materialCode',
    },
    {
      name: 'productionBatch',
    },
    {
      name: 'materialName',
    },
    {
      name: 'weight',
    },
    {
      name: 'finalMaterialCode',
    },
    {
      name: 'finalMaterialName',
    },
    {
      name: 'finalWeight',
    },
  ];

  const handleSubmit = () => {
    if (!tableHeaderValue.chargingMethod) {
      notification.error({
        message: intl.get(`${modelPrompt}.please.enter.method`).d('请填写装炉方式！'),
      });
      return;
    }
    submitDs.setQueryParameter('stoveHeadId', tableHeaderValue.stoveHeadId);
    submitDs.query();
    submitModal = Modal.open({
      title: ' ',
      destroyOnClose: true,
      style: {
        width: '1080px',
      },
      maskClosable: false,
      closable: false,
      children: <Table columns={submitColumns} dataSet={submitDs} />,
      okButton: true,
      cancelButton: true,
      onOk: async () => {
        if (submitDs.selected.length === 0) return false;
        const res = await submit({
          params: submitDs.selected.map(item => item.data),
        });
        if (res && res.success) {
          notification.success({
            message: intl.get(`${modelPrompt}.message.success`).d('操作成功！'),
          });
          handleQueryDetail();
          return true;
        }
        return false;
      },
    });

    // Modal.confirm({
    //   title: intl.get(`${modelPrompt}.prompt`).d('提示'),
    //   children: (
    //     <div>
    //       <p>{intl.get(`${modelPrompt}.submit.confirm`).d('是否确认提交？')}</p>
    //     </div>
    //   ),
    // }).then(async (button) => {
    //   if(button === 'ok'){
    //     const res = await submit({
    //       params: {
    //         stoveHeadId: tableHeaderValue.stoveHeadId,
    //       },
    //     })
    //     if(res&&res.success){
    //       notification.success({
    //         message: intl.get(`${modelPrompt}.message.success`).d('操作成功！'),
    //       })
    //       handleQueryDetail()
    //     }
    //   }
    // });
  };

  const handleBack = () => {
    props.history.push({
      pathname: `/hmes/furnace-loading-diagram-platform/list`,
      state: {
        equipmentCode: tableHeaderValue?.equipmentCode,
      },
    });
  };

  useEffect(() => {
    if (printLoading) {
      window.setTimeout(() => {
        const element = pdfWrapRef.current;

        let maxLen = 0;
        let diffWidth = 1200;

        const flattenList = templateList.flat();
        flattenList.forEach(ele => {
          if (ele.length > maxLen) {
            maxLen = ele.length;
          }
        });
        if (maxLen > 40) {
          diffWidth += ((maxLen - 30) / 5) * 200;
        }

        const opts = {
          scale: 6, // 缩放比例，提高生成图片清晰度
          useCORS: true, // 允许加载跨域的图片
          allowTaint: false, // 允许图片跨域，和 useCORS 二者不可共同使用
          tainttest: true, // 检测每张图片已经加载完成
          logging: false, // 日志开关，发布的时候记得改成 false
          windowWidth: parseInt(diffWidth),
          // y:-20,
          // scrollY:20
          // y:20
          // dpi:150
        };

        html2Canvas(element, opts).then(canvas => {
          const pdfWidth = 592.28
          const pdfHeight = 841.89
          const contentWidth = canvas.width;
          const contentHeight = canvas.height;
          // 一页pdf显示html页面生成的canvas高度;
          const pageHeight = (contentWidth / pdfHeight) * pdfWidth;
          // 未生成pdf的html页面高度
          const leftHeight = contentHeight;
          // 页面偏移
          const position = 0;
          // a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
          const imgWidth = pdfHeight
          const imgHeight = (pdfHeight / contentWidth) * contentHeight;
          const pageData = canvas.toDataURL('image/jpeg', 1.0);

          // a4纸纵向，一般默认使用；new JsPDF('landscape'); 横向页面
          const PDF = new JsPDF('landscape', 'pt', 'a4');

          // 当内容未超过pdf一页显示的范围，无需分页
          if (leftHeight < pageHeight) {
            // addImage(pageData, 'JPEG', 左，上，宽度，高度)设置
            PDF.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight);
          } else {
            const scale = pageHeight / leftHeight
            const realWidth = imgWidth * scale
            const diffX = (pdfHeight - realWidth) / 2
            PDF.addImage(pageData, 'JPEG', diffX, 0, realWidth, imgHeight * scale);
            // 超过一页时，分页打印（每页高度841.89）
            // while (leftHeight > 0) {
            //   PDF.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight);
            //   leftHeight -= pageHeight;
            //   position -= pdfWidth;
            //   if (leftHeight > 0) {
            //     PDF.addPage();
            //   }
            // }
          }
          PDF.save(
            `${tableHeaderValue.templateName}-${tableHeaderValue.equipmentCode}-${tableHeaderValue.stoveCount}` +
            '.pdf',
          );
          setPrintLoading(false);
        });
      }, 1000);
    }
  }, [printLoading]);

  const exportPdf = () => {
    setPrintLoading(true);
  };


  const handleAbandon = async () => {
    const res = await abandon({
      params: [tableHeaderValue?.stoveHeadId],
    })
    if (res && res.success) {
      notification.success({
        message: '操作成功！',
      })
      handleQueryDetail();
    }
  }

  return (
    <div id="acceptedPuted" className="FurnaceLoadingPlatformEnter hmes-style">
      <TarzanSpin
        dataSet={detailDs || stoveDs}
        spinning={
          queryTemplateLoading ||
          pointGenerateLoading ||
          saveLoading ||
          crucibleReleaseLoading ||
          lineSaveLoading ||
          queryDetailLoading ||
          submitlLoading
        }
      >
        <Header
          title={intl.get(`${modelPrompt}.title`).d('装炉图平台')}
          onBack={handleBack}
          backPath="/hmes/furnace-loading-diagram-platform/list"
        />
        <Content>
          <div style={{ marginBottom: 10, display: 'flex', justifyContent: 'center' }}>
            <div style={{ width: clientWidth, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                color="primary"
                loading={crucibleReleaseLoading}
                disabled={
                  !templateList ||
                  templateList?.length === 0 ||
                  tableHeaderValue?.status !== 'RELEASED'
                }
                onClick={handleCrucibleRelease}
              >
                {intl.get(`${modelPrompt}.select.crucibleRelease`).d('坩锅释放')}
              </Button>
              <Button
                color="primary"
                disabled={
                  !materialBtnCanEdit ||
                  !templateList ||
                  templateList?.length === 0 ||
                  ['COMPLETED', 'RELEASED', 'DISUSED'].includes(tableHeaderValue?.status)
                }
                onClick={handleMaterial}
              >
                {intl.get(`${modelPrompt}.select.material`).d('选择碳化料')}
              </Button>
              <Button
                color="primary"
                disabled={
                  !templateList ||
                  templateList?.length === 0 ||
                  !tableHeaderValue?.stoveHeadId ||
                  ['COMPLETED', 'RELEASED', 'DISUSED'].includes(tableHeaderValue?.status)
                }
                onClick={handlePoint}
              >
                {intl.get(`${modelPrompt}.point.generate`).d('点位生成')}
              </Button>
              <Button
                color="primary"
                disabled={
                  !templateList ||
                  templateList?.length === 0 ||
                  ['COMPLETED', 'RELEASED', 'DISUSED'].includes(tableHeaderValue?.status) ||
                  id === 'create'
                }
                onClick={handleClean}
              >
                {intl.get(`${modelPrompt}.reset.point`).d('重置点位')}
              </Button>

              <Button
                color="primary"
                disabled={
                  !templateList ||
                  templateList?.length === 0 ||
                  ['COMPLETED', 'RELEASED', 'DISUSED'].includes(tableHeaderValue?.status) ||
                  tableHeaderValue?.chargingMethod === 'BOX'
                }
                onClick={handleLot}
              >
                {intl.get(`${modelPrompt}.lot`).d('选择坩锅')}
              </Button>
              <Button
                color="primary"
                disabled={
                  !templateList ||
                  templateList?.length === 0 ||
                  ['COMPLETED', 'RELEASED', 'DISUSED'].includes(tableHeaderValue?.status)
                }
                onClick={handleSave}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button
                color="primary"
                disabled={
                  !templateList ||
                  templateList?.length === 0 ||
                  !tableHeaderValue?.stoveHeadId ||
                  ['COMPLETED', 'RELEASED', 'DISUSED'].includes(tableHeaderValue?.status)
                }
                onClick={handleSubmit}
              >
                {intl.get('tarzan.common.button.submit').d('提交')}
              </Button>
              <Button color="primary" onClick={handleOpen}>
                {intl.get(`${modelPrompt}.equipmentCode.change`).d('炉号切换')}
              </Button>
              <Button color="primary" onClick={exportPdf} loading={printLoading}>
                {intl.get(`${modelPrompt}.equipmentCode.exportPdf`).d('导出')}
              </Button>

              <Button
                onClick={handleAbandon}
                color="primary"
                disabled={abandonLoading || ['DISUSED', 'COMPLETED'].includes(tableHeaderValue?.status)}
              >
                {intl.get('tarzan.aps.common.button.abandon').d('废弃')}
              </Button>
            </div>
          </div>
          <div ref={pdfWrapRef}>
            <div style={{ height: '20px' }}></div>
            <div
              className={styles.tableWapper}
              style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
            >
              <table className={styles.hreeLayerTable} style={{ width: clientWidth }}>
                <thead>
                  <tr>
                    <td colSpan={10} style={{ fontSize: 20, letterSpacing: '10px' }}>
                      {tableHeaderValue?.enterpriseName}
                    </td>
                  </tr>
                  {renderHeaderTR(tableHeader)}
                  {renderHeaderTRValue(tableHeaderValue)}
                </thead>
              </table>
              {templateList &&
                templateList.length &&
                templateList?.map((item, index) => (
                  <FurnaceDiagram
                    templateListChange={templateListChange}
                    maxWidth={clientWidth}
                    colNum={item[0].length}
                    key={uuid()}
                    ref={ref => {
                      furnaceDiagramRef.current[index] = ref;
                    }}
                    templateList={item}
                    clientWidth={clientWidth}
                    level={layLevelMeaningList[index]}
                    furnaceLayer={index}
                    preBg={preBg}
                    preFontColor={preFontColor}
                    tableHeaderValue={tableHeaderValue}
                  />
                ))}
            </div>
            <div
              className={styles.printForm}
              style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
            >
              <div style={{ width: clientWidth }} className={styles.printTableWrap}>
                <Table dataSet={detailDs} columns={columns} />
                <Form
                  dataSet={remarkFormDs}
                  labelAlign="left"
                  disabled={
                    !templateList ||
                    templateList?.length === 0 ||
                    ['COMPLETED', 'RELEASED', 'DISUSED'].includes(tableHeaderValue?.status)
                  }
                >
                  {printLoading && (
                    <div name="remark">
                      <div
                        className={cs(styles.printText, {
                          [styles.printTextDisabled]:
                            !templateList ||
                            templateList?.length === 0 ||
                            ['COMPLETED', 'RELEASED', 'DISUSED'].includes(tableHeaderValue?.status),
                        })}
                        dangerouslySetInnerHTML={{
                          __html: (remarkFormDs.current.get('remark') || '').replace(
                            /(\n|\r|\r\n)/g,
                            '<br />',
                          ),
                        }}
                      ></div>
                    </div>
                  )}

                  {!printLoading && <TextArea name="remark" />}
                </Form>
              </div>
            </div>
          </div>
        </Content>
      </TarzanSpin>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.FurnaceLoadingDiagramPlatform', 'tarzan.common'],
})(FurnaceLoadingDiagramPlatform);
