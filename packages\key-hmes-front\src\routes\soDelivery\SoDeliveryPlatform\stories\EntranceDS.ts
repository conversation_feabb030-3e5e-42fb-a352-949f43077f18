/**
 * @Description: 销售发运平台 - 入口页面DS
 * @Author: <EMAIL>
 * @Date: 2022/2/9 18:02
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.soDelivery.soDeliveryPlatform';
const tenantId = getCurrentOrganizationId();

// const endUrl = '-30607';
const endUrl = '';

const headerTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: 'instructionDocId',
  autoLocateFirst: true,
  queryFields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单号'),
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
      lookupCode: 'MT.SO_DELIVERY_RETURN_DOC_TYPE',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=INSTRUCTION_DOC_STATUS_SO_DELIVERY`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteObj.siteId',
    },
    {
      name: 'customerObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      type: FieldType.number,
      bind: 'customerObj.customerId',
    },
    {
      name: 'soNumberObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.soNumber`).d('销售订单号'),
      lovCode: 'MT.SO_NUMBER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'soId',
      type: FieldType.number,
      bind: 'soNumberObj.soId',
    },
    {
      name: 'materialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'MT.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialObj.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'planDeliveryDateFrom',
      type: FieldType.dateTime,
      max: 'planDeliveryDateTo',
      label: intl.get(`${modelPrompt}.planDeliveryDateFrom`).d('计划发货日期从'),
    },
    {
      name: 'planDeliveryDateTo',
      type: FieldType.dateTime,
      min: 'planDeliveryDateFrom',
      label: intl.get(`${modelPrompt}.planDeliveryDateTo`).d('计划发货日期至'),
    },
    {
      name: 'planReturnDateFrom',
      type: FieldType.dateTime,
      max: 'planReturnDateTo',
      label: intl.get(`${modelPrompt}.planReturnDateFrom`).d('计划退货日期从'),
    },
    {
      name: 'planReturnDateTo',
      type: FieldType.dateTime,
      min: 'planReturnDateFrom',
      label: intl.get(`${modelPrompt}.planReturnDateTo`).d('计划退货日期至'),
    },
  ],
  fields: [
    {
      name: 'instructionDocId',
      type: FieldType.number,
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单号'),
    },
    {
      name: 'instructionDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
    },
    {
      name: 'sourceSystemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceSystemDesc`).d('来源系统'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户'),
    },
    {
      name: 'contactAddress',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contactAddress`).d('送货地址'),
    },
    {
      // name: 'expectedArrivalTime',
      name: 'demandTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.expectedArrivalTime`).d('计划发货日期'),
    },
    {
      // name: 'demandTime',
      name: 'expectedArrivalTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.demandTime`).d('计划退货日期'),
    },
    {
      name: 'specialFlag',
      type: FieldType.string,
      bind: 'flex.specialFlag',
      label: intl.get(`${modelPrompt}.specialFlag`).d('特殊标识'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'contactPerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contactPerson`).d('收货人'),
    },
    {
      name: 'contactTel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contactTel`).d('收货人电话'),
    },
    {
      name: 'contactFax',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contactFax`).d('传真'),
    },
    {
      name: 'logisticsMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.logisticsMode`).d('物流方式'),
    },
    {
      name: 'logisticsCompanyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.logisticsCompanyCode`).d('物流公司编码'),
    },
    {
      name: 'logisticsCompanyDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.logisticsCompanyDesc`).d('物流公司名称'),
    },
    {
      name: 'paymentType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.paymentType`).d('付款方式'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('更新时间'),
    },
    {
      name: 'flex',
    },
    {
      name: 'weighingQuantity',
      type: FieldType.string,
      bind: 'flex.weighingQuantity',
      label: intl.get(`${modelPrompt}.weighingQuantity`).d('称重数量'),
    },
    {
      name: 'weighingTime',
      type: FieldType.dateTime,
      bind: 'flex.weighingTime',
      label: intl.get(`${modelPrompt}.weighingTime`).d('称重时间'),
    },
    {
      name: 'numberPlate',
      type: FieldType.string,
      bind: 'flex.numberPlate',
      label: intl.get(`${modelPrompt}.numberPlate`).d('车牌号'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/mt-product-delivery-platform/instruction-doc/head/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_LIST.HEAD`,
        method: 'GET',
      };
    },
  },
  record: {
    dynamicProps: {
      // 关闭类型的单据不可选择
      selectable: record => !['CANCEL', 'CLOSED'].includes(record?.get('instructionDocStatus')),
    },
  },
});

const lineTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  paging: false,
  dataKey: 'rows',
  cacheSelection: true,
  primaryKey: 'instructionDocLineId',
  fields: [
    {
      name: 'instructionDocLineId',
      type: FieldType.number,
    },
    {
      name: 'identifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
    },
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'needQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.needQty`).d('需求数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('状态'),
    },
    {
      name: 'soNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNumber`).d('销售订单号'),
    },
    {
      name: 'soLineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
    },
    {
      name: 'targetSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetSiteCode`).d('站点'),
    },
    {
      name: 'targetLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetLocatorCode`).d('仓库'),
    },
    {
      name: 'prepareQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.prepareQty`).d('备货数量'),
    },
    {
      name: 'deliveryQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.deliveryQty`).d('发货数量'),
    },
    {
      name: 'receivingQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.receivingQty`).d('接收数量'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'orderFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderFlag`).d('按单标识'),
    },
    {
      name: 'toleranceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
    },
    {
      name: 'toleranceTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差值'),
    },
    {
      name: 'weighingQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.weighingQuantity`).d('称重数量'),
    },
    {
      name: 'weighingTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.weighingTime`).d('称重时间'),
    },
    {
      name: 'numberPlate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.numberPlate`).d('车牌号'),
    },
    {
      name: 'customerMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerMaterialName`).d('客户物料编码'),
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差值'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/mt-product-delivery-platform/line/detail/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_LIST.LINE`,
        method: 'GET',
      };
    },
  },
});

export { headerTableDS, lineTableDS };
