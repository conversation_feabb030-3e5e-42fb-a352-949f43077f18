// 加工件（在制品标识）DS
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.graphitizationOperationPlatform.CompleteReport';
const API = `${BASIC.HMES_BASIC}`;
// const API = '/kd-mes-20000'

const formDS = () => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  fields: [
    {
      name: 'eoCount',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eoCount`).d('打包条码'),
      min: 1,
      defaultValue: 1,
      required: true,
    },
    {
      name: 'qtyMax',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('最大总重量'),
    },
    {
      name: 'productionBatchFlag',
      type: FieldType.boolean,
      defaultValue: false,
      label: intl.get(`${modelPrompt}.productionBatchFlag`).d('工艺批次必输'),
    },
    {
      name: 'productionBatch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionBatch`).d('工艺批次'),
      dynamicProps: {
        required: ({record}) => record.get('productionBatchFlag'),
      },
      validator: value => {
        const pattern = new RegExp('[\u4e00-\u9fa5\\（，。\\）]');
        if (pattern.test(value)) {
          return intl.get(`${modelPrompt}.not.ch`).d('不允许输入中文');
        }
        return true;
      },
    },
    {
      name: 'singleWeight',
      type: FieldType.number,
      max: 'qty',
      min: 0,
      label: intl.get(`${modelPrompt}.singleWeight`).d('吨袋单位重量'),
    },
    {
      name: 'sumWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumWeight`).d('完工总量'),
    },
    {
      name: 'completionLocationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completionLocationCode`).d('完工库位'),
      lookupCode: 'HME.COMPLETED_LOCATOR_CODE',
      dynamicProps: {
        required: ({dataSet}) => dataSet.getState('type') === '管道',
      },
    },
  ],
  events: {
    update: ({ record, name, value, dataSet, oldValue})  => {      
      if (name === 'productionBatch' && value && value !== oldValue) {
        const productionBatchs = value.replace(/\s*/g,"");
        dataSet?.current?.set('productionBatch', productionBatchs);
      }
      if((name === 'eoCount'||name === 'singleWeight')&&record?.get('eoCount')&&record?.get('singleWeight')){
        const eoCount = record.get('eoCount');
        const singleWeight = record.get('singleWeight');
        record.init('sumWeight', accMul(eoCount, singleWeight));
      }
    },
  },
})

function accMul(arg1,arg2){
  let m=0; const s1=arg1.toString(); const s2=arg2.toString();
  try{m+=s1.split(".")[1].length}catch(e){};
  try{m+=s2.split(".")[1].length}catch(e){};
  return Number(s1.replace(".",""))*Number(s2.replace(".",""))/Math.pow(10,m);
}

const detailDS = () => ({
  autoQuery: false,
  autoCreate: false,
  autoQueryAfterSubmit: false,
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${API}/v1/${getCurrentOrganizationId()}/hme-graphite-platform/materialLot/info/get`,
        method: 'GET',
      };
    },
  },

  fields: [
    {
      name: 'productionBatch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionBatch`).d('工艺批次'),
    },
    {
      name: 'layerMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerMeaning`).d('层数'),
    },
    {
      name: 'serialNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('条码号'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('重量'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
  ],
});



export { formDS, detailDS };
