import React  from 'react';
import { DataSet, Table,  Button } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
import { useDataSetEvent } from 'utils/hooks';
import { useRequest } from '@components/tarzan-hooks';
import withProps from 'utils/withProps';
import { observer } from 'mobx-react-lite';
import {Save, Export} from './services';
import { tableDS } from './stores';
import  './index.module.less';

const modelPrompt = 'tarzan.tariffDifferAdjustmentPlatform';


const TariffDifferAdjustmentPlatform =observer(props => {
  const { tableDs } = props;
  const { run: save, loading: saveLoading } = useRequest(Save(), { manual: true, needPromise: true });
  const { run: exportFun, loading: exportLoading } = useRequest(Export(), { manual: true, needPromise: true });

  const handleData = (data) => {
    let newData = []
    const stoveCodeList = data.map(item => item.stoveCode).filter((item, index, arr) => arr.indexOf(item) === index)
    stoveCodeList.forEach(item => {
      const arr = data.filter(i => i.stoveCode === item)
      arr.forEach((i, index) => {
        i.smhTrueValue = i.smhTrueValue?JSON.parse(i.smhTrueValue).valueList:''
        i.bbTrueValue = i.bbTrueValue?JSON.parse(i.bbTrueValue):''
        if(i.bbTrueValue){
          if(i.bbTrueValue?.range?.length){
            i.bbTrueValue = i.bbTrueValue?.range?.map(m => m.limits)
          }else if(i.bbTrueValue?.judgeValue){
            i.bbTrueValue = i.bbTrueValue?.judgeValue
          }else if(i.bbTrueValue?.valueList){
            i.bbTrueValue = i.bbTrueValue?.valueList
          }else if(i.bbTrueValue?.text){
            i.bbTrueValue = i.bbTrueValue?.text
          }
        }
        i.merge = true
        if (index === 0) {
          i.rowSpan = arr.length
        } else {
          i.rowSpan = 0
        }
      })
      newData = newData.concat(arr)
    })
    tableDs.loadData(newData)
  }

  useDataSetEvent(tableDs, 'load', () => {
    if(tableDs.toData().every(item => !item.merge)) {
      handleData(tableDs.toData())
    }
  });

  // 头列表配置
  const columns = [
    {
      name: 'creationDate',
      width: 100,
    },
    {
      name: 'stoveCode',
    },
    {
      name: 'stoveCount',
    },
    {
      name: 'materialName',
    },
    {
      name: 'sourceObjectCode',
    },
    {
      name: 'layerLevelDesc',
    },
    {
      name: 'bbInspectValue',
    },
    {
      name: 'bbTrueValue',
      width: 100,
    },
    {
      name: 'smhInspectValue',
    },
    {
      name: 'smhTrueValue',
    },
    {
      name: 'sumWeight',
      onCell({ record }) {
        const { rowSpan } = record.data;
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'totalConsumption',
      editor: true,
      onCell({ record }) {
        const { rowSpan } = record.data;
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'unitConsumption',
      editor: true,
      onCell({ record }) {
        const { rowSpan } = record.data;
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'reboundTime',
      onCell({ record }) {
        const { rowSpan } = record.data;
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
      editor: true,
    },
    {
      name: 'staggerTime',
      editor: true,
      onCell({ record }) {
        const { rowSpan } = record.data;
        return {
          rowSpan,
          hidden: rowSpan === 0,
        };
      },
    },
    {
      name: 'inspectResult',
      renderer({ value }) {
        return value === 'OK' ? <span>合格</span> : value === 'NG' ?<span style={{color: 'red'}}>不合格</span>:''
      },
    },
    {
      name: 'ngInspectResult',
    },
  ];



  const handleSave = async () => {
    const data = tableDs.toData()
    const updateList = tableDs.toJSONData()
    data.forEach(item => {
      updateList.forEach(i => {
        if(i.stoveCode === item.stoveCode) {
          item.totalConsumption = i.totalConsumption;
          item.unitConsumption = i.unitConsumption;
          item.reboundTime = i.reboundTime;
          item.staggerTime = i.staggerTime;
        }
      })
    })
    const res = await save({
      params: data.map(item => ({
        inspectObjectId: item.inspectObjectId,
        sourceObjectCode: item.sourceObjectCode,
        stoveCode: item.stoveCode,
        totalConsumption: item.totalConsumption,
        unitConsumption: item.unitConsumption,
        reboundTime: item.reboundTime,
        staggerTime: item.staggerTime,
      })),
    })
    if(res&&res.success){
      notification.success({
        message: '保存成功！',
      })
      tableDs.query();
    }
  }

  const handleExport = async () => {
    const res = await exportFun({
      params: {
        ...tableDs.queryDataSet.toData()[0],
      },
    })
    const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
    const a = document.createElement('a');
    const url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = decodeURI('石墨化检测数据表.xlsx');
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    a.parentNode?.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('石墨化检测数据表')}>
        <Button onClick={handleExport} color="primary" disabled={exportLoading||!tableDs.records.length}>
          {intl.get(`hzero.common.button.export`).d('导出')}
        </Button>
        <Button color="primary" icon="save" onClick={handleSave} disabled={saveLoading}>
          {intl.get(`hzero.common.button.save`).d('保存')}
        </Button>
      </Header>
      <Content className="graphitizationDetectionDataTable">
        <Table
          searchCode="dfcytzpt"
          customizedCode="dfcytzpt"
          dataSet={tableDs}
          columns={columns}
          border
          highLightRow={false}
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
          }}
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.model.hmes.userRights', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(TariffDifferAdjustmentPlatform),
);
