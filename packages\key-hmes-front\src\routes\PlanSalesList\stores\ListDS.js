import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.planSalesList';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

const tableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-plan-sales/list/ui`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.lotCode`).d('工厂'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteObj.siteId',
    },
    {
      name: 'operationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationObj`).d('工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      noCache: true,
      ignore: 'always',
    },
    {
      name: 'operationId',
      bind: 'operationObj.operationId',
    },
    {
      name: 'workcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellObj`).d('工位'),
      lovCode: 'HMES.WORKCELL_STATIONS',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'workcellId',
      bind: 'workcellObj.workcellId',
    },
    {
      name: 'planDateStart',
      label: intl.get(`${modelPrompt}.planDateStart`).d('开始时间'),
      type: 'date',
      max: 'planDateEnd',
    },
    {
      name: 'planDateEnd',
      label: intl.get(`${modelPrompt}.planDateEnd`).d('结束时间'),
      type: 'date',
      min: 'planDateStart',
    },    
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('计划销售产品'),
      lookupCode: 'HME.PRODUCT_TYPE',
    },
  ],
  fields: [
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂编码'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      ignore: 'always',
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteObj.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteObj.siteCode',
    },
    {
      label: intl.get(`${modelPrompt}.siteName`).d('工厂描述'),
      name: 'siteName',
      bind: 'siteObj.siteName',
    },
    {
      name: 'operationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationObj`).d('工艺编码'),
      lovCode: 'MT.METHOD.OPERATION',
      noCache: true,
      ignore: 'always',
      required: true,
    },
    {
      name: 'operationId',
      bind: 'operationObj.operationId',
    },{
      name: 'operationName',
      bind: 'operationObj.operationName',
    },
    {
      name: 'operationDesc',
      label: intl.get(`${modelPrompt}.operationDesc`).d('工艺描述'),
      bind: 'operationObj.operationDesc',
    },
    {
      name: 'workcellObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workcellObj`).d('工位'),
      lovCode: 'HMES.WORKCELL_STATIONS',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        // locale: 'zhCN',
      },
    },
    {
      name: 'workcellId',
      bind: 'workcellObj.workcellId',
    },{
      name: 'workcellCode',
      bind: 'workcellObj.workcellCode',
    },
    {
      name: 'planDate',
      label: intl.get(`${modelPrompt}.planDate`).d('日期'),
      type: 'date',
    },
    { 
      name: 'planQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.planQty`).d('计划产量'),
      min: 0.01,
      step:0.01,
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('计划销售产品'),
      lookupCode: 'HME.PRODUCT_TYPE',
    },    
    { 
      name: 'planSaleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.planSaleQty`).d('计划销售量'),
      // required: true,
      min: 0.01,
      step:0.01,
    },
    {
      name: 'lastUpdate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdate`).d('更新人'),
    },
    {
      name: 'operator',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.action').d('操作'),
    },
    {
      name: 'planWeight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planWeight`).d('计划石墨化炉重量'),
    },
    {
      name: 'planType',
      type: FieldType.string,
      lookupCode: 'HME.PLAN_SALE_TYPE',
      label: intl.get(`${modelPrompt}.planType`).d('计划类别（重量、炉次)'),
    },
  ],
});


export { tableDS };
