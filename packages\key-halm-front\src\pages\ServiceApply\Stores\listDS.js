import intl from 'utils/intl';
import { dateTimeRender } from 'utils/renderer';
import { DEFAULT_DATETIME_FORMAT } from 'utils/constants';

const promptCode = 'amtc.serviceApply.model.serviceApply';

const strategy = () => ({
  basic: [
    {
      name: 'srNumber',
      label: intl.get(`${promptCode}.srNumber`).d('申请编号'),
      type: 'string',
    },
    {
      name: 'srName',
      label: intl.get(`${promptCode}.srName`).d('申请概述'),
      type: 'string',
    },
    {
      name: 'srStatus',
      label: intl.get(`${promptCode}.applyStatus`).d('申请状态'),
      type: 'string',
      lookupCode: 'AMTC.SR_STATUS',
    },
  ],
  priority: [
    {
      name: 'priorityLov',
      type: 'object',
      label: intl.get(`${promptCode}.priorityName`).d('优先级'),
      ignore: 'always',
      lovCode: 'AMTC.PRIORITIES',
    },
    {
      name: 'priorityId',
      type: 'number',
      bind: 'priorityLov.priorityId',
    },
  ],
  srType: [
    {
      name: 'srTypeLov',
      type: 'object',
      lovCode: 'AMTC.SRTYPES',
      label: intl.get(`${promptCode}.srType`).d('服务申请类型'),
      ignore: 'always',
    },
    {
      name: 'srTypeId',
      type: 'string',
      bind: 'srTypeLov.srTypeId',
    },
  ],
  org: [
    {
      name: 'orgName',
      type: 'string',
      label: intl.get(`${promptCode}.orgName`).d('需求组织'),
      ignore: 'always',
    },
  ],
  reportor: [
    {
      name: 'reporterLov',
      type: 'object',
      lovCode: 'AORI.EMPLOYEE_UNIT',
      label: intl.get(`${promptCode}.reporter`).d('报告人'),
      ignore: 'always',
    },
    {
      name: 'reporterId',
      type: 'number',
      bind: 'reporterLov.employeeId',
    },
  ],
  reportDate: [
    {
      name: 'reportDateFrom',
      label: intl.get(`${promptCode}.reportDateFrom`).d('报告日期从'),
      type: 'dateTime',
      max: 'reportDateTo',
      format: DEFAULT_DATETIME_FORMAT,
      transformRequest: value => dateTimeRender(value),
    },
    {
      name: 'reportDateTo',
      label: intl.get(`${promptCode}.reportDateTo`).d('报告日期至'),
      type: 'dateTime',
      min: 'reportDateFrom',
      format: DEFAULT_DATETIME_FORMAT,
      transformRequest: value => dateTimeRender(value),
    },
  ],
  maintSite: [
    {
      name: 'maintSiteLov',
      type: 'object',
      lovCode: 'AMDM.ASSET_MAINT_SITE',
      label: intl.get(`${promptCode}.maintSite`).d('服务区域'),
      ignore: 'always',
    },
    {
      name: 'maintSiteId',
      type: 'number',
      bind: 'maintSiteLov.maintSiteId',
    },
  ],
  asset: [
    {
      name: 'assetLov',
      type: 'object',
      lovCode: 'AAFM.ASSET_RECEIPT',
      label: intl.get(`${promptCode}.asset`).d('设备'),
      noCache: true,
      ignore: 'always',
      lovPara: {
        aclFlag: 1,
      },
    },
    {
      name: 'assetId',
      type: 'string',
      bind: 'assetLov.assetId',
    },
    {
      name: 'assetName',
      type: 'string',
      bind: 'assetLov.assetName',
      label: intl.get(`${promptCode}.asset`).d('设备'),
    },
  ],
  location: [
    {
      name: 'locationLov',
      type: 'object',
      lovCode: 'AMDM.LOCATIONS',
      label: intl.get(`${promptCode}.assetLocation`).d('位置'),
      noCache: true,
      ignore: 'always',
    },
    {
      name: 'assetLocationId',
      type: 'string',
      bind: 'locationLov.assetLocationId',
    },
    {
      name: 'assetLocationName',
      label: intl.get(`${promptCode}.assetLocation`).d('位置'),
      type: 'string',
      bind: 'locationLov.locationName',
    },
  ],
});

const listFields = () => {
  return [
    {
      name: 'srTypeIcon',
      type: 'string',
      label: intl.get(`${promptCode}.icon`).d('图标'),
    },
    {
      name: 'srNumber',
      type: 'string',
      label: intl.get(`${promptCode}.srNumber`).d('申请编号'),
    },
    {
      name: 'srName',
      type: 'string',
      label: intl.get(`${promptCode}.srName`).d('申请概述'),
    },
    {
      name: 'assetLocationName',
      label: intl.get(`${promptCode}.assetLocation`).d('位置'),
      type: 'string',
    },
    {
      name: 'assetName',
      label: intl.get(`${promptCode}.asset`).d('设备'),
      type: 'string',
    },
    {
      name: 'object',
      label: intl.get(`${promptCode}.object`).d('对象'),
      type: 'string',
    },
    {
      name: 'plannerName',
      label: intl.get(`${promptCode}.planner`).d('计划员'),
      type: 'string',
    },
    {
      name: 'srStatusMeaning',
      type: 'string',
      label: intl.get(`${promptCode}.applyStatus`).d('申请状态'),
    },
    {
      name: 'ownerName',
      label: intl.get(`alm.common.model.principal`).d('负责人'),
      type: 'string',
    },
    {
      name: 'orgName',
      type: 'string',
      label: intl.get(`${promptCode}.org`).d('需求组织'),
    },
    {
      name: 'descAndLabel',
      type: 'string',
      label: intl.get(`${promptCode}.object`).d('对象'),
    },
    {
      name: 'description',
      type: 'string',
      label: intl.get(`${promptCode}.description`).d('描述'),
    },
    {
      name: 'reporterName',
      type: 'string',
      label: intl.get(`${promptCode}.reporter`).d('报告人'),
    },
    {
      name: 'woNum',
      type: 'string',
      label: intl.get(`${promptCode}.woNum`).d('单据编号'),
    },
    {
      name: 'basicTypeFlag',
      type: 'number',
      label: intl.get(`alm.common.model.documentType`).d('单据类型'),
    },
  ];
};

export { strategy, listFields };
