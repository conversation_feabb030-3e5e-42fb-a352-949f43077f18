/**
 * @Description:  完工报表-入口页
 */
import React, {  } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { flow, isNil } from 'lodash';
import { BASIC } from '@utils/config';
import { tableDS } from './stores/ListDS';

const endUrl = '';
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.completeReport';

const CompleteReportList = observer(props => {
  const { tableDs } = props;

  // 头列表配置
  const columns = [
    {
      name: 'plantCode',
      align: 'center',
    },
    {
      name: 'areaCode',
      align: 'center',
    },
    {
      name: 'prodLineCode',
      align: 'center',
    },
    {
      name: 'sapWorkOrderNum',
      align: 'center',
    },
    {
      name: 'sapStatus',
      align: 'center',
      width: 150,
    },
    {
      name: 'sapQty',
      align: 'center',
      width: 150,
    },
    {
      name: 'workOrderNum',
      align: 'center',
    },
    {
      name: 'status',
      align: 'center',
      width: 150,
    },
    {
      name: 'qty',
      align: 'center',
      width: 150,
    },
    {
      name: 'materialCode',
      align: 'center',
      width: 150,
    },
    {
      name: 'materialName',
      align: 'center',
      width: 150,
    },
    {
      name: 'materialLotCode',
      align: 'center',
    },
    {
      name: 'primaryUomQty',
      align: 'center',
    },
    {
      name: 'uomCode',
      align: 'center',
    },
    {
      name: 'eoNum',
      align: 'center',
    },
    {
      name: 'eoStatus',
      align: 'center',
      width: 150,
    },
    {
      name: 'eoQty',
      align: 'center',
      width: 150,
    },
    {
      name: 'operationName',
      align: 'center',
    },
    {
      name: 'operationDesc',
      align: 'center',
    },
    {
      name: 'workcellCode',
      align: 'center',
      width: 150,
    },
    {
      name: 'workcellName',
      align: 'center',
      width: 150,
    },
    {
      name: 'lot',
      align: 'center',
    },
    {
      name: 'productionBatch',
      align: 'center',
    },
    {
      name: 'locatorCode',
      align: 'center',
    },
    {
      name: 'creationDate',
      align: 'center',
    },
    {
      name: 'createdByName',
      align: 'center',
    },
    {
      name: 'transReasonCode',
      align: 'center',
    },
    {
      name: 'eventId',
      align: 'center',
    },
    {
      name: 'lastUpdateDate',
      align: 'center',
      width: 180,
    },
  ];

  const onFieldEnterDown = () => {
    tableDs.query(props.tableDs.currentPage);
  };

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParams = tableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return queryParams;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('完工报表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-completion-report/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="CompleteReport"
          customizedCode="CompleteReport"
          dataSet={tableDs}
          columns={columns}
          highLightRow
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
            onFieldEnterDown,
          }}
        />
      </Content>
    </div>
  );
});

export default flow(
  formatterCollections({ code: ['tarzan.completeReport', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({...tableDS()});
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(CompleteReportList);
