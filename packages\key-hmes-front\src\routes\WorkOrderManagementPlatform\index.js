import React, { useEffect, useState, useMemo } from 'react';
import {
  Button,
  DataSet,
  Table,
  Modal,
  Form,
  NumberField,
  // Select,
  Spin,
  Lov,
  DatePicker,
} from 'choerodon-ui/pro';
import { HZERO_PLATFORM } from 'utils/config';
// import { useRequest } from '@components/tarzan-hooks';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import notification from 'utils/notification';
// import { drawerPropsC7n } from '@components/tarzan-ui';
import { BASIC } from '@utils/config';
import { queryMapIdpValue } from 'services/api';
import {
  tableDS,
  dispatchFormDS,
  dispatchDateFormDS,
  specifyBatchFormDS,
  splitFormDS,
  historyDS,
  // levelDS,
  // bomAndRouterDS,
  // prodDS,
  drawerDS,
} from './stores/WorkOrderManagementPlatformDS';
import HistoryDrawer from "./HistoryDrawer";
// import BomAndRouterDrawer from './BomAndRouterDrawer';

const tenantId = getCurrentOrganizationId();
// const Host = `/yp-mes-24308`;
const modelPrompt = 'tarzan.hmes.WorkOrderManagementPlatform';
const Host = BASIC.HMES_BASIC;

const WorkOrderManagementPlatform = props => {
  const [selectedLength, setSelectedLength] = useState(0);
  const [selectedRow, setSelectedRow] = useState([]);
  const [loading, setLoading] = useState(false);
  const [commandDispatchLoading, setCommandDispatchLoading] = useState(false);
  const [productionBatchLoading, setProductionBatchLoading] = useState(false);

  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 复制ds
  const dispatchFormDs = useMemo(() => new DataSet(dispatchFormDS()), []); // 复制ds
  const dispatchDateFormDs = useMemo(() => new DataSet(dispatchDateFormDS()), []); // 复制ds
  const specifyBatchFormDs = useMemo(() => new DataSet(specifyBatchFormDS()), []); // 复制ds
  const splitFormDs = useMemo(() => new DataSet(splitFormDS()), []); // 复制ds
  // const levelDs = useMemo(() => new DataSet(levelDS()), []);
  // const bomAndRouterDs = useMemo(() => new DataSet(bomAndRouterDS()), []);
  // const prodDs = useMemo(() => new DataSet(prodDS()), []);
  const drawerDs = useMemo(() => new DataSet(drawerDS()), []);
  const historyDs = useMemo(() => new DataSet(historyDS()), []);
  const { path } = props.match;

  let splitModal;
  let dispatchModal;
  let batchModal;
  let drawerModal;
  // const { run } = useRequest(
  //   {
  //     url: `${Host}/v1/${tenantId}/hme-work-order-new/bom/router/update`,
  //     method: 'POST',
  //   },
  //   { manual: true, needPromise: true },
  // );
  useEffect(() => {
    // 添加选中监听事件
    tableDs.addEventListener('select', handleDataSetSelect);
    tableDs.addEventListener('unSelect', handleDataSetSelect);
    tableDs.addEventListener('selectAll', handleDataSetSelect);
    tableDs.addEventListener('unSelectAll', handleDataSetSelect);
    tableDs.addEventListener('query', () => {
      setSelectedLength(0);
    });
  }, []);

  // 选中事件
  const handleDataSetSelect = () => {
    setSelectedLength(tableDs.selected.length);
    setSelectedRow(tableDs.selected);
  };

  useEffect(() => {
    // 查询值集
    queryMapIdpValue({
      specifyBatchList: 'HME.LIMIT_SPECIFY_BATCH',
    }).then(res => {
      if (res && !res.failed) {
        window.localStorage.setItem('specifyBatchList', JSON.stringify(res.specifyBatchList));
      }
    });
  }, []);

  // 跳转生产指令详情
  const goWorkOrderDeatil = id => {
    props.history.push(`/hmes/workshop/production-order-mgt/detail/${id}`);
  };

  // 跳转制造装配详情
  const goBomDeatil = record => {
    if (record.data.status === 'RELEASED' && record.data.workOrderType === 'REPAIR') {
      props.history.push(`/hmes/product/assembly-list/dist/${record.data.bomId}`);
    } else {
      props.history.push(`/hmes/product/manufacture-list/dist/${record.data.bomId}`);
    }
  };

  // 跳转制造工艺详情
  const goRouterDeatil = record => {
    if (record.data.status === 'RELEASED' && record.data.workOrderType === 'REPAIR') {
      props.history.push(`/hmes/new/process/routes-c7n/dist/${record.data.routerId}`);
    } else {
      props.history.push(`/hmes/new/manufacture-process/routes-c7n/dist/${record.data.routerId}`);
    }
  };

  // 拆分数量弹框
  const openSplitModal = () => {
    splitFormDs
      .getField('prodLineObj')
      .setLovPara('organizationId', selectedRow[0].data.organizationId);
    splitFormDs.getField('prodLineObj').setLovPara('workOrderId', selectedRow[0].data.workOrderId);
    splitFormDs.current.init('splitQty', null);
    splitFormDs.reset();
    splitModal = Modal.open({
      key: Modal.key(),
      title: '',
      width: 400,
      destroyOnClose: true,
      children: (
        <Form dataSet={splitFormDs} columns={1} style={{ height: '190px' }}>
          <Lov name="prodLineObj" required />
          <NumberField name="splitQty" required />
        </Form>
      ),
      footer: (
        <div style={{ float: 'right', marginTop: '-50px' }}>
          <Button
            onClick={() => {
              cancelSplit();
            }}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            color="primary"
            type="submit"
            onClick={() => {
              splitConfirm();
            }}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  // 拆分数量确定
  const splitConfirm = async () => {
    const validateResult = await splitFormDs.validate();
    if (validateResult) {
      const data = splitFormDs.toData()[0];
      const params = {
        workOrderId: selectedRow[0].data.workOrderId,
        ...data,
        prodLineId: data.organizationId,
      };
      const qtyNum = Number(selectedRow[0].data.qty || 0);
      if (Number(data.splitQty) > qtyNum) {
        return notification.error({
          message: `当前工单待拆分数量为${qtyNum}，不可超过该数量！`,
        });
      }
      setLoading(true);
      request(`${Host}/v1/${tenantId}/hme-work-order-new/wo/split/ui`, {
        method: 'POST',
        body: { ...params },
      }).then(res => {
        setLoading(false);
        if (res && res.success) {
          notification.success();
          splitModal.close();
          tableDs.query();
        } else {
          notification.error({ message: res.message });
        }
      });
    }
  };

  // 拆分数量弹框取消
  const cancelSplit = async () => {
    splitModal.close();
  };

  // 关闭
  const handleProductCancel = () => {
    Modal.confirm({
      title: intl.get('tarzan.common.button.notice').d('提示'),
      children: (
        <div>
          <p>确认是否关闭工单？</p>
        </div>
      ),
    }).then(async button => {
      if (button === 'ok') {
        const params = {
          workOrderId: selectedRow[0].data.workOrderId,
        };
        setLoading(true);
        const res = await request(`${Host}/v1/${tenantId}/hme-work-order-new/close`, {
          method: 'GET',
          query: { ...params },
        });
        setLoading(false);
        if (res && res.success) {
          notification.success();
          tableDs.query();
        } else {
          notification.error({
            message: res.message,
          });
        }
      }
    });
  };

  // 取消关闭
  const handleCancelClose = () => {
    const selectedData = selectedRow[0].data;
    if (selectedData.status !== 'CLOSED') {
      return notification.error({
        message: `当前工单${selectedData.workOrderNum}状态不为关闭状态状态，无法取消关闭，请检查！`,
      });
    }
    setLoading(true);
    request(`${Host}/v1/${tenantId}/hme-work-order-new/cancel/close`, {
      method: 'GET',
      query: {
        workOrderId: tableDs.selected[0].data.workOrderId,
      },
    }).then(res => {
      setLoading(false);
      if (res && res.success) {
        notification.success();
        tableDs.query();
      } else {
        notification.error({ message: res.message });
      }
    });
  };

  // 取消完成
  const handleCancelComplete = () => {
    setLoading(true);
    request(`${Host}/v1/${tenantId}/hme-work-order-new/cancel/complete`, {
      method: 'POST',
      body: tableDs.selected.map((item) => item.get('workOrderId')),
    }).then(res => {
      setLoading(false);
      if (res && res.success) {
        notification.success();
        tableDs.query();
        setSelectedRow([]);
      } else {
        notification.error({ message: res.message });
      }
    });
  };

  // 上移
  // const handleUp = () => {
  //   const params = {
  //     workOrderId: selectedRow[0].data.workOrderId,
  //   };
  //   setLoading(true);
  //   request(`${Host}/v1/${tenantId}/hme-work-order-new/priority/up`, {
  //     method: 'GET',
  //     query: { ...params },
  //   }).then(res => {
  //     if (res && res.success) {
  //       notification.success();
  //       tableDs.query();
  //     } else {
  //       notification.error({ message: res.message });
  //     }
  //     setLoading(false);
  //   });
  // };

  // 下移
  // const handleDown = () => {
  //   const params = {
  //     workOrderId: selectedRow[0].data.workOrderId,
  //   };
  //   setLoading(true);
  //   request(`${Host}/v1/${tenantId}/hme-work-order-new/priority/down`, {
  //     method: 'GET',
  //     query: { ...params },
  //   }).then(res => {
  //     if (res && res.success) {
  //       notification.success();
  //       tableDs.query();
  //     } else {
  //       notification.error({ message: res.message });
  //     }
  //     setLoading(false);
  //   });
  // };

  // 置顶
  // const handleTop = () => {
  //   const params = {
  //     workOrderId: selectedRow[0].data.workOrderId,
  //   };
  //   setLoading(true);
  //   request(`${Host}/v1/${tenantId}/hme-work-order-new/priority/top`, {
  //     method: 'GET',
  //     query: { ...params },
  //   }).then(res => {
  //     if (res && res.success) {
  //       notification.success();
  //       tableDs.query();
  //     } else {
  //       notification.error({ message: res.message });
  //     }
  //     setLoading(false);
  //   });
  // };

  // 指令拆分
  const handleWorkOrderSplit = () => {
    splitFormDs.create({});
    openSplitModal();
  };

  // 指定工艺批次
  const handleProductionBatch = ()=>{
    setProductionBatchLoading(true);
    const selectedData = selectedRow[0]?.data;
    const workOrderId = selectedRow[0]?.data?.workOrderId;
    const flag = JSON.parse(window.localStorage.getItem('specifyBatchList')).filter(item => item.value === selectedData.attribute1)[0]
    if (flag) {
      setCommandDispatchLoading(false);
      return notification.error({
        message: `当前工单${selectedData.workOrderNum}不允许指定工艺批次，请检查！`,
      });
    }
    specifyBatchFormDs.setQueryParameter('workOrderId', workOrderId);
    specifyBatchFormDs.query();
    batchModal = Modal.open({
      key: Modal.key(),
      title: '指定工艺批次',
      style:{
        width: 800,
      },
      destroyOnClose: true,
      children: (
        <Table
          queryBar="none"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={specifyBatchFormDs}
          columns={modalColumns}
          style={{ height: 400 }}
        />
      ),
      onOk: async () => {
        if(await specifyBatchFormDs.validate()){
          const obj = specifyBatchFormDs.toData()
          confirmBatch(obj)
          return true
        }
        return false
      },
      onCancel: () => setProductionBatchLoading(false),
    });
  };

  // 指定工艺批次
  const confirmBatch = async (data) => {
    setLoading(true);
    const res = await request(`${Host}/v1/${tenantId}/hme-work-order-new/production/batch/confirm`, {
      method: 'POST',
      body: data,
    })
    setProductionBatchLoading(false);
    setLoading(false);

    if (res && res.success) {
      notification.success();
      batchModal.close();
      tableDs.query();
    } else {
      notification.error({ message: res.message });
    }
  };

  // 指令派工
  const handleWorkOrderDispatch = () => {
    setCommandDispatchLoading(true);
    const selectedData = selectedRow[0].data;
    if (selectedData.status !== 'RELEASED' && selectedData.status !== 'EORELEASED') {
      setCommandDispatchLoading(false);
      return notification.error({
        message: `当前工单${selectedData.workOrderNum}状态不为下达或下达作业状态，不允许派工，请检查！`,
      });
    }
    const tag = JSON.parse(window.localStorage.getItem('specifyBatchList')).filter(item => item.value === selectedData.attribute1)[0]
    if (!tag && selectedData.productionBatch === '') {
      setCommandDispatchLoading(false);
      return notification.error({
        message: `当前工单${selectedData.workOrderNum}未指定工艺批次，无法进行派工，请检查！`,
      });
    }

    // let tempDs;
    // if (selectedData.attribute1 === '130') {
    //   dispatchFormDs.setState('workOrderQty', selectedData.qty);
    //   tempDs = dispatchFormDs;
    // } else {
    //   tempDs = dispatchDateFormDs;
    // }
    // tempDs.create({});
    openDispatchModal(selectedData);
  };

  // 派工弹框
  const openDispatchModal = async (selectedData) => {
    const res = await request(`${HZERO_PLATFORM}/v1/lovs/data`, {
      query: {
        lovCode: 'HME.DISPATCH_MODE',
      },
    })
    if(res&&res.length){
      const tag = res.filter(item => item.value === selectedData.attribute1)[0]?.tag

      if(tag === 'Y'){
        dispatchFormDs.current.init('qty', null)
        dispatchFormDs.current.getField('qty').set('max', selectedData.qty)
        dispatchModal = Modal.open({
          key: Modal.key(),
          title: '',
          width: 400,
          destroyOnClose: true,
          children: (
            <Form dataSet={dispatchFormDs} columns={1} style={{ height: '160px' }}>
              <NumberField name="qty" />
            </Form>
          ),
          onOk: async () => {
            if(await dispatchFormDs.validate()){
              const obj = dispatchFormDs.toData()
              dispatchConfirm(obj[0])
              return true
            }
            return false
          },
          onCancel: () => setCommandDispatchLoading(false),
        });
      }else if(tag === 'N'){
        dispatchDateFormDs.current.init('startDate', null);
        dispatchDateFormDs.current.init('endDate', null);
        dispatchModal = Modal.open({
          key: Modal.key(),
          title: '',
          width: 400,
          destroyOnClose: true,
          children: (
            <Form dataSet={dispatchDateFormDs} columns={1} style={{ height: '160px' }}>
              <DatePicker name="startDate" />
              <DatePicker name="endDate" />
            </Form>
          ),
          onOk: async () => {
            if(await dispatchDateFormDs.validate()){
              const obj = dispatchDateFormDs.toData()
              dispatchConfirm(obj[0])
              return true
            }
            return false
          },
          onCancel: () => setCommandDispatchLoading(false),
        });
      }else{
        setCommandDispatchLoading(false);
        notification.error({
          message: `未获取到区域对应的拆分方式，请检查！`,
        })
      }
    }else{
      setCommandDispatchLoading(false);
      notification.error({ message: res.message });
    }
  };

  // 派工确定
  const dispatchConfirm = async (data) => {
    const params = {
      workOrderId: selectedRow[0].data.workOrderId,
      attribute1: selectedRow[0].data.attribute1,
      ...data,
    };
    setLoading(true);
    const res = await request(`${Host}/v1/${tenantId}/hme-work-order-new/dispatch`, {
      method: 'POST',
      body: { ...params },
    })
    setCommandDispatchLoading(false);
    setLoading(false);

    if (res && res.success) {
      notification.success();
      dispatchModal.close();
      tableDs.query();
    } else {
      notification.error({ message: res.message });
    }
  };

  const columns = [
    // 工厂
    {
      name: 'siteCode',
      align: 'left',
      lock: 'left',
      width: 200,
    },
    // 工单号
    {
      name: 'workOrderNum',
      align: 'left',
      lock: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => goWorkOrderDeatil(record.data.workOrderId)}>{value}</a>
          </span>
        );
      },
    },
    // 工单状态
    {
      name: 'statusDesc',
      align: 'left',
      lock: 'left',
    },
    // 工单类型
    {
      name: 'workOrderTypeDesc',
      align: 'left',
      lock: 'left',
    },
    // 物料编码
    {
      name: 'materialCode',
      align: 'left',
      lock: 'left',
    },
    // 物料描述
    {
      name: 'materialName',
      align: 'left',
      lock: 'left',
    },
    // 是否分解
    {
      name: 'splitFlag',
      align: 'left',
    },
    // 生产线
    {
      name: 'prodLineName',
      align: 'left',
    },
    // 生产线
    {
      name: 'prodLineCode',
      align: 'left',
    },
    // 生产数量
    {
      name: 'qty',
      align: 'left',
    },
    // 完工数量
    {
      name: 'completedQty',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => openModal(record.data.workOrderId, 'COMPLETED')}>{value}</a>
          </span>
        );
      },
    },
    // 在制数量
    {
      name: 'releasedQty',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => openModal(record.data.workOrderId, 'WORKING')}>{value}</a>
          </span>
        );
      },
    },
    // 报废数量
    {
      name: 'scrappedQty',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => openModal(record.data.workOrderId, 'CLOSED')}>{value}</a>
          </span>
        );
      },
    },
    // 优先级
    {
      name: 'priority',
      align: 'left',
    },
    // 装配清单名称
    {
      name: 'bomName',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => goBomDeatil(record)}>{value}</a>
          </span>
        );
      },
    },
    // 装配清单版本
    {
      name: 'bomRevision',
      align: 'left',
    },
    // 工艺路线名称
    {
      name: 'routerName',
      align: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => goRouterDeatil(record)}>{value}</a>
          </span>
        );
      },
    },

    {
      name: 'stoveCode',
      align: 'left',
    },
    // 工艺路线版本
    {
      name: 'routerRevision',
      align: 'left',
    },
    // 计划开始时间
    {
      name: 'planStartTime',
      align: 'left',
    },
    // 计划结束时间
    {
      name: 'planEndTime',
      align: 'left',
    },
    // 实际完成时间
    {
      name: 'actualEndDate',
      align: 'left',
    },
    {
      name: 'attribute1',
      align: 'left',
    },
    {
      name: 'organizationName',
      align: 'left',
    },
    {
      name: 'uomCode',
      align: 'left',
    },
    // {
    //   name: 'attribute2',
    //   align: 'left',
    // },
    // {
    //   name: 'level',
    //   align: 'left',
    // },
    // {
    //   name: 'parentWorkOrderNum',
    //   align: 'left',
    // },
    {
      name: 'productionVision',
      align: 'left',
    },
    {
      name: 'areaWorkOrder',
      align: 'left',
    },
    {
      name: 'productionBatch',
      align: 'left',
    },
  ];

  const modalColumns = [
    {
      name: 'materialCode',
      align: 'left',
      width: 150,
    },{
      name: 'materialName',
      align: 'left',
      width: 200,
    },{
      name: 'type',
      align: 'left',
      width: 100,
    },{
      name: 'productionBatch',
      align: 'left',
      editor: true,
      width: 200,
    },
  ];
  const handleOpenTab = eoId => {
    drawerModal.close();
    props.history.push(`/hmes/workshop/execute-operation-management/detail/${eoId}`);
  };

  const drawerColumns = [
    {
      name: 'eoNum',
      width: 200,
      lock: 'left',
      renderer: ({ value, record }) => {
        return (
          <span className="action-link">
            <a onClick={() => handleOpenTab(record?.data?.eoId)}>{value}</a>
          </span>
        );
      },
    },
    {
      name: 'identification',
      width: 150,
    },
    {
      name: 'materialCode',
    },
    {
      name: 'revisionCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'statusDesc',
    },
    {
      name: 'qty',
    },
  ];

  const openModal = async (workOrderId, status) => {
    drawerDs.setQueryParameter('workOrderId', workOrderId);
    drawerDs.setQueryParameter('status', status);
    await drawerDs.query();
    let title = '';
    if (status === 'COMPLETED') {
      title = intl.get(`${modelPrompt}.modal.completionDetails`).d('完工明细');
    } else if (status === 'WORKING') {
      title = intl.get(`${modelPrompt}.modal.workingDetails`).d('在制明细');
    } else if (status === 'CLOSED') {
      title = intl.get(`${modelPrompt}.modal.scrappedDetails`).d('报废明细');
    }
    drawerModal = Modal.open({
      key: Modal.key(),
      style: {
        width: '720px',
      },
      title,
      drawer: true,
      closable: true,
      resizable: true,
      children: <Table dataSet={drawerDs} columns={drawerColumns} style={{ height: 400 }} />,
      footer: null,
    });
  };

  // const handleLevel = () => {
  //   levelDs.reset();
  //   splitModal = Modal.open({
  //     key: Modal.key(),
  //     title: intl.get(`${modelPrompt}.modal.specifyLevel`).d('指定等级'),
  //     width: 400,
  //     children: (
  //       <Form dataSet={levelDs} columns={1}>
  //         <Select name="level" />
  //       </Form>
  //     ),
  //     footer: (
  //       <div>
  //         <Button
  //           onClick={() => {
  //             cancelSplit();
  //           }}
  //         >
  //           {intl.get('tarzan.common.button.cancel').d('取消')}
  //         </Button>
  //         <Button
  //           color="primary"
  //           type="submit"
  //           loading={loading}
  //           onClick={() => {
  //             levelConfirm();
  //           }}
  //         >
  //           {intl.get('tarzan.common.button.confirm').d('确定')}
  //         </Button>
  //       </div>
  //     ),
  //   });
  // };

  // const levelConfirm = async () => {
  //   setLoading(true);
  //   splitModal.close();

  //   const res = await request(`${Host}/v1/${tenantId}/hme-work-order-new/designate/leave`, {
  //     method: 'POST',
  //     body: {
  //       workOrderIds: selectedRow.map(item => item.data.workOrderId),
  //       level: levelDs?.current?.get('level'),
  //     },
  //   });
  //   setLoading(false);
  //   const result = getResponse(res);
  //   if (result) {
  //     notification.success();
  //     tableDs.query();
  //   }
  // };
  // const handleProd = async () => {
  //   prodDs.getField('prodLineObj').setLovPara('organizationId', selectedRow[0].data.organizationId);
  //   prodDs.getField('prodLineObj').setLovPara('workOrderId', selectedRow[0].data.workOrderId);
  //   if (selectedRow[0].data.itemGroup !== 'ZB12') {
  //     notification.warning({
  //       message: `当前工单${selectedRow[0].data.workOrderNum}不是模组工单，不可指定产线，请检查！`,
  //     });
  //     return;
  //   }
  //   prodDs.reset();
  //   await Modal.open({
  //     width: 400,
  //     key: Modal.key(),
  //     title: intl.get(`${modelPrompt}.modal.designatedProductionLine`).d('指定生产线'),
  //     children: (
  //       <Form dataSet={prodDs} columns={1}>
  //         <Lov name="prodLineObj" />
  //       </Form>
  //     ),
  //     onOk: async () => {
  //       if (await prodDs.validate()) {
  //         setLoading(true);
  //         return request(`${Host}/v1/${tenantId}/hme-work-order-new/designate/prodline`, {
  //           method: 'POST',
  //           body: {
  //             workOrderId: selectedRow[0].data.workOrderId,
  //             prodLineId: prodDs.current.get('organizationId'),
  //           },
  //         }).then(res => {
  //           setLoading(false);
  //           if (res && res.success) {
  //             notification.success();
  //             tableDs.query();
  //           } else {
  //             return Promise.resolve(false);
  //           }
  //         });
  //       }
  //       return false;
  //     },
  //   });
  // };

  // const handleChangeBomAndRouter = async () => {
  //   const selectedData = selectedRow[0].data;
  //   bomAndRouterDs.current.set('siteId', selectedData.siteId);
  //   bomAndRouterDs.current.set('materialId', selectedData.materialId);
  //   bomAndRouterDs.current.set('workOrderId', selectedData.workOrderId);
  //   Modal.open({
  //     ...drawerPropsC7n({ ds: bomAndRouterDs }),
  //     drawer: false,
  //     key: Modal.key(),
  //     title: intl.get(`${modelPrompt}.modal.assemblyList`).d('装配清单/工艺路线'),
  //     okText: intl.get(`${modelPrompt}.button.confirm`).d('确定'),
  //     children: <BomAndRouterDrawer ds={bomAndRouterDs} queryRouter={queryRouter} />,
  //     onOk: async () => {
  //       if (!(await bomAndRouterDs.validate())) {
  //         return false;
  //       }
  //       const data = bomAndRouterDs.current.toData();
  //       const _saveData = {};
  //       _saveData.bomId = data.bom?.bomId;
  //       _saveData.bomName = data.bom?.bomName;
  //       _saveData.bomRevision = data.bom?.revision;
  //       _saveData.routerId = data.router.routerId;
  //       _saveData.routerName = data.router.routerName;
  //       _saveData.routerRevision = data.router.revision;
  //       _saveData.productionVersion = data.productionVersionCode;
  //       setLoading(true);
  //       return run({
  //         params: {
  //           ...selectedRow[0].data,
  //           ..._saveData,
  //           validateFlag: 'Y',
  //         },
  //       }).then(res => {
  //         setLoading(false);
  //         if (res && res.success) {
  //           notification.success();
  //           tableDs.query(tableDs.currentPage);
  //         } else {
  //           return Promise.resolve(false);
  //         }
  //       });
  //     },
  //   });
  // };

  // const queryRouter = productionVersionCode => {
  //   const { workOrderId } = selectedRow[0].data;
  //   // 获取重读使用的bom和router
  //   setLoading(true);
  //   request(
  //     `${Host}/v1/${tenantId}/hme-work-order-new/bom/router/query?workOrderId=${workOrderId ||
  //       ''}&productionVersion=${productionVersionCode}`,
  //     {
  //       method: 'GET',
  //     },
  //   ).then(response => {
  //     setLoading(false);
  //     const res = getResponse(response);
  //     if (res) {
  //       const rows = res.content[0];
  //       if (rows.bomId) {
  //         const bom = {
  //           bomId: rows.bomId,
  //           bomName: rows.bomName,
  //           revision: rows.bomRevision,
  //         };
  //         bomAndRouterDs.current.set('bom', bom);
  //       }
  //       const router = {
  //         routerId: rows.routerId,
  //         routerName: rows.routerName,
  //         revision: rows.routerRevision,
  //       };
  //       bomAndRouterDs.current.set('router', router);
  //     }
  //   });
  // };

  const handleQueryMaterialLotsHistory = () => {
    const woIds = tableDs.selected.map((ele) => ele.toData().workOrderId);
    historyDs.setQueryParameter('woIds', woIds);
    historyDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      closable: true,
      drawer: true,
      maskClosable: false,
      style: {
        width: 1080,
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      key: Modal.key(),
      title: (
        <div
          style={{
            width: 'calc(100% - 20px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}</div>
          {/* <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/export/his/ui`}
            queryParams={{
              materialLotIds: eoId,
            }}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          /> */}
        </div>
      ),
      destroyOnClose: true,
      children: <HistoryDrawer ds={historyDs} />,
    });
  };


  return (
    <React.Fragment>
      <Spin spinning={loading}>
        <Header title={intl.get(`${modelPrompt}.title`).d('工单管理平台')}>
          {/* <Button
            onClick={handleChangeBomAndRouter}
            style={{ marginRight: 15 }}
            color="primary"
            icon="cached"
            disabled={
              selectedLength !== 1 ||
              selectedRow?.some(item => item.data.status !== 'RELEASED') ||
              selectedRow?.some(item => item.data.workOrderType !== 'REPAIR')
            }
          >
            {intl.get(`${modelPrompt}.button.assemblyList`).d('装配清单/工艺路线变更')}
          </Button> */}
          {/* <Button
            onClick={handleProd}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              selectedLength !== 1 || selectedRow?.some(item => item.data.status !== 'RELEASED')
            }
          >
            {intl.get(`${modelPrompt}.button.designatedProductionLine`).d('指定生产线')}
          </Button>
          <Button
            onClick={handleLevel}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={!selectedLength || selectedRow?.some(item => item.data.status !== 'RELEASED')}
          >
            {intl.get(`${modelPrompt}.button.specifyLevel`).d('指定等级')}
          </Button> */}
          <PermissionButton
            type="c7n-pro"
            color="primary"
            permissionList={[
              {
                code: `${path}.button.productionBatch`,
                type: 'button',
                meaning: '指定工艺批次',
              },
            ]}
            loading={productionBatchLoading}
            disabled={
              selectedLength !== 1 || selectedRow?.some(item => item.data.status !== 'RELEASED' || item.data.prodLineCode === '-1')
            }
            onClick={handleProductionBatch}
          >
            {intl.get(`${modelPrompt}.button.productionBatch`).d('工艺批次')}
          </PermissionButton>
          <Button
            onClick={handleWorkOrderDispatch}
            loading={commandDispatchLoading}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              selectedLength !== 1 || selectedRow?.some(
                item =>
                  (item.data.status !== 'RELEASED' &&
                  item.data.status !== 'EORELEASED') ||
                  item.data.prodLineCode === '-1',
              )
            }
          >
            {intl.get(`${modelPrompt}.button.commandDispatch`).d('指令派工')}
          </Button>
          <Button
            onClick={handleWorkOrderSplit}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              selectedLength !== 1 ||
              selectedRow?.some(
                item =>
                  (item.data.status !== 'RELEASED' &&
                  item.data.status !== 'NEW') ||
                  item.data.prodLineCode !== '-1',
              )
            }
          >
            {intl.get(`${modelPrompt}.button.instructionSplitting`).d('指令拆分')}
          </Button>
          {/* <Button
            onClick={handleTop}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              selectedLength !== 1 ||
              selectedRow?.some(
                item => item.data.status !== 'RELEASED' && item.data.status !== 'EORELEASED',
              )
            }
          >
            {intl.get(`${modelPrompt}.button.toTop`).d('置顶')}
          </Button> */}
          {/* <Button
            onClick={handleDown}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              selectedLength !== 1 ||
              selectedRow?.some(
                item => item.data.status !== 'RELEASED' && item.data.status !== 'EORELEASED',
              )
            }
          >
            {intl.get(`${modelPrompt}.button.down`).d('下移')}
          </Button>
          <Button
            onClick={handleUp}
            style={{ marginRight: 15 }}
            color="primary"
            disabled={
              selectedLength !== 1 ||
              selectedRow?.some(
                item => item.data.status !== 'RELEASED' && item.data.status !== 'EORELEASED',
              )
            }
          >
            {intl.get(`${modelPrompt}.button.top`).d('上移')}
          </Button> */}
          <Button
            onClick={handleProductCancel}
            style={{ marginRight: 15 }}
            color="primary"
            loading={loading}
            disabled={
              selectedLength !== 1 ||
              selectedRow?.some(
                item =>
                  (item.data.status !== 'RELEASED' &&
                  item.data.status !== 'NEW') ||
                  item.data.prodLineCode !== '-1',
              )
            }
          >
            {intl.get(`${modelPrompt}.button.close`).d('关闭')}
          </Button>
          <PermissionButton
            onClick={handleCancelClose}
            style={{ marginRight: 15 }}
            color="primary"
            loading={loading}
            permissionList={[
              {
                code: `${path}.button.cancelClose`,
                type: 'button',
                meaning: '取消关闭',
              },
            ]}
            disabled={
              selectedLength !== 1 || selectedRow?.some(item => item.data.status !== 'CLOSED' || item.data.prodLineCode !== '-1')
            }
          >
            {intl.get(`${modelPrompt}.button.cancelClose`).d('取消关闭')}
          </PermissionButton>
          <Button
            onClick={handleCancelComplete}
            style={{ marginRight: 15 }}
            color="primary"
            loading={loading}
            disabled={selectedRow?.length === 0 || selectedRow?.some(item => item.data.status !== 'COMPLETED')}
          >
            {intl.get(`${modelPrompt}.button.cancelComplete`).d('取消完成')}
          </Button>
          <Button disabled={!tableDs.selected.length} onClick={handleQueryMaterialLotsHistory}>
            {intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}
          </Button>
        </Header>
        <Content>
          <Table
            searchCode="WorkOrderManagementPlatform"
            customizedCode="WorkOrderManagementPlatform"
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            queryFieldsLimit={7}
            style={{ height: 400 }}
          />
        </Content>
      </Spin>
    </React.Fragment>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.WorkOrderManagementPlatform', 'tarzan.common'],
})(WorkOrderManagementPlatform);
