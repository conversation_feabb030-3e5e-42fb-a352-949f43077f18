/**
 * @Description: 生产指令管理详情
 * @Author: <<EMAIL>>
 * @Date: 2021-07-22 09:40:22
 * @LastEditTime: 2023-05-18 14:42:04
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState, useRef } from 'react';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import notification from 'utils/notification';
import {
  DataSet,
  Form,
  Select,
  Lov,
  TextField,
  NumberField,
  DateTimePicker,
  Icon,
  Dropdown,
  Menu,
  Modal,
} from 'choerodon-ui/pro';
import { Radio, Collapse } from 'choerodon-ui';
import myInstance from '@utils/myAxios';
import formatterCollections from 'utils/intl/formatterCollections';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Button as PermissionButton } from 'components/Permission';
import { AttributeDrawer, drawerPropsC7n, C7nFormItemSort } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
// import { C7nFormItemSort } from '@/components/tarzan-ui';
import { getResponse } from '@utils/utils';
import request from 'utils/request';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import uuid from 'uuid/v4';

import {
  formDS,
  defaultBomRouterMaterialDS,
  bomAndRouterDS,
} from '../stores/ProductionOrderMgtDetailDS';

import styles from '../index.module.less';

import TotalList from './TotalList';
import DetailList from './DetailList';
import SplitWoDrawer from './SplitWoDrawer';
import MergeWoDrawer from './MergeWoDrawer';
import CreateEoDrawer from './CreateEoDrawer';
import CreateBeforehandEoDrawer from './CreateBeforehandEoDrawer';
import CreateTargetEoDrawer from './CreateTargetEoDrawer';
import BomAndRouterDrawer from './BomAndRouterDrawer';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.workshop.productionOrderMgt';

const ProductionOrderMgtDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    custConfig,
    customizeForm,
  } = props;

  // 加工实绩装配实绩组件
  const detailListRef = useRef();
  // 编辑开关
  const [canEdit, setCanEdit] = useState(false);
  // 实绩信息初始化
  const [isFirst, setIsFirst] = useState(false);
  // 基础信息&实绩信息切换
  const [pageSwitch, setPageSwitch] = useState('left');
  // 实绩信息进度数据
  const [listData, setListData] = useState({});
  // 抽屉编辑开关
  const [splitVisible, setSplitVisible] = useState(false);
  const [mergeVisible, setMergeVisible] = useState(false);
  const [eoVisible, setEoVisible] = useState(false);
  const [beforehandEoVisible, setBeforehandEoVisible] = useState(false);
  const [targetEoVisible, setTargetEoVisible] = useState(false);

  const [linkRe, setLinkRe] = useState(1);
  const [status, setStatus] = useState(undefined);

  // 记录初始装配清单/工艺路线/生产线版本/生产版本信息
  const [defaultBomRouter, setDefaultBomRouter] = useState({});
  // 根据站点、物料、生产线、生产版本自动带出来的bom
  const [autoBroughtOutBomInfo, setAutoBroughtOutBomInfo] = useState({});
  // 根据站点、物料、生产线、生产版本自动带出来的router
  const [autoBroughtOutRouterInfo, setAutoBroughtOutRouterInfo] = useState({});

  // 表单信息DS
  const formDs = useMemo(() => {
    return new DataSet(formDS());
  }, []);

  const defaultBomRouterMaterialDs = useMemo(() => {
    return new DataSet(defaultBomRouterMaterialDS());
  }, []);

  // 装配清单和工艺路线弹窗的DS
  const bomAndRouterDs = useMemo(() => {
    return new DataSet(bomAndRouterDS());
  }, []);

  const { run } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/save/ui`,
      method: 'POST',
    },
    { manual: true, needPromise: true },
  );

  const mtWorkOrderSave = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.DETAIL.BASIC`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  useEffect(() => {
    if (formDs) {
      formDs.addEventListener('update', handleQueryDataSetUpdate);
    }
    return () => {
      if (formDs) {
        formDs.removeEventListener('update', handleQueryDataSetUpdate);
      }
    };
  });

  useEffect(() => {
    if (id === 'create') {
      formDs.current.set('kid', uuid());
      setCanEdit(true);
      formDs.current.set('bomType', false);
      formDs.current.set('routerType', false);
    } else {
      // 编辑开关
      setCanEdit(false);
      setPageSwitch('left');
      setSplitVisible(false);
      setMergeVisible(false);
      queryForm(id);
    }
  }, [id]);

  const queryWorking = () => {
    if (detailListRef.current) {
      detailListRef.current.queryWorking();
    }
  };
  const queryBom = () => {
    if (detailListRef.current) {
      detailListRef.current.queryBom();
    }
  };

  const handleQueryDataSetUpdate = event => {
    setLinkRe(linkRe + 1);
    if (event && event.name === 'revisionCodeUpdate' && event.value && event.oldValue) {
      getDefaultBomRouterMaterial();
    }
  };

  // 查询上方表单,下方表格
  const queryForm = workOrderId => {
    formDs.setQueryParameter('workOrderId', workOrderId);
    formDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.DETAIL.BASIC`);
    formDs.query().then(res => {
      if (res && res.rows) {
        const {
          bomId,
          bomName,
          bomRevision,
          routerId,
          routerName,
          routerRevision,
          productionLineId,
          productionVersionCode,
          makeOrderNum,
          siteId,
          materialId,
          soNumber,
          soLineNumber,
        } = res.rows;
        setDefaultBomRouter({
          bomId,
          bomName,
          bomRevision,
          routerId,
          routerName,
          routerRevision,
          productionLineId,
          productionVersionCode,
        });
        setStatus(res.rows.status);
        if (soNumber && soLineNumber) {
          formDs.current.set('soNumberSoLineNum', `${soNumber || ''}/${soLineNumber || ''}`);
        } else {
          formDs.current.set('soNumberSoLineNum', `${soNumber || ''}${soLineNumber || ''}`);
        }
        // 将bom类型和router类型默认都当作material类型的，不满足要求，只有bom和router未发生改变时才当作wo类型的，改为true
        formDs.current.set('bomType', false);
        formDs.current.set('routerType', false);
        if (makeOrderNum) {
          formDs.current.set('productionVersionRequire', 'N');
          formDs.current.set('productionVersionDisable', 'Y');
        } else {
          const url = `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-production-version/prod-version-edit-verify/ui`;
          myInstance
            .post(url, {
              siteId,
              materialId,
            })
            .then(response => {
              if (response.data.success) {
                if (response.data.rows === 'Y') {
                  formDs.current.set('productionVersionRequire', 'Y');
                  formDs.current.set('productionVersionDisable', 'Y');
                } else if (response.data.rows === 'N') {
                  formDs.current.set('productionVersionRequire', 'N');
                  formDs.current.set('productionVersionDisable', 'N');
                }
              }
            });
        }
      }
      setLinkRe(linkRe + 1);
    });

    totalListQuery(workOrderId);
  };

  const totalListQuery = async workOrderId => {
    const response = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/actual-qty/collect/ui`,
      {
        method: 'GET',
        query: {
          workOrderId,
        },
      },
    );
    const res = getResponse(response);
    if (res && res.rows) {
      const stepWipQty =
        (res.rows.stepWipQty || 0) +
          (res.rows.stepWipScrapQty || 0) +
          (res.rows.stepWipHoldQty || 0) || 0;
      const other =
        (res.rows.unReleasedQty || 0) +
        (res.rows.unStartedQty || 0) +
        (res.rows.stepWipQty || 0) +
        (res.rows.completedQty || 0) +
        (res.rows.scrapConfirmQty || 0) -
        (res.rows.qty || 0);
      const totalListData = {
        list1: [
          {
            name: 'unReleasedQty',
            title: intl.get(`${modelPrompt}.unReleasedQty`).d('未下达数量'),
            value: (res.rows.unReleasedQty || 0).toFixed(3) - 0,
          },
          {
            name: 'unStartedQty',
            title: intl.get(`${modelPrompt}.unStartedQty`).d('未开始数量'),
            value: (res.rows.unStartedQty || 0).toFixed(3) - 0,
          },
          {
            name: 'stepWipQty',
            title: intl.get(`${modelPrompt}.stepWipQty`).d('在制数量'),
            value: stepWipQty.toFixed(3) - 0,
            max: stepWipQty.toFixed(3) - 0,
            children: [
              {
                name: 'stepWipQty',
                title: intl.get(`${modelPrompt}.stepWipQty`).d('在制数量'),
                value: (res.rows.stepWipQty || 0).toFixed(3) - 0,
              },
              {
                name: 'stepWipScrapQty',
                title: intl.get(`${modelPrompt}.stepWipScrapQty`).d('在制报废数量'),
                value: (res.rows.stepWipScrapQty || 0).toFixed(3) - 0,
              },
              // {
              //   name: 'stepWipHoldQty',
              //   title: intl.get(`${modelPrompt}.stepWipHoldQty`).d('在制保留数量'),
              //   value: (res.rows.stepWipHoldQty || 0).toFixed(3) - 0,
              // },
            ],
          },
          {
            name: 'completedQty',
            title: intl.get(`${modelPrompt}.completedQty`).d('作业已完工数量'),
            value: (res.rows.completedQty || 0).toFixed(3) - 0,
          },
          {
            name: 'scrapConfirmQty',
            title: intl.get(`${modelPrompt}.scrapConfirmQty`).d('报废确认数量'),
            value: (res.rows.scrapConfirmQty || 0).toFixed(3) - 0,
          },
        ],
        list2: [
          {
            name: 'completedQty',
            title: intl.get(`${modelPrompt}.completedQty`).d('完成数量'),
            value: (res.rows.completedQty || 0).toFixed(3) - 0,
          },
          {
            name: 'kitQty',
            title: intl.get(`${modelPrompt}.kitQty`).d('齐套数量'),
            value: (res.rows.kitQty || 0).toFixed(3) - 0,
          },
        ],
        max: res.rows.qty,
        other: other.toFixed(3) - 0,
      };
      setListData(totalListData);
    } else {
      setListData({});
    }
  };

  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hmes/workshop/production-order-mgt/list');
    } else {
      setCanEdit(false);
      formDs.reset();
      queryForm(id);
    }
  };

  const revisionLinkBom = () => {
    const _id = formDs.current.get('bomId');
    if (formDs.current.get('bomType') !== 'EO' && id === 'create') {
      return false;
    }
    if (_id > 0) {
      props.history.push(`/hmes/product/manufacture-list/dist/${_id}`);
    }
  };

  const revisionLinkRouter = () => {
    const _id = formDs.current.get('routerId');
    if (formDs.current.get('routerType') !== 'EO' && id === 'create') {
      return false;
    }
    if (_id > 0) {
      props.history.push(`/hmes/new/manufacture-process/routes-c7n/dist/${_id}`);
    }
  };

  const siteChange = () => {
    formDs.current.init('material', undefined);
    formDs.current.init('productionLine', undefined);
    formDs.current.init('productionVersion', undefined);
    formDs.current.init('bom', undefined);
    formDs.current.init('router', undefined);
    formDs.current.set('productionVersionRequire', 'N');
    formDs.current.set('productionVersionDisable', 'N');
    formDs.current.init('soNumberObj', undefined);
  };

  const searchDefaultProductionVersion = (detail = {}) => {
    const { siteId, materialId, productionLineId, revisionCode } = formDs.current.toData();

    if (siteId && materialId && productionLineId) {
      const url = `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-production-version/material-limit-prod-version-get/ui`;
      myInstance
        .post(url, {
          siteId,
          materialId,
          organizationId: productionLineId,
          revisionCode,
        })
        .then(res => {
          if (res.data.success) {
            const { productionVersionId, productionVersionCode } = res.data.rows;
            let _detail = detail;
            if (productionVersionId) {
              _detail = res.data.rows;
            }
            const { bomName, bomId, bomRevision, routerId, routerName, routerRevision } = _detail;
            if (bomName || bomId) {
              setAutoBroughtOutBomInfo({
                bomName,
                bomId,
                revision: bomRevision,
              });
            } else {
              setAutoBroughtOutBomInfo({});
            }
            if (routerId || routerName) {
              setAutoBroughtOutRouterInfo({
                routerId,
                routerName,
                revision: routerRevision,
              });
            } else {
              setAutoBroughtOutRouterInfo({});
            }
            formDs.current.set('productionVersionCode', productionVersionCode);
            formDs.current.set('productionVersionId', productionVersionId);
            if (id === 'create') {
              formDs.current.set('bom', {
                bomId,
                bomName,
                revision: bomRevision,
              });
              formDs.current.set('router', {
                routerId,
                routerName,
                revision: routerRevision,
              });
            }
            if (
              id !== 'create' &&
              productionLineId === defaultBomRouter.productionLineId &&
              productionVersionCode === defaultBomRouter.productionVersionCode
            ) {
              formDs.current.set('bom', {
                bomId: defaultBomRouter.bomId,
                bomName: defaultBomRouter.bomName,
                revision: defaultBomRouter.bomRevision,
              });
              formDs.current.set('router', {
                routerId: defaultBomRouter.routerId,
                routerName: defaultBomRouter.routerName,
                revision: defaultBomRouter.routerRevision,
              });
            }
          }
        });
    }
  };

  const soNumberObjChange = val => {
    if (val) {
      formDs.current.set('soNumberSoLineNum', `${val.soNumber}/${val.soLineNum}`);
    } else {
      formDs.current.set('soNumberSoLineNum', null);
    }
  };

  const materialChange = val => {
    if (val) {
      // 物料编码切换时需要查询生产版本是否可编辑
      const url = `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-production-version/prod-version-edit-verify/ui`;
      myInstance
        .post(url, {
          siteId: formDs.current.get('siteId'),
          materialId: formDs.current.get('materialId'),
        })
        .then(res => {
          if (res.data.success) {
            if (res.data.rows === 'Y') {
              formDs.current.set('productionVersionRequire', 'Y');
              formDs.current.set('productionVersionDisable', 'Y');
            } else if (res.data.rows === 'N') {
              formDs.current.set('productionVersionRequire', 'N');
              formDs.current.set('productionVersionDisable', 'N');
            }
          }
        });

      formDs.current.init('revisionCode', val.currentRevisionCode);
      formDs.current.init('bom', undefined);
      formDs.current.init('router', undefined);
      formDs.current.set('kid', uuid());
    } else {
      // 清空生产版本装配清单
      formDs.current.set('productionVersionRequire', 'N');
      formDs.current.set('productionVersionDisable', 'N');
      formDs.current.init('productionVersion', undefined);
      formDs.current.init('bom', undefined);
      formDs.current.init('router', undefined);
    }
  };

  const productionLineChange = () => {
    formDs.current.init('productionVersion', undefined);
    formDs.current.init('bom', undefined);
    formDs.current.init('router', undefined);
    getDefaultBomRouterMaterial();
  };

  const completeControlChange = () => {
    if (formDs.current.get('completeControlQty')) {
      formDs.current.init('completeControlQty', undefined);
    }
  };

  const getDefaultBomRouterMaterial = () => {
    const { siteId, materialId, productionLineId, revisionCode } = formDs.current.toData();

    if (siteId && materialId && productionLineId) {
      defaultBomRouterMaterialDs.queryParameter = {
        materialId,
        prodLineId: productionLineId,
        siteId,
        revisionCode: revisionCode || '',
      };
      defaultBomRouterMaterialDs.query().then(res => {
        if (res && res.rows) {
          const {
            completeControlQty,
            completeControlType,
            completionLocatorCode,
            completionLocatorId,
            completionLocatorName,
          } = res.rows;
          formDs.current.set('completeControlType', completeControlType);
          formDs.current.set('completeControlQty', completeControlQty);
          formDs.current.set('locator', {
            locatorId: completionLocatorId,
            locatorCode: completionLocatorCode,
            locatorName: completionLocatorName,
          });
          searchDefaultProductionVersion(res.rows);
        } else {
          searchDefaultProductionVersion({});
        }
      });
    }
  };

  // 确认保存弹窗
  const showConfirmModal = async () => {
    const _saveData = {};
    _saveData.bomId = autoBroughtOutBomInfo.bomId;
    _saveData.bomName = autoBroughtOutBomInfo.bomName;
    _saveData.bomRevision = autoBroughtOutBomInfo.revision;
    _saveData.routerId = autoBroughtOutRouterInfo.routerId;
    _saveData.routerName = autoBroughtOutRouterInfo.routerName;
    _saveData.routerRevision = autoBroughtOutRouterInfo.revision;
    await run({
      params: {
        ...formDs.current.toData(),
        ..._saveData,
      },
    }).then(res => {
      if (res && res.success) {
        setCanEdit(false);
        notification.success();
        if (id === 'create') {
          props.history.push(`/hmes/workshop/production-order-mgt/detail/${res.rows}`);
        } else {
          queryForm(res.rows);
        }
      }
    });
  };

  const handleChangeBomAndRouter = async () => {
    const {
      siteId,
      materialId,
      productionLineId,
      revisionCode,
      productionVersionCode,
    } = formDs.current.toData();
    const queryParams = {
      siteId,
      materialId,
      revisionCode,
      productionVersionCode,
    };
    bomAndRouterDs.create({
      ...queryParams,
    });
    // 获取重读使用的bom和router
    const url = `${
      BASIC.HMES_BASIC
    }/v1/${tenantId}/mt-work-order/default/bom/router?materialId=${materialId ||
      ''}&prodLineId=${productionLineId || ''}&siteId=${siteId || ''}&revisionCode=${revisionCode ||
      ''}`;
    let querySuccess = false;
    await myInstance.get(url).then(response => {
      const { rows, success } = getResponse(response.data);
      if (success) {
        querySuccess = true;
        const productionVersion = {
          productionVersionId: rows.productionVersionId,
          productionVersionCode: rows.productionVersionCode,
          productionVersionDesc: rows.productionVersionDesc,
        };
        const bom = {
          bomId: rows.bomId,
          bomName: rows.bomName,
          revision: rows.bomRevision,
        };
        const router = {
          routerId: rows.routerId,
          routerName: rows.routerName,
          revision: rows.routerRevision,
        };
        bomAndRouterDs.current.set('designProductionVersion', productionVersion);
        bomAndRouterDs.current.set('designBom', bom);
        bomAndRouterDs.current.set('designRouter', router);
      }
    });
    if (!querySuccess) {
      return;
    }
    if (formDs.current.get('productionVersionDisable') === 'Y') {
      // 有生产版本————感觉之前设置的disable不对，此时应该为N才对
      bomAndRouterDs.current.set('ownProductionVersion', true);
    }
    await Modal.open({
      ...drawerPropsC7n({ ds: bomAndRouterDs }),
      drawer: false,
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.bomAndRouter`).d('装配清单/工艺路线'),
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      children: <BomAndRouterDrawer ds={bomAndRouterDs} />,
      onOk: async () => {
        if (!(await bomAndRouterDs.validate())) {
          return false;
        }
        const data = bomAndRouterDs.current.toData();
        const _saveData = {};
        if (data.selectType === 'designChange') {
          // 设计变更
          if (data.ownProductionVersion) {
            // formDs.current.set('productionVersion', data.designProductionVersion);
            _saveData.productionVersionId = data.designProductionVersion.productionVersionId;
            _saveData.productionVersionCode = data.designProductionVersion.productionVersionCode;
          }
          // formDs.current.set('bom', data.designBom);
          // formDs.current.set('router', data.designRouter);
          _saveData.bomId = data.designBom.bomId;
          _saveData.bomName = data.designBom.bomName;
          _saveData.bomRevision = data.designBom.revision;
          _saveData.routerId = data.designRouter.routerId;
          _saveData.routerName = data.designRouter.routerName;
          _saveData.routerRevision = data.designRouter.revision;
        } else {
          // 物料
          if (data.ownProductionVersion) {
            // formDs.current.set('productionVersion', data.productionVersion);
            _saveData.productionVersionId = data.productionVersion.productionVersionId;
            _saveData.productionVersionCode = data.productionVersion.productionVersionCode;
          }
          // formDs.current.set('bom', data.bom);
          // formDs.current.set('router', data.router);
          _saveData.bomId = data.bom.bomId;
          _saveData.bomName = data.bom.bomName;
          _saveData.bomRevision = data.bom.revision;
          _saveData.routerId = data.router.routerId;
          _saveData.routerName = data.router.routerName;
          _saveData.routerRevision = data.router.revision;
        }
        return run({
          params: {
            ...formDs.current.toData(),
            ..._saveData,
          },
        }).then(res => {
          if (res && res.success) {
            notification.success();
            if (id === 'create') {
              props.history.push(`/hmes/workshop/production-order-mgt/detail/${res.rows}`);
            } else {
              queryForm(res.rows);
            }
          } else {
            return Promise.resolve(false);
          }
        });
      },
    });
  };

  const handleSave = async () => {
    formDs.current.set({ nowDate: new Date().getTime() });
    const validate = await formDs.validate();
    if (!validate) {
      return;
    }
    if (id === 'create' && (autoBroughtOutRouterInfo.routerName || autoBroughtOutBomInfo.bomName)) {
      // 新建的生产指令，有bom或者router，弹确认框
      await showConfirmModal();
      return;
    }
    if (
      id !== 'create' &&
      (defaultBomRouter.productionLineId !== formDs.current.get('productionLineId') ||
        defaultBomRouter.productionVersionCode !== formDs.current.get('productionVersionCode')) &&
      (autoBroughtOutRouterInfo.routerName || autoBroughtOutBomInfo.bomName)
    ) {
      // 编辑生产指令时，修改过生产线或者生产版本，弹确认框
      await showConfirmModal();
      return;
    }
    // bom和router没有发生改变时的保存,bomType和routerType为true，意味着保存的是WO类型的bom和router
    formDs.current.set('bomType', true);
    formDs.current.set('routerType', true);

    const { completeControlType, ...data } = formDs.current.toData();
    if (completeControlType === null) {
      data.completeControlType = '';
    } else {
      data.completeControlType = completeControlType;
    }
    if (data.bomId === null) {
      data.bomId = 0;
    }
    if (data.routerId === null) {
      data.routerId = 0;
    }
    delete data.productionVersion;
    delete data.productionVersionDisable;
    delete data.productionVersionRequire;

    return mtWorkOrderSave
      .run({
        params: data,
      })
      .then(res => {
        if (res?.success) {
          notification.success({});
          setCanEdit(false);
          const newId = res?.rows;
          if (id === 'create') {
            props.history.push(`/hmes/workshop/production-order-mgt/detail/${newId}`);
          } else {
            queryForm(newId);
          }
        }
      });
  };

  const handlePageChange = e => {
    if (!isFirst) {
      setIsFirst(true);
    }
    setPageSwitch(e.target.value);
  };

  const clickSplitMergeMenu = ({ key }) => {
    switch (key) {
      case 'WOSPLIT':
        setSplitVisible(true);
        break;
      case 'WOMERGE':
        setMergeVisible(true);
        break;
      default:
        break;
    }
  };

  const clickEoDrawer = () => {
    setEoVisible(true);
  };

  const clickBeforehandEoDrawer = () => {
    setBeforehandEoVisible(true);
  };

  const clickTargetEoDrawer = () => {
    setTargetEoVisible(true);
  };

  const drawerCancel = () => {
    setSplitVisible(false);
    setMergeVisible(false);
    setEoVisible(false);
    setBeforehandEoVisible(false);
    setTargetEoVisible(false);
  };
  const modalRefresh = type => {
    queryForm(id);
    switch (type) {
      case 'WOSPLIT':
        queryWorking();
        break;
      case 'WOMERGE':
        queryBom();
        break;
      default:
        break;
    }
  };

  const getRevisionColor = (name, type) => {
    if (linkRe) {
      if (formDs.current.get(type) !== 'EO' && id === 'create') {
        return `rgba(0,0,0,0.25)`;
      }
      if (formDs.current.get(name) > 0) {
        return '#29bece';
      }
      return `rgba(0,0,0,0.25)`;
    }
  };

  const clickMenu = async ({ key }) => {
    const response = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-work-order/status/update/ui`,
      {
        method: 'POST',
        body: {
          workOrderId: id === 'create' ? null : id,
          operationType: key,
        },
      },
    );
    const res = getResponse(response);
    if (res) {
      notification.success();
      queryForm(id);
    }
  };

  const handleChangeRevisionCode = () => {
    formDs.current.set('productionVersion', null);
    formDs.current.set('bom', {});
    formDs.current.set('router', {});
    setAutoBroughtOutBomInfo({});
    setAutoBroughtOutRouterInfo({});
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get(`${modelPrompt}.model.productionOrderMgt.productionOrderMgt`)
          .d('生产指令管理')}
        backPath="/hmes/workshop/production-order-mgt/list"
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <PermissionButton type="c7n-pro" icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </PermissionButton>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            disabled={['ABANDON', 'CLOSE'].indexOf(status) > -1 || !status}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <PermissionButton
          type="c7n-pro"
          icon="insert_drive_file-o"
          onClick={clickEoDrawer}
          disabled={['EORELEASED', 'RELEASED'].indexOf(status) === -1 || !status}
          permissionList={[
            {
              code: `${path}.button.eoCreate`,
              type: 'button',
              meaning: '详情页-EO创建按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.model.productionOrderMgt.eoCreate`).d('EO创建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="insert_drive_file-o"
          onClick={clickBeforehandEoDrawer}
          disabled={['EORELEASED', 'RELEASED'].indexOf(status) === -1 || !status}
          permissionList={[
            {
              code: `${path}.button.eoCreate`,
              type: 'button',
              meaning: '详情页-EO创建按钮',
            },
          ]}
        >
          {intl
            .get(`${modelPrompt}.model.productionOrderMgt.eoBeforehandCreate`)
            .d('预装物料执行作业创建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="insert_drive_file-o"
          onClick={clickTargetEoDrawer}
          disabled={['EORELEASED', 'RELEASED'].indexOf(status) === -1 || !status}
          permissionList={[
            {
              code: `${path}.button.eoCreate`,
              type: 'button',
              meaning: '详情页-EO创建按钮',
            },
          ]}
        >
          {intl
            .get(`${modelPrompt}.model.productionOrderMgt.eoTargetCreate`)
            .d('指定物料执行作业创建')}
        </PermissionButton>
        <Dropdown
          overlay={
            <Menu onClick={clickMenu} className={styles['split-menu']} style={{ width: '100px' }}>
              {status === 'NEW' && (
                <Menu.Item key="RELEASE">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.model.productionOrderMgt.release`).d('下达')}
                  </a>
                </Menu.Item>
              )}
              {status === 'NEW' && (
                <Menu.Item key="ABANDON">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.model.productionOrderMgt.abandon`).d('废弃')}
                  </a>
                </Menu.Item>
              )}
              {(status === 'RELEASED' || status === 'EORELEASED') && (
                <Menu.Item key="COMPLETE">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.model.productionOrderMgt.complete`).d('完成')}
                  </a>
                </Menu.Item>
              )}
              {(status === 'RELEASED' || status === 'EORELEASED' || status === 'COMPLETED') && (
                <Menu.Item key="CLOSE">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.model.productionOrderMgt.close`).d('关闭')}
                  </a>
                </Menu.Item>
              )}
              {/* {(status === 'RELEASED' || status === 'EORELEASED' || status === 'COMPLETED') && (
                <Menu.Item key="HOLD">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.model.productionOrderMgt.hold`).d('保留')}
                  </a>
                </Menu.Item>
              )} */}
              {status === 'COMPLETED' && (
                <Menu.Item key="COMPLETE_CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl
                      .get(`${modelPrompt}.model.productionOrderMgt.complete_cancel`)
                      .d('取消完成')}
                  </a>
                </Menu.Item>
              )}
              {status === 'CLOSED' && (
                <Menu.Item key="CLOSE_CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.model.productionOrderMgt.close_cancel`).d('取消关闭')}
                  </a>
                </Menu.Item>
              )}
              {/* {status === 'HOLD' && (
                <Menu.Item key="HOLD_CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.model.productionOrderMgt.hold_cancel`).d('取消保留')}
                  </a>
                </Menu.Item>
              )} */}
            </Menu>
          }
          trigger={['click']}
          disabled={id === 'create' || canEdit}
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={id === 'create' || canEdit || status === 'ABANDON' || !status}
            permissionList={[
              {
                code: `${path}.button.statusChange`,
                type: 'button',
                meaning: '详情页-状态变更按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.model.productionOrderMgt.statusChange`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        <Dropdown
          overlay={
            <Menu
              onClick={clickSplitMergeMenu}
              className={styles['split-menu']}
              style={{ width: '100px' }}
            >
              <Menu.Item key="WOSPLIT">
                <a>{intl.get(`${modelPrompt}.model.productionOrderMgt.splitWo`).d('WO拆分')}</a>
              </Menu.Item>
              <Menu.Item key="WOMERGE">
                <a>{intl.get(`${modelPrompt}.model.productionOrderMgt.mergeWo`).d('WO合并')}</a>
              </Menu.Item>
            </Menu>
          }
          trigger={['click']}
          disabled={id === 'create' || canEdit}
        >
          <PermissionButton
            type="c7n-pro"
            icon="keyboard_tab"
            disabled={
              id === 'create' ||
              canEdit ||
              // ['NEW', 'RELEASED', 'EORELEASED', 'HOLD'].indexOf(status) === -1 ||
              !status
            }
            permissionList={[
              {
                code: `${path}.button.splitMerge`,
                type: 'button',
                meaning: '详情页-拆分合并按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.model.productionOrderMgt.splitMerge`).d('拆分合并')}
          </PermissionButton>
        </Dropdown>
        <PermissionButton
          type="c7n-pro"
          icon="cached"
          disabled={
            id === 'create' ||
            canEdit ||
            ['NEW', 'RELEASED', 'EORELEASED', 'HOLD'].indexOf(status) === -1 ||
            !status
          }
          onClick={handleChangeBomAndRouter}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.changeBomAndRouter`).d('修改装配清单/工艺路线')}
        </PermissionButton>
        <AttributeDrawer
          serverCode={BASIC.HMES_BASIC}
          disabled={id === 'create'}
          className="org.tarzan.mes.domain.entity.MtWorkOrder"
          kid={id}
          canEdit={canEdit}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.DETAIL.ATTR`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        {!canEdit && (
          <div>
            <Radio.Group onChange={handlePageChange} value={pageSwitch} style={{ marginBottom: 8 }}>
              <Radio.Button value="left">
                {intl.get(`${modelPrompt}.basicInfo`).d('基础信息')}
              </Radio.Button>
              <Radio.Button value="right">
                {intl.get(`${modelPrompt}.achieveInfo`).d('实绩信息')}
              </Radio.Button>
            </Radio.Group>
          </div>
        )}
        <div style={{ display: pageSwitch === 'left' || canEdit ? 'block' : 'none' }}>
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location', 'productionAttr']}>
            <Panel
              header={intl
                .get(`${modelPrompt}.model.productionOrderMgt.basicAttributes`)
                .d('基本属性')}
              key="basicInfo"
              dataSet={formDs}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.DETAIL.BASIC`,
                },
                <Form
                  disabled={!canEdit}
                  dataSet={formDs}
                  columns={3}
                  labelLayout="horizontal"
                  labelWidth={110}
                >
                  <TextField name="workOrderNum" />
                  <Lov name="site" onChange={siteChange} />
                  <TextField name="siteName" />
                  <Select name="workOrderType" />
                  <Select name="status" />
                  <TextField name="remark" />
                </Form>,
              )}
            </Panel>
            <Panel
              header={intl
                .get(`${modelPrompt}.model.productionOrderMgt.requirementAttr`)
                .d('需求属性')}
              key="location"
              dataSet={formDs}
            >
              <Form
                disabled={!canEdit}
                dataSet={formDs}
                columns={3}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <C7nFormItemSort name="material" itemWidth={['70%', '30%']}>
                  <Lov name="material" onChange={materialChange} />
                  <Select
                    name="revisionCode"
                    onChange={handleChangeRevisionCode}
                    dropdownMatchSelectWidth={false}
                    dropdownMenuStyle={{ width: '200px' }}
                  />
                </C7nFormItemSort>
                <TextField name="materialName" />
                <NumberField name="qty" />
                <TextField name="uomCode" />
                <TextField name="uomName" />
                <Lov name="customer" />
                <TextField name="customerName" />
                <TextField name="makeOrderNum" />
                <Lov name="soNumberObj" onChange={soNumberObjChange} />
              </Form>
            </Panel>
            <Panel
              header={intl
                .get(`${modelPrompt}.model.productionOrderMgt.productionAttr`)
                .d('生产属性')}
              key="productionAttr"
              dataSet={formDs}
            >
              <Form
                disabled={!canEdit}
                dataSet={formDs}
                columns={3}
                labelLayout="horizontal"
                labelWidth={110}
              >
                <DateTimePicker name="planStartTime" />
                <DateTimePicker name="planEndTime" />
                <Lov name="locator" />
                <Lov name="productionLine" onChange={productionLineChange} />
                <TextField name="productionLineName" />
                <TextField name="locatorName" />
                <TextField name="productionVersionCode" />
                <C7nFormItemSort name="bom" itemWidth={['68%', '17%', '15%']}>
                  <TextField name="bomName" />
                  <TextField name="bomRevision" />
                  <Icon
                    type="link2"
                    itemType="link"
                    onClick={revisionLinkBom}
                    style={{ color: getRevisionColor('bomId', 'bomType') }}
                    iconDisabled
                  />
                </C7nFormItemSort>
                <C7nFormItemSort name="router" itemWidth={['68%', '17%', '15%']}>
                  <TextField name="routerName" />
                  <TextField name="routerRevision" />
                  <Icon
                    type="link2"
                    itemType="link"
                    onClick={revisionLinkRouter}
                    style={{ color: getRevisionColor('routerId', 'routerType') }}
                    iconDisabled
                  />
                </C7nFormItemSort>
                {/* <Select name="completeControlType" onChange={completeControlChange} />
                <NumberField name="completeControlQty" /> */}
                <br />
              </Form>
            </Panel>
          </Collapse>
        </div>
        {isFirst && (
          <div style={{ display: pageSwitch === 'right' && !canEdit ? 'block' : 'none' }}>
            <TotalList listData={listData} />
            <DetailList ref={detailListRef} id={id} />
          </div>
        )}
      </Content>
      <SplitWoDrawer
        visible={splitVisible}
        handleRefresh={modalRefresh}
        handleCancel={drawerCancel}
        workOrderId={id}
        history={props.history}
        status={status}
      />
      <MergeWoDrawer
        visible={mergeVisible}
        handleRefresh={modalRefresh}
        handleCancel={drawerCancel}
        workOrderId={id}
        history={props.history}
        status={status}
      />
      <CreateEoDrawer
        visible={eoVisible}
        handleRefresh={modalRefresh}
        handleCancel={drawerCancel}
        workOrderId={id}
        history={props.history}
      />
      <CreateBeforehandEoDrawer
        visible={beforehandEoVisible}
        handleRefresh={modalRefresh}
        handleCancel={drawerCancel}
        workOrderId={id}
        basicFormDs={formDs}
        history={props.history}
      />
      <CreateTargetEoDrawer
        visible={targetEoVisible}
        handleRefresh={modalRefresh}
        handleCancel={drawerCancel}
        workOrderId={id}
        basicFormDs={formDs}
        history={props.history}
      />
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.workshop.productionOrderMgt', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.DETAIL.ATTR`, `${BASIC.CUSZ_CODE_BEFORE}.WORK_ORDER.DETAIL.BASIC`],
  })(ProductionOrderMgtDetail),
);
