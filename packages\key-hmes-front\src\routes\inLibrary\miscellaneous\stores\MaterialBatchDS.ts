/* eslint-disable no-restricted-properties */
import intl from 'utils/intl';
import { FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { isUndefined, isNull } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.purchase.delivery';
const tenantId = getCurrentOrganizationId();

const headDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  selection: false,
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/miscellaneous/material-lot/create/info/ui`,
        method: 'GET',
      };
    },
  },
  events: {
    update: ({ record, name, value })  => {
      if(name === 'materialLotQty'){
        // orderedQuantity
        if(value&&value!==0){
          record.set('materialSheets', Math.floor(record.get('orderedQuantity')/value))
        }else{
          record.set('materialSheets', null)
        }
      }
    },
  },
  fields: [
    {
      name: 'deliverNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.deliverNumber`).d('送货单号/行号'),
    },
    {
      name: 'deliverLineNumber',
      type: FieldType.number,
    },
    {
      name: 'materialLotQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.form.materialLotQty`).d('数量'),
      required: true,
      dynamicProps: {
        min: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
        precision: ({ record }) => {
          return record?.get('decimalNumber');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
      },
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
    },
    {
      name: 'materialSheets',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.form.materialSheets`).d('张数'),
      required: true,
      step: 1,
      min: 1,
      validator: (...args: Array<any>) => {
        const {
          data: { materialSheets, materialLotQty = 0, orderedQuantity, createdQuantity },
        } = args[2];
        const inputQty = isUndefined(materialLotQty) ? 0 : materialLotQty * materialSheets;
        const demandQty = orderedQuantity - createdQuantity;
        if (isUndefined(materialLotQty) || isNull(materialLotQty)) {
          return true;
        } if (inputQty > demandQty) {
          return intl
            .get(`${modelPrompt}.quantity.verification`)
            .d('输入的张数*数量必须小于等于需求数量-已创建数量!');
        }
      },
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
      // required: true,
    },
    {
      name: 'productionDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
      required: true,
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.lot`).d('批次'),
      disabled: true,
    },
    {
      name: 'description',
      type: FieldType.string,
    },
    {
      name: 'productionBatch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.productionBatch`).d('工艺批次'),
      dynamicProps: {
        required: ({record}) => record?.get('description') === 'CUSTOMER',
      },
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.materialCode`).d('物料'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.materialLotStatus`).d('物料批状态'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.uomCode`).d('单位'),
    },
    {
      name: 'orderedQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.form.orderedQuantity`).d('需求数量'),
    },
    {
      name: 'createdQuantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.form.createdQuantity`).d('已创建数量'),
    },

  ],
});

const tableDS = (): DataSetProps => {
  return {
    autoQuery: false,
    autoCreate: false,
    pageSize: 10,
    selection: 'multiple' as DataSetSelection,
    cacheSelection: true,
    autoLocateFirst: false,
    forceValidate: true,
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-delivery-doc/non-execution/material-lot/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST_MATERIAL_LOT.QUERY`,
          method: 'GET',
        };
      },
    },
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    primaryKey: 'materialLotId',
    fields: [
      {
        name: 'identification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.identification`).d('物料批标识'),
      },
      {
        name: 'primaryUomQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.primaryUomQty`).d('数量'),
      },
      {
        name: 'primaryUomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.primaryUomCode`).d('单位'),
      },
      {
        name: 'materialLotStatus',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.materialLotStatus`).d('物料批状态'),
      },
      /* {
        name: 'qualityInspection',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.qualityInspection`).d('是否质检'),
        lookupCode: 'MT.YES_NO',
        lovPara: { tenantId },
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'N',
      },
      {
        name: 'receivingWarehouse',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.receivingWarehouse`).d('接收仓库'),
      }, */
      {
        name: 'supplierLot',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
      },
      {
        name: 'productionDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
      },
      {
        name: 'lot',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.lot`).d('批次'),
      },
      {
        name: 'printTimes',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.printTimes`).d('打印次数'),
      },
      {
        name: 'createdByName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.createdByName`).d('操作人'),
      },
      {
        name: 'creationDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.table.creationDate`).d('操作时间'),
      },
    ],
  };
};

export { headDS, tableDS };
