/* eslint-disable jsx-a11y/alt-text */
// 加工件（工单）
import React, { useState, useEffect, useMemo } from 'react';
import {
  Form,
  Button,
  DataSet,
  Output,
  NumberField,
  Row,
  Col,
  Table,
  Modal,
  TextField,
  Select,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import { connect } from 'dva';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TemplatePrintButton } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import { namespace } from '../../model';
import { detailDS, formDS } from './stores/ProcessingCompletedDS';
import { CardLayout } from '../commonComponents';
import styles from './index.modules.less';
import {
  ManualReport,
  StartTime,
  EndTime,
  QueryInfoAuto,
  Complete,
  QueryMaterialLot,
  ReSave,
  CompleteCancel,
} from './services';

const modelPrompt = 'tarzan.hmes.operationPlatform.ProcessingCompleted';
// 末 自动
const WorkorderMachinedPart = observer(props => {
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );
  const formDs = useMemo(
    () =>
      new DataSet({
        ...formDS(),
      }),
    [],
  );

  const [cacheEoId, setCacheEoId] = useState(null); // 工单数据
  const [lastStepFlag, setLastStepFlag] = useState(false); // 工序
  const [operationMethod, setOperationMethod] = useState(''); // 报工方式
  const [reInputFlag, setReInputFlag] = useState(false); // 是否有重新录入数据的权限
  const [title, setTitle] = useState('');
  const [showBarCode, setShowBarCode] = useState(false); // 展示条码
  const [singleExecution, setSingleExecution] = useState(null); // 展示条码
  const [customFlag, setCustomFlag] = useState(null); // 自定义完工标识

  const { run: manualReport, loading: manualReportLoading } = useRequest(ManualReport(), {
    manual: true,
    needPromise: true,
  });
  const { run: startTime, loading: startTimeLoading } = useRequest(StartTime(), {
    manual: true,
    needPromise: true,
  });
  const { run: endTime, loading: endTimeLoading } = useRequest(EndTime(), {
    manual: true,
    needPromise: true,
  });
  const { run: reSave, loading: reSaveLoading } = useRequest(ReSave(), {
    manual: true,
    needPromise: true,
  });
  const { run: queryInfoAuto, loading: queryInfoAutoLoading } = useRequest(QueryInfoAuto(), {
    manual: true,
    needPromise: true,
  });
  const { run: complete, loading: completeLoading } = useRequest(Complete(), {
    manual: true,
    needPromise: true,
    showNotification: false,
  });

  const { run: queryMaterialLot, loading: queryMaterialLotLoading } = useRequest(
    QueryMaterialLot(),
    { manual: true, needPromise: true },
  );
  const { run: completeCancel, loading: completeCancelLoading } = useRequest(
    CompleteCancel(),
    { manual: true, needPromise: true },
  );

  useEffect(() => {
    if (props.eoData?.eo?.eoId && cacheEoId !== props.eoData?.eo?.eoId) {
      setCacheEoId(props.eoData?.eo?.eoId);
    }
    if (!props.eoData?.eo?.eoId) {
      formDs.reset();
      detailDs.loadData([]);
      setTitle('');
      setOperationMethod('');
      setReInputFlag(false);
      setShowBarCode(false);
    } else {
      setLastStepFlag(props.eoData?.lastStepFlag);
      const method = props.loginWkcInfo.operationDTOList[0]?.operationMethod;
      if (!props.eoData?.lastStepFlag && method === 'MANUAL') {
        setTitle(intl.get(`${modelPrompt}.title.report`).d('工序报工'));
      } else if (!props.eoData?.lastStepFlag && method === 'AUTO') {
        setTitle(intl.get(`${modelPrompt}.title.autoReport`).d('计时报工'));
        // 查询
        handleQueryInfoAuto();
      } else if (props.eoData?.lastStepFlag) {
        // 如果是末道序 不区分手动和自动
        // 在这里判断是否需要展示工艺批次
        const productionBatchFlag = props.eoData?.productionBatchFlag
        // const materialChangeFlag =  props?.loginWkcInfo?.materialChangeFlag
        formDs.current?.set('productionBatchFlag', productionBatchFlag);
        if(props.eoData?.productionBatch){
          formDs.current?.set('productionBatch', props.eoData?.productionBatch);
        }
        formDs.current.getField('productionBatch').set('required', productionBatchFlag !== 'N')
        // formDs.current.getField('changeMaterialName').set('required', materialChangeFlag === 'Y')

        setTitle(intl.get(`${modelPrompt}.title.complete`).d('加工完成'));
        formDs.current?.set('unitQty', props.loginWkcInfo?.operationDTOList[0]?.unitQty);
      }
      const newSingleExecution = props.loginWkcInfo?.operationDTOList[0]?.singleExecution;
      const newCustomFlag = props.loginWkcInfo?.operationDTOList[0]?.customFlag;
      setSingleExecution(newSingleExecution);
      setCustomFlag(newCustomFlag);
      if (newCustomFlag === 'Y') {
        formDs.current?.init('unitQty', null);
        formDs.current?.init('materialLotNum', 1);
        formDs.current?.getField('unitQty')?.set('required', true);
      } else if (newSingleExecution === 'Y') {
        formDs.current?.init('materialLotNum', 1);
      } else {
        formDs.current?.init('materialLotNum', null);
      }
    }
  }, [props.eoData?.eo?.eoId, props.eoData?.eoStepWipId]);

  useEffect(() => {
    setOperationMethod(props.loginWkcInfo.operationDTOList[0]?.operationMethod);
    setReInputFlag(props.loginWkcInfo.saveFlag);
  }, [props.loginWkcInfo]);

  const handleQueryInfoAuto = async () => {
    const res = await queryInfoAuto({
      params: {
        ...props.eoData,
      },
    });
    if (res && res.failed) {
      handleRecord(res.message, 'ERROR');
    } else {
      handleRecord('查询成功', 'SUCCESS');
      // 非末工序
      if (!props.eoData?.lastStepFlag) {
        let reportingTotal = null;
        if (Number(res.hmeEoAutoComplete?.startQty) && Number(res.hmeEoAutoComplete?.endQty)) {
          reportingTotal =
            Number(res.hmeEoAutoComplete?.endQty) - Number(res.hmeEoAutoComplete?.startQty);
        }
        formDs.loadData([
          {
            startTime: res.hmeEoAutoComplete?.startTime || null,
            endTime: res.hmeEoAutoComplete?.endTime || null,
            startQty: res.hmeEoAutoComplete?.startQty || null,
            endQty: res.hmeEoAutoComplete?.endQty || null,
            reportingTotal,
          },
        ]);
      } else {
        let totalQty = null;
        if (Number(res.hmeEoAutoComplete?.startQty) && Number(res.hmeEoAutoComplete?.endQty)) {
          totalQty =
            Number(res.hmeEoAutoComplete?.endQty) - Number(res.hmeEoAutoComplete?.startQty);
        }
        // 末工序
        formDs.loadData([
          {
            startTime: res.hmeEoAutoComplete?.startTime || null,
            endTime: res.hmeEoAutoComplete?.endTime || null,
            startQty: res.hmeEoAutoComplete?.startQty || null,
            endQty: res.hmeEoAutoComplete?.endQty || null,
            unitQty: props.loginWkcInfo?.operationDTOList[0]?.unitQty,
            totalQty,
          },
        ]);
      }
    }
  };

  const column = [
    {
      name: 'serialNumber',
      width: 70,
      renderer: ({ record }) => {
        if (record) {
          return record?.index + 1;
        }
      },
    },
    {
      name: 'materialLotCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'productionBatch',
    },
    { name: 'userName' },
    {
      name: 'primaryUomQty',
      width: 70,
      align: 'left',
    },
    {
      name: 'creationDate',
      width: 200,
    },
  ];
  // useEffect(() => {
  //   console.log(document.getElementById('ProcessingCompleted'))
  // }, [])

  const handleReport = async () => {
    let qty = null;
    if (operationMethod === 'MANUAL') {
      if (Number(formDs.current?.get('qty'))) {
        // 非末工序 手工
        qty = formDs.current?.get('qty');
      } else {
        return;
      }
    } else if (operationMethod === 'AUTO') {
      if (Number(formDs.current?.get('reportingTotal'))) {
        // 非末工序 自动
        qty = formDs.current?.get('reportingTotal');
      } else {
        return;
      }
    }
    const res = await manualReport({
      params: {
        ...props.eoData,
        qty,
      },
    });
    if (res && res.success) {
      handleRecord(`报工成功，数量为:${qty}`, 'SUCCESS');
      formDs.current.set('qty', null);
      // 报工成功之后刷新批次生产单和完工历史
      props.handleRefresh({
        refreshEoData: true,
        refreshCompleteHistory: true,
      });
    } else {
      handleRecord(res.message, 'ERROR');
    }
  };

  const handeReSave = async () => {
    if (Number(formDs.current?.get('endQty')) && Number(formDs.current?.get('startQty'))) {
      const res = await reSave({
        params: {
          ...props.eoData,
          hmeEoAutoComplete: {
            ...formDs.current?.data,
          },
        },
      });
      if (res && res.success) {
        handleRecord('操作成功', 'SUCCESS');
        handleQueryInfoAuto();
      } else {
        handleRecord(res.message, 'ERROR');
      }
    }
  };

  const handleEndTime = async () => {
    if (Number(formDs.current?.get('endQty'))) {
      const res = await endTime({
        params: {
          ...props.eoData,
          hmeEoAutoComplete: {
            ...formDs.current?.data,
          },
        },
      });
      if (res && res.success) {
        handleRecord('操作成功', 'SUCCESS');
        handleQueryInfoAuto();
      } else {
        handleRecord(res.message, 'ERROR');
      }
    }
  };

  const handleStartTime = async () => {
    if (Number(formDs.current?.get('startQty'))) {
      const res = await startTime({
        params: {
          ...props.eoData,
          hmeEoAutoComplete: {
            ...formDs.current?.data,
          },
        },
      });
      if (res && res.success) {
        handleRecord('操作成功', 'SUCCESS');
        handleQueryInfoAuto();
      } else {
        handleRecord(res.message, 'ERROR');
      }
    }
  };

  const handleRecord = (message, type) => {
    props.handleAddRecords({
      cardId: props.cardId,
      messageType: type,
      recordType: '',
      message,
    });
  };

  const handleChangeEndAuto = () => {
    if (
      Number(formDs.current?.get('endQty')) &&
      Number(formDs.current?.get('endQty')) > Number(formDs.current?.get('startQty'))
    ) {
      if (lastStepFlag) {
        formDs.current.set(
          'totalQty',
          Number(formDs.current?.get('endQty')) - Number(formDs.current?.get('startQty')),
        );
      } else {
        const qty = Number(formDs.current?.get('endQty')) - Number(formDs.current?.get('startQty'));
        formDs.current?.set('reportingTotal', qty);
      }
    } else {
      if (lastStepFlag) {
        formDs.current.set('totalQty', null);
      } else {
        formDs.current.set('reportingTotal', null);
      }
      formDs.current.set('endQty', null);
    }
  };

  const handleCompleteConfirm  = () => {
    if (!formDs.current?.get('changeMaterialCode') && props?.loginWkcInfo?.materialChangeFlag === "Y") {
      
      Modal.confirm({
        title:(
          <span style={{ color: 'white' }}>
            <span>未录入转换物料，是否确认完工？</span>
          </span>
        ),
        style: {
          width: 360,
        },
        contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
        okProps: {
          style: {
            background: '#00D4CD',
            color: 'white',
            borderColor: '#00d4cd',
          },
        },
        cancelProps: {
          style: {
            background: '#50819c',
            color: 'white',
          },
        },
        onOk: () => handleComplete(),
      });
    } else {
      handleComplete();
    }
  }

  const handleComplete = async () => {
    if(formDs.current?.get('changeMaterialCode') && formDs.current?.get('changeMaterialCode') === props.eoData?.materialCode){
      notification.error({ message: '物料与当前工单物料一致，请重新选择！' });
      handleRecord('物料与当前工单物料一致，请重新选择！', 'ERROR');
      return;
    }
    if (await formDs.current?.validate(true)&&formDs.current?.get('totalQty')) {
      const materialLotNum = formDs.current?.get('materialLotNum');
      const res = await complete({
        params: {
          ...props.eoData,
          materialLotNum,
          unitQty: formDs.current?.get('unitQty'),
          qty: formDs.current?.get('totalQty'),
          productionBatch: formDs.current?.get('productionBatch'),
          changeMaterialCode: formDs.current?.get('changeMaterialCode') || null,
        },
      });
      if (res?.statusCode === 'POP') {
        Modal.confirm({
          children: res.message,
          onOk: async () => {
            const res = await complete({
              params: {
                ...props.eoData,
                materialLotNum,
                unitQty: formDs.current?.get('unitQty'),
                qty: formDs.current?.get('totalQty'),
                productionBatch: formDs.current?.get('productionBatch'),
                confirmFlag: 'Y',
              },
            });
            if (!res?.success) {
              handleRecord(res.message, 'ERROR');
              return;
            }
            handleRecord('操作成功', 'SUCCESS');
            formDs.current?.init('totalQty', null);

            if (customFlag === 'Y') {
              formDs.current?.init('unitQty', null);
              formDs.current?.getField('unitQty').set('required', true);
            } else if (singleExecution === 'Y') {
              formDs.current?.init('unitQty', null);
            } else {
              formDs.current?.init('materialLotNum', null);
            }
            // 完工之后查询完工历史
            props.handleRefresh({
              refreshEoData: true,
              refreshCompleteHistory: true,
            });
            return true;
          },
        });
        return;
      }
      if (!res?.success) {
        handleRecord(res.message, 'ERROR');
        notification.error({ message: res.message });
        return;
      }
      handleRecord('操作成功', 'SUCCESS');
      formDs.current?.init('totalQty', null);
      if (customFlag === 'Y') {
        formDs.current?.init('unitQty', null);
        formDs.current?.getField('unitQty').set('required', true);
      } else if (singleExecution === 'Y') {
        formDs.current?.init('unitQty', null);
      } else {
        formDs.current?.init('materialLotNum', null);
      }
      // 完工之后查询完工历史
      props.handleRefresh({
        refreshEoData: true,
        refreshCompleteHistory: true,
      });
      return true;
    }
  };

  const handleChangeLotNum = () => {
    if (Number(formDs.current?.get('materialLotNum'))) {
      formDs.current.set(
        'totalQty',
        Number(formDs.current?.get('materialLotNum')) * Number(formDs.current?.get('unitQty')),
      );
    } else {
      formDs.current.set('totalQty', null);
    }
  };

  const handleChangeMaterialName = () => {
    const record = formDs.current;
    const value = record.get('changeMaterialName');
    if (value) {
      record.set('changeMaterialCode',value);
    } else {
      record.set('changeMaterialCode', null);
    }
  };

  const handleBack = () => {
    setShowBarCode(false);
    detailDs.loadData([]);
  };

  const handleBarCode = async () => {
    setTitle(intl.get(`${modelPrompt}.title`).d('完工条码'));
    setShowBarCode(true);
    queryMaterialLotCode();
  };

  const queryMaterialLotCode = async () => {
    const res = await queryMaterialLot({
      params: {
        ...props.eoData,
      },
    });
    if (res && res.failed) {
      handleRecord(res.message, 'ERROR');
    } else {
      detailDs.loadData(res);
    }
  }

  const handleRevoke = async () => {
    const res = await completeCancel({
      params: {
        hmeWorkStationVO3: props?.eoData,
        list: detailDs.selected.map(e => e.data),
      },
    })
    if(res && res.failed){
      handleRecord(res.message, 'ERROR');
    }else{
      handleRecord('操作成功', 'SUCCESS');
      queryMaterialLotCode();
    }
  }

  return (
    <CardLayout.Layout
      spinning={
        manualReportLoading ||
        startTimeLoading ||
        endTimeLoading ||
        reSaveLoading ||
        queryInfoAutoLoading ||
        completeLoading ||
        queryMaterialLotLoading||
        completeCancelLoading
      }
      className={styles.ProcessingCompletedTitle}
    >
      <CardLayout.Header
        title={title}
        addonAfter={
          <>
            {lastStepFlag && (
              <>
                <TemplatePrintButton
                  disabled={detailDs.selected.length === 0}
                  printButtonCode="HME.WORK_STATION_TEMP"
                  printParams={{
                    materialLotIdList: detailDs.selected.map(e => e.get('materialLotId')).join(','),
                  }}
                />
                {showBarCode && (
                  <>
                    <Button onClick={handleBack} color="primary">
                    返回
                    </Button>
                    <Button color="primary" onClick={handleRevoke}>
                    完工撤销
                    </Button></>
                )}
                <Button color="primary" onClick={handleBarCode}>
                  条码
                </Button>
                {!showBarCode && (
                  <Button
                    onClick={handleCompleteConfirm}
                    style={{
                      background: 'rgba(0, 212, 205, 1)',
                      color: '#fff',
                      borderColor: 'rgba(0, 212, 205, 1)',
                    }}
                  >
                    完工
                  </Button>
                )}
              </>
            )}
            {!lastStepFlag && (
              <Button
                onClick={handleReport}
                disabled={
                  props.loginWkcInfo.operationDTOList[0]?.operationMethod === 'AUTO' &&
                  (!formDs.current?.get('endTime') || !formDs.current?.get('startTime'))
                }
                style={{ background: 'rgba(0, 212, 205, 1)', borderColor: 'rgba(0, 212, 205, 1)' }}
              >
                报工
              </Button>
            )}
          </>
        }
      />
      <CardLayout.Content>
        {!showBarCode && (
          <div className={styles.flex}>
            {/* 非末工序 */}
            {!lastStepFlag && operationMethod === 'MANUAL' && (
              <Form
                labelWidth={200}
                className={styles.contentForm}
                dataSet={formDs}
                labelLayout="horizontal"
              >
                <NumberField
                  name="qty"
                  precision={2}
                  min={0}
                  style={{ width: 200, background: '#50819c' }}
                />
              </Form>
            )}
            {!lastStepFlag && operationMethod === 'AUTO' && (
              <Form
                labelWidth={200}
                className={styles.contentForm}
                dataSet={formDs}
                labelLayout="horizontal"
              >
                <Row>
                  <Col span={20}>
                    <Form
                      labelLayout="horizontal"
                      dataSet={formDs}
                      labelWidth={80}
                      columns={2}
                      className={styles.contentForm}
                      style={{ fontSize: '18px' }}
                    >
                      <Output name="startTime" colSpan={1} />
                      <NumberField
                        name="startQty"
                        readOnly={
                          !(
                            (reInputFlag && formDs.current?.get('endTime')) ||
                            !formDs.current?.get('startTime')
                          )
                        }
                        colSpan={1}
                        style={{ width: 100, background: '#50819c' }}
                        suffix={<span style={{ color: '#fff' }}>kg</span>}
                      />
                      <Output name="endTime" colSpan={1} />
                      <NumberField
                        onChange={handleChangeEndAuto}
                        readOnly={
                          (!reInputFlag && formDs.current?.get('endTime')) ||
                          !formDs.current?.get('startTime')
                        }
                        name="endQty"
                        colSpan={1}
                        style={{ width: 100, background: '#50819c' }}
                        suffix={<span style={{ color: '#fff' }}>kg</span>}
                      />
                      <Output
                        name="reportingTotal"
                        colSpan={1}
                        renderer={({ record }) => {
                          if (record) {
                            return (
                              <div
                                style={{
                                  color: '#fff',
                                  background: 'rgba(0, 212, 205, 1)',
                                  width: 150,
                                  textAlign: 'center',
                                }}
                              >
                                {record.get('reportingTotal')}
                              </div>
                            );
                          }
                        }}
                      />
                    </Form>
                  </Col>
                  <Col span={4} style={{ padding: 5 }}>
                    {!formDs.current?.get('startTime') && (
                      <Button
                        color="primary"
                        onClick={handleStartTime}
                        style={{ background: '#00D4CD', color: '#fff', borderColor: '#00D4CD' }}
                      >
                        计时开始
                      </Button>
                    )}
                    {!formDs.current?.get('endTime') && formDs.current?.get('startTime') && (
                      <Button
                        onClick={handleEndTime}
                        style={{ background: 'red', color: '#fff', borderColor: 'red' }}
                      >
                        计时结束
                      </Button>
                    )}
                    {formDs.current?.get('startTime') &&
                      formDs.current?.get('endTime') &&
                      reInputFlag && (
                      <Button color="primary" onClick={handeReSave} style={{ margin: 0 }}>
                          重新录入
                      </Button>
                    )}
                  </Col>
                </Row>
              </Form>
            )}
            {lastStepFlag && (
              <Form
                labelLayout="horizontal"
                dataSet={formDs}
                labelWidth={150}
                className={styles.contentForm}
                style={{ fontSize: '18px', paddingTop: '8px' }}
              >
                {props?.loginWkcInfo?.materialChangeFlag === 'Y' && <><Select
                  name="changeMaterialName"
                  colSpan={1}
                  onChange={handleChangeMaterialName}
                  style={{ width: 200, background: '#50819c' }}
                  getPopupContainer={() => document.getElementById('operationPlatform') || document.body}
                />
                <TextField
                  name="changeMaterialCode"
                  colSpan={1}
                  disabled
                  style={{ width: 200, background: '#50819c' }}
                /></>}
                <NumberField
                  name="unitQty"
                  colSpan={1}
                  disabled={!(customFlag === 'Y' || singleExecution === 'Y')}
                  onChange={handleChangeLotNum}
                  suffix={<span style={{ color: '#fff' }}>kg</span>}
                  style={{ width: 200, background: '#50819c' }}
                />
                <NumberField
                  disabled={!(customFlag === 'Y' || singleExecution !== 'Y')}
                  onChange={handleChangeLotNum}
                  suffix={<span style={{ color: '#fff' }}>个</span>}
                  name="materialLotNum"
                  colSpan={1}
                  style={{ width: 200, background: '#50819c' }}
                />
                <Output
                  name="totalQty"
                  renderer={({ value }) => {
                    return (
                      <div
                        style={{
                          color: '#fff',
                          background: 'rgba(0, 212, 205, 1)',
                          width: 200,
                          textAlign: 'center',
                        }}
                      >
                        {value}
                      </div>
                    );
                  }}
                />

                {formDs.current.get('productionBatchFlag')!=='N'&&<TextField
                  name="productionBatch"
                  colSpan={1}
                  style={{ width: 200,background: '#50819c' }}
                />}
              </Form>
            )}
            {/* {
            lastStepFlag&&operationMethod === 'AUTO'&&
            <Row>
              <Col span={20}>
                <Form labelLayout="horizontal" dataSet={formDs} labelWidth={80} columns={2} className={styles.contentForm} style={{fontSize: '18px'}} >
                  <Output name="startTime" colSpan={1} />
                  <NumberField readOnly={formDs.current?.get('startTime')} name="startQty" colSpan={1} style={{width: 100,background: '#50819c'}} />
                  <Output name="endTime" colSpan={1} />
                  <NumberField readOnly={formDs.current?.get('endTime')} onChange={handleChangeEndAuto} name="endQty" colSpan={1} style={{width: 100,background: '#50819c'}} />
                  <Output name="totalQty"
                    colSpan={1}
                    renderer={({value}) => {
                      return <div style={{color: '#fff',background: 'rgba(0, 212, 205, 1)', width: 100,textAlign: 'center'}}>{value}</div>
                    }} />
                  <Output labelWidth={120} name="unitQty" colSpan={1} renderer={({value}) => {
                    return <div style={{color: '#fff'}}>{value}kg</div>
                  }} />
                  <NumberField suffix={<span style={{ color: '#fff' }}>个</span>}
                    name="materialLotNum" colSpan={1} style={{width: 100,background: '#50819c'}} />
                </Form>
              </Col>
              <Col span={4} style={{padding: 5}}>
                {
                  !formDs.current?.get('startTime')&&
                  <Button color='primary' onClick={handleStartTime}
                    style={{background: '#00D4CD',color: '#fff',borderColor: '#00D4CD'}}
                  >
              计时开始
                  </Button>}
                {formDs.current?.get('startTime')&&!formDs.current?.get('startTime')&&
                <Button onClick={handleEndTime} style={{background: 'red',color: '#fff',borderColor: 'red'}}>
              计时结束
                </Button>}
              </Col>
            </Row>
          } */}
          </div>
        )}

        {/* 展示完工条码 */}
        {showBarCode && (
          <div className={styles.ProcessingCompleted}>
            <Table dataSet={detailDs} columns={column} />
          </div>
        )}
      </CardLayout.Content>
    </CardLayout.Layout>
  );
});

export default formatterCollections({
  code: [`${modelPrompt}`, 'model.org.monitor'],
})(
  connect(state => {
    return {
      modelState: state[namespace],
    };
  })(WorkorderMachinedPart),
);
