/**
 * 组件介绍: 功能级别组件，应用在以下3个功能。
 * 1.mes产品的制造装配清单
 * 2.mes产品的物料装配清单
 * 3.aps产品的计划装配清单
 * ----------------------------
 * @Description: 装配清单 - 入口页面（c7n重构）
 * @Author: <EMAIL>
 * @Date: 2022/7/25 10:48
 * @LastEditTime: 2023-05-18 15:02:24
 * @LastEditors: <<EMAIL>>
 */
import React, { FC, useEffect, useMemo } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { useDataSet } from 'utils/hooks';
import { Badge } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Button as PermissionButton } from 'components/Permission';
import { Content, Header } from 'components/Page';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import { API_HOST, BASIC } from '@utils/config';
import { isNil } from 'lodash';
import { tableDS } from '../stores/ListDS';

const tenantId = getCurrentOrganizationId();

/**
 * @description 装配清单 - 入口页面入参
 * @param {string} featureTitle 详情页title
 * @param {any} history 页面路由的 history 对象
 * @param {any} match 页面路由相关的 match 对象
 * @param {any} location 页面路由相关的 location 对象
 * @param {string} exportServerCode 导出接口访问的服务
 * @param {string} uiServerCode 功能接口（如列表页的查询接口）要访问的服务
 * @param {string} typeGroup 查询条件中类型的值集code
 * @param {string} searchCode 列表页-动态筛选条后端接口唯一编码
 * @param {string} customizedCode 列表页-个性化编码
 * @param {any} customizeTable 列表页-表格个性化组件
 * @param {any} custCode 列表页-个性化单元编码，中间的字符串(eg: ${BASIC.CUSZ_CODE_BEFORE}.{customizeTable}.QUERY)
 * @return {Component} 装配清单入口页面组件 <AssemblyList />
 */
export interface AssemblyListProps {
  featureTitle: string;
  history: any;
  match: any;
  location: any;
  exportServerCode: string;
  uiServerCode: string;
  typeGroup: string;
  searchCode: string;
  customizedCode: string;
  customizeTable: any;
  custCode: string;
}

export const AssemblyList: FC<AssemblyListProps> = props => {
  const {
    featureTitle,
    history,
    location,
    exportServerCode,
    uiServerCode,
    typeGroup,
    searchCode,
    customizedCode,
    customizeTable,
    custCode,
    match: { path },
  } = props;
  const basePath = path.substring(0, path.indexOf('/list'));
  // 入口页面表格DS-用useDataSet处理可以启用DataSet的缓存，从详情界面返回主页面时保留主页面的查询条件
  const tableDs = useDataSet(() => new DataSet({ ...tableDS(uiServerCode, typeGroup) }), basePath);

  useEffect(() => {
    tableDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.QUERY,${BASIC.CUSZ_CODE_BEFORE}.${custCode}.LIST`);
    if (location?.state?._back) {
      // 详情页点取消跳转回来，query为空对象，会有state._back = -1
      tableDs.query(tableDs.currentPage);
    }
  }, [location?.state]);

  // 入口页面表格columns
  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'bomName',
        width: 300,
        renderer: ({ value, record }) => {
          return (
            <a onClick={() => history.push(`${basePath}/dist/${record?.get('bomId')}`)}>{value}</a>
          );
        },
      },
      {
        name: 'description',
      },
      {
        name: 'revision',
        width: 100,
      },
      {
        name: 'bomTypeDesc',
        width: 150,
      },
      {
        name: 'bomStatusDesc',
        width: 100,
        align: ColumnAlign.center,
      },
      {
        name: 'currentFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'dateFrom',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'dateTo',
        align: ColumnAlign.center,
        width: 150,
      },
    ],
    [],
  );

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={featureTitle}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => history.push(`${basePath}/dist/create`)}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <ExcelExport
          exportAsync
          requestUrl={`${API_HOST}${exportServerCode}/v1/${tenantId}/mt-bom/export`}
          queryParams={getExportQueryParams}
        />
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.${custCode}.LIST`,
          },
          <Table
            dataSet={tableDs}
            columns={columns}
            searchCode={searchCode}
            customizedCode={customizedCode}
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
          />,
        )}
      </Content>
    </div>
  );
};
