import intl from 'utils/intl';
import moment from "moment";
import {FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import {getCurrentSiteInfo} from "@utils/utils";

const endUrl = '';
const modelPrompt = 'tarzan.completeReport';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  primaryKey: 'dataRecordId',
  dataKey: 'content',
  totalKey: 'totalElements',
  autoLocateFirst: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-completion-report/list/ui`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        defaultValue: () => {
          const  siteInfo = getCurrentSiteInfo();
          if (siteInfo.siteId) {
            return { ...siteInfo }
          }
          return undefined;
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'workshopLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.workshopLov`).d('车间'),
      required: true,
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.AREA',
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'areaId',
      bind: 'workshopLov.areaId',
    },
    {
      name: 'areaCode',
      bind: 'workshopLov.areaCode',
    },
    {
      name: 'prodLineLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineLov`).d('产线'),
      required: true,
      ignore: FieldIgnore.always,
      lovCode: 'HME.PROD_LINE_BY_AREA',
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            areaId: record.get('areaId'),
          };
        },
      },
    },
    {
      name: 'prodLineId',
      bind: 'prodLineLov.organizationId',
    },
    {
      name: 'prodLineCode',
      bind: 'prodLineLov.organizationCode',
    },
    {
      name: 'transReasonCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transReasonCode`).d('移动类型'),
      lookupCode: 'AAFM.ASSET_IMPORTANCE',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单号'),
    },
    {
      name: 'startTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
      max: 'endTime',
      defaultValue: moment().subtract(3, 'days'),
    },
    {
      name: 'endTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
      min: 'startTime',
      defaultValue: moment(),
    },
  ],
  fields: [
    {
      name: 'plantCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.plantCode`).d('工厂'),
    },
    {
      name: 'areaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaCode`).d('车间'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('生产线'),
    },
    {
      name: 'sapWorkOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sapWorkOrderNum`).d('SAP工单'),
    },
    {
      name: 'sapStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sapStatus`).d('SAP工单状态'),
    },
    {
      name: 'sapQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sapQty`).d('SAP工单数量'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('MES工单'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('MES工单状态'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('MES工单数量'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('产出物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('产出物料描述'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('产出条码'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('产出数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('批次生产单'),
    },
    {
      name: 'eoStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoStatus`).d('批次生产单状态'),
    },
    {
      name: 'eoQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoQty`).d('批次生产单数量'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
    },
    {
      name: 'operationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationDesc`).d('工艺描述'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('产出工位编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('产出工位名称'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'productionBatch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionBatch`).d('工艺批次'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('产出时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('操作人'),
    },
    {
      name: 'transReasonCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transReasonCode`).d('移动类型'),
    },
    {
      name: 'eventId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
});


export { tableDS };
