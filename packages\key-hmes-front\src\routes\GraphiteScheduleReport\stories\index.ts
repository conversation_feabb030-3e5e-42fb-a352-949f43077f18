
import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.mes.event.graphiteScheduleReport';
const tenantId = getCurrentOrganizationId();

export const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'equipmentLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.equipmentName`).d('炉号'),
      lovCode: 'MT.MODEL.EQUIPMENT',
      textField: 'equipmentCode',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    {
      name: 'equipmentId',
      bind: 'equipmentLov.equipmentId',
    },
    {
      name: 'stoveCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stoveCode`).d('装炉编码'),
    },    
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.model.productionOrderMgt.materialId`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'powerDateStart',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.powerDateStart`).d('送电开始时间从'),
      max: 'powerDateEnd',
    },
    {
      name: 'powerDateEnd',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.powerDateEnd`).d('送电开始时间至'),
      min: 'powerDateStart',
    },
    {
      name: 'dischargingDateStart',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dischargingDateStart`).d('出炉结束时间从'),
      max: 'dischargingDateEnd',
    },
    {
      name: 'dischargingDateEnd',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dischargingDateEnd`).d('出炉结束时间至'),
      min: 'dischargingDateStart',
    },
    {
      name: 'dischargingFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dischargingFlag`).d('是否出炉'),
      textField: 'meaning',
      valueField: 'value',
      lookupCode: 'MT.YES_NO',
      defaultValue: 'N',
    },
  ],
  fields: [
    {
      name: 'stoveCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stoveCode`).d('装炉编码'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('炉号编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('炉号描述'),
    },
    {
      name: 'stoveCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stoveCount`).d('炉使用次数'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'productionBatch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionBatch`).d('工艺批次'),
    },
    {
      name: 'layerMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.layerMeaning`).d('所在层数'),
    },
    {
      name: 'sumWeight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumWeight`).d('装炉重量'),
    },
    {
      name: 'detailQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.detailQty`).d('明细重量'),
    },
    {
      name: 'feedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.feedFlag`).d('是否投料'),
    },
    {
      name: 'chargingDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.chargingDate`).d('装炉时间'),
    },
    {
      name: 'powerDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.powerDate`).d('送电时间'),
    },
    {
      name: 'dischargingDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dischargingDate`).d('出炉时间'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('出炉重量'),
    },
    {
      name: 'sumNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumNumber`).d('坩埚数量'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-graphite-schedule-report/list/ui`,
        method: 'GET',
      };
    },
  },
});
