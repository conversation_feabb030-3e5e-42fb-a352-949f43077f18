import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.crucibleBatchQuery';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

const headerTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  primaryKey: 'crucibleUsageId',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-crucible-batch-query/list/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.Crucible_Batch_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.Crucible_Batch_LIST.HEAD`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      name: 'lotObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.lotCode`).d('供应商批次'),
      lovCode: 'HME.CRUCIBLE_LOT',
      noCache: true,
      ignore: 'always',
      // required: true,
    },
    {
      name: 'lotCode',
      type: FieldType.string,
      bind: 'lotObj.lotCode',
    },
    {
      name: "materialCode",
      type: FieldType.string,
      lookupCode: 'HWM_USE_NUMBER',
      valueField: 'value',
      textField: 'value',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
  ],
  fields: [
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('供应商批次'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('坩埚物料'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('坩埚物料名称'),
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantity`).d('数量'),
    },
    {
      name: 'useQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.useQty`).d('使用次数'),
    },
    {
      name: 'discardQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.discardQty`).d('报废数量'),
    },
    {
      name: 'number',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.number`).d('预计使用次数'),
    },
    {
      name: 'avgQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.avgQty`).d('平均使用次数'),
    },
    {
      name: 'discardRate',
      type: FieldType.string,
      label: intl.get(`tarzan.common.discardRate`).d('实时破损率'),
    },
    {
      name: 'lotCreationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCreationDate`).d('批次创建时间'),
    },
  ],
});

const lineTableDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    pageSize: 10,
    selection: false,
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-crucible-batch-query/detail/list/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.Crucible_Batch_LIST.LINE`,
          method: 'GET',
        };
      },
    },
    fields: [
      {
        name: 'lot',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lot`).d('供应商批次'),
      },
      {
        name: 'quantity',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.quantity`).d('数量'),
      },
      {
        name: 'instructionDocNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.instructionDocNum`).d('领料单据'),
      },
      {
        name: 'equipmentName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.equipmentName`).d('石墨化炉'),
      },
      {
        name: 'useQty',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.useQty`).d('使用次数'),
      },
      {
        name: 'discardQty',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.discardQty`).d('报废数量'),
      },
      {
        name: 'stoveCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.stoveCode`).d('装炉图编码'),
      },
      {
        name: 'stoveCount',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.stoveCount`).d('炉次'),
      },
    ],
  };
};

export { headerTableDS, lineTableDS };
