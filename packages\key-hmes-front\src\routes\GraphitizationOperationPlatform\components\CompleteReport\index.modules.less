.CompleteReport {
  :global {
    .c7n-pro-field-label {
      color: rgba(112, 187, 243, 1) !important;
      font-size: 16px !important;
    }

    .c7n-pro-select {
      font-size: 16px !important;

      input {
        font-size: 16px !important;
      }
    }

    .c7n-pro-input-number {
      :global {
        background-color: #3c87ad !important;
        color: #fff !important;
      }
    }
    .c7n-pro-input{
      :global {
        background-color: #3c87ad !important;
        color: #fff !important;
      }
    }
    .c7n-pro-select{
      :global {
        background-color: #3c87ad !important;
        color: #fff !important;
      }
    }

    button {
      font-size: 16px;
    }
  }

  :global {
    .c7n-pro-field {
      font-size: 16px !important;

      input {
        font-size: 16px !important;
      }
    }
  }

  :global {
    .c7n-pro-output-wrapper {
      color: #fff !important;
    }

    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .c7n-pro-table-wrapper.c7n-pro-table-wrapper
      .c7n-pro-table
      .c7n-pro-table-content
      .c7n-pro-table-row {
      height: 45px !important;
    }

    // .icon-refresh {
    //   background-color: rgb(91, 136, 160) !important;
    //   color: white !important;
    // }
    // .c7n-pro-table-cell{
    //   background-color: #3c87ad !important;
    // }

    // .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-cell {
    //   background-color: #3c87ad !important;
    //   color: white !important;
    //   border: none !important;
    //   font-size: 17px !important;
    // }

    // .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
    //   background-color: #3c87ad !important;
    //   color: white !important;
    //   font-size: 17px !important;
    // }
    // .c7n-pro-table-cell-inner c7n-pro-table-cell-inner-bordered c7n-pro-table-cell-inner-editable c7n-pro-table-cell-inner-dirty c7n-pro-table-cell-inner-row-height-fixed{
    //   color: #000 !important;
    // }

    .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
      font-size: 16px !important;
    }

    // .c7n-pro-btn-wrapper {
    //   background-color: #3c87ad !important;
    //   color: white !important;
    // }

    .icon {
      color: white !important;
    }

    .c7n-pro-pagination-perpage {
      color: white !important;
    }

    .c7n-pro-pagination-page-info {
      color: white !important;
    }
  }

  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .c7n-pro-table-wrapper.c7n-pro-table-wrapper
      .c7n-pro-table
      .c7n-pro-table-content
      .c7n-pro-table-row {
      height: 45px !important;
    }

    .icon-refresh {
      background-color: rgb(91, 136, 160) !important;
      color: white !important;
    }

    // 表头
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper
      .c7n-pro-table
      .c7n-pro-table-content
      table
      .c7n-pro-table-tfoot
      tr
      th,
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper
      .c7n-pro-table
      .c7n-pro-table-content
      table
      .c7n-pro-table-thead
      tr
      th {
        background-color: #3c87ad !important;
      color: rgba(147, 203, 255, 1) !important;
      border: none !important;
      font-size: 17px !important;

    }

    // 表格内容
    .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
      background-color: #3c87ad !important;
      color: white !important;
      font-size: 17px !important;
    }

    .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .icon {
      color: white !important;
    }

    .c7n-pro-pagination-perpage {
      color: white !important;
    }

    .c7n-pro-pagination-page-info {
      color: white !important;
    }
  }

  .normalDiv {
    height: 35px;
    line-height: 35px;
    text-align: center;
    margin-left: 3.5%;
    width: 20%;
    color: #00b8ff;
    border-style: solid;
    border-width: 2px;
    border-color: #00b8ff;
    cursor: pointer;
    position: relative;
    border-radius:7px;
  }

  .selectDiv {
    height: 35px;
    line-height: 35px;
    text-align: center;
    margin-left: 3.5%;
    width: 20%;
    color: #00b8ff;
    border-style: solid;
    border-width: 2px;
    border-color: #00b8ff;
    cursor: pointer;
    position: relative;
    border-radius:7px;
  }

  .selectDiv:before {
    content: '';

    position: absolute;

    right: 0;

    bottom: 0;

    border: 7px solid #00b8ff;

    border-top-color: transparent;

    border-left-color: transparent;

    border-bottom-right-radius: 3px;
  }

  .selectDiv:after {
    content: '';

    width: 5px;

    height: 10px;

    position: absolute;

    right: 1px;

    bottom: 1px;

    border: 2px solid #fff;

    border-top-color: transparent;

    border-left-color: transparent;

    transform: rotate(45deg);
  }
}
