/**
 * @Description:  计划产销量功能维护-入口页
 */
import React, { useState } from 'react';
import { DataSet, Table, Button, NumberField, Select, Lov, DatePicker } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import notification from 'utils/notification';
import withProps from 'utils/withProps';
import { flow, isNil } from 'lodash';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { observer } from 'mobx-react-lite';
import { API_HOST, BASIC } from '@utils/config';
import { tableDS } from './stores/ListDS';

const modelPrompt = 'tarzan.planSalesList';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

const PlanSalesList = props => {
  const { tableDs } = props;

  const [loading, setLoading] = useState(false);

  const handleSave = async(record) => {
    // 判断是否有修改
    const flag = await record.validate();
    if (flag) {
      request(`${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-plan-sales/save/ui`, {
        method: 'POST',
        body: record.toData(),
      }).then(res => {
        if (res && res.failed) {
          notification.error({ message: res.message });
        } else {
          tableDs.query();
          const { records } = tableDs;
          records.forEach(i => i.setState('editing', false));
        }
      });
    } else {
      notification.error({
        message: intl.get('aatn.deliveryList.validation.require.field').d('请填写必输字段'),
      });
    }
  };
  const handleDelete = async() => {
    if (tableDs?.selected?.length === 0) {
      return;
    }
    const data = tableDs.selected.map(i=>i.get('planId'))
    setLoading(true);
    request(`${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-plan-sales/delete/ui`, {
      method: 'POST',
      body: data,
    }).then(res => {
      if (res && res.failed) {
        setLoading(false);
        notification.error({ message: res.message });
      } else {
        setLoading(false);
        tableDs.query();
        const { records } = tableDs;
        records.forEach(i => i.setState('editing', false));
      }
    });
  };

  // 取消头
  const handleCleanHead = record => {
    if (record.get('planId')) {
      record.reset();
      record.setState('editing', false);
    } else {
      tableDs.remove(record);
    }
  };

  // 编辑头
  const handleEdit = record => {
    record.setState('editing', true);
  };

  // 头列表配置
  const columns = [
    {
      name: 'siteObj',
      align: 'center',
      editor: record => record.getState('editing') && <Lov />,
    },
    {
      name: 'siteName',
      align: 'center',
    },
    {
      name: 'operationObj',
      align: 'center',
      editor: record => record.getState('editing') && <Lov />,
    },
    {
      name: 'operationDesc',
      align: 'center',
    },
    {
      name: 'workcellObj',
      align: 'center',
      editor: record => record.getState('editing') && <Lov />,
    },
    {
      name: 'planDate',
      align: 'center',
      editor: record => record.getState('editing') && <DatePicker/>,
    },
    {
      name: 'materialCode',
      align: 'center',
      editor: record => record.getState('editing') && <Select />,
    },
    {
      name: 'planQty',
      align: 'center',
      editor: record => record.getState('editing') && <NumberField precision={2} step={0.00001}/>,
    },
    {
      name: 'planSaleQty',
      align: 'center',
      editor: record => record.getState('editing') && <NumberField precision={2} step={0.00001}/>,
    },
    {
      name: 'planWeight',
      width: 180,
      editor: record => record.getState('editing'),
    },
    {
      name: 'planType',
      width: 180,
      editor: record => record.getState('editing'),
    },
    {
      name: 'lastUpdate',
      align: 'center',
    },
    {
      name: 'operator',
      width: 150,
      align: 'center',
      renderer: ({ record }) =>
        record?.getState('editing') ? (
          <>
            <a onClick={() => handleCleanHead(record)} style={{ marginRight: 20 }}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </a>
            <a onClick={() => handleSave(record)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </a>
          </>
        ) : (
          <>
            <a onClick={() => handleEdit(record)} disabled={record?.data?.status ==='AUDIT' || record?.data?.status ==='PUBLISH'}>
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </a>
          </>
        ),
    },
  ];

  // const onFieldEnterDown = () => {
  //   tableDs.query(props.tableDs.currentPage);
  // };

  const handleImport = ()=>{
    openTab({
      key: '/himp/commentImport/HME.PLAN_SALE_IMPORT',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  }

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return {
      ...queryParmas,
      // planIds: [tableDs.selected.map(item => item.get('planId'))],
    }
  };
  
  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('计划产销量功能维护')}>
        <Button
          color={ButtonColor.primary}
          disabled={tableDs.selected.length === 0}
          onClick={handleDelete}
          loading={loading}
        >
          {intl.get('hzero.common.button.delete').d('删除')}
        </Button>
        <Button icon="to-top" onClick={handleImport}>
          {intl.get('hzero.common.button.import').d('导入')}
        </Button>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/hme-plan-sales/excel/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="jhcxl"
          customizedCode="jhcxl"
          dataSet={tableDs}
          columns={columns}
          highLightRow
          queryBar="filterBar"
        />,
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.planSalesList', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({ ...tableDS()});
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(observer(PlanSalesList));
