/**
 * @Description: 库存调拨平台 - 列表页
 * @Author: <EMAIL>
 * @Date: 2022/3/8 10:11
 * @LastEditTime: 2023-05-18 16:21:34
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Button, DataSet, Dropdown, Menu, Modal, Table, Form, Lov } from 'choerodon-ui/pro';
import { Badge, Collapse, Spin } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
// import FRPrintButton from '@components/tarzan-ui/FRPrintButton';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';
import { queryMapIdpValue } from 'services/api';
import { headerTableDS, lineTableDS } from '../stores/EntranceDS';
import { drawerTableDS, inventoryDrawerTableDS } from '../stores/DrawerTableDS';
import { HandleChangeStatus, HandleDeleteLine, SendOut, SendIn, SendInSubmit, WeighbridgeSynchronizationApi } from '../services';
import AppointMaterialLotPage from '../AppointMaterialLot';
import { TemplatePrintButton } from '../../../../components/tarzan-ui';
import styles from './index.module.less';


const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inLibrary.sendReceiveDoc';

function isAllEqualWithKeyWord(array: string[], keyWord: string) {
  // 勾选的都是下达状态，状态变更可点击，且下拉显示取消
  const cancelList: string[] = ['RELEASED'];
  // 这几种状态时，状态变更可点击，且下拉显示关闭
  const closeList: string[] = [
    'PROCESSING',
    '1_PROCESSING',
    '1_COMPLETED',
    '2_PROCESSING',
    'COMPLETED',
  ];
  if (array.length > 0) {
    if (keyWord === 'CANCEL') {
      return array.some(value => {
        return cancelList.indexOf(value) === -1;
      });
    }
    if (keyWord === 'CLOSED') {
      return array.some(value => {
        return closeList.indexOf(value) === -1;
      });
    }
  } else {
    return false;
  }
}

const SendReceiveList = observer(props => {
  const {
    headerTableDs,
    lineTableDs,
    match: { path },
    customizeTable,
  } = props;
  // 动态列实际columns
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [selectedInstructionDocIds, setSelectedInstructionDocIds] = useState<any[]>([]);
  const drawerTableDs: DataSet = useMemo(() => new DataSet(drawerTableDS()), []);
  const sendDrawerTableDs: DataSet = useMemo(() => new DataSet(inventoryDrawerTableDS()), []);
  const receiveDrawerTableDs: DataSet = useMemo(() => new DataSet(inventoryDrawerTableDS()), []);
  const [instructionDocTypeList, setInstructionDocType] = useState<string[]>([]);
  const [instructionDocTypeInList, setInstructionDocTypeInList] = useState<string[]>([]);

  const { run: weighbridgeSynchronizationApi, loading: weighbridgeSynchronizationLoading } = useRequest(WeighbridgeSynchronizationApi(),
    {
      manual: true,
      needPromise: true,
    },
  );

  const { run: handleChangeStatus, loading: changeStatusLoading } = useRequest(
    HandleChangeStatus(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: sendOut, loading: sendOutLoading } = useRequest(
    SendOut(),
    {
      manual: true,
      needPromise: true,
    },
  );

  const { run: sendIn, loading: sendInLoading } = useRequest(
    SendIn(),
    {
      manual: true,
      needPromise: true,
    },
  );

  const { run: sendInSubmit } = useRequest(
    SendInSubmit(),
    {
      manual: true,
      needPromise: true,
    },
  );

  const { run: handleDeleteLine } = useRequest(HandleDeleteLine(), {
    manual: true,
    needPromise: true,
  });

  const { run: lengSave, loading: lengSaveLoading } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/inventory-send-receive/save/head/for/ui`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: lengSaveDetail } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/inventory-send-receive/material-lot/save/for/ui`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );
  let _copyDrawer;

  // 当保存成功回到页面时自动重查数据
  useEffect(() => {
    // 当在页签上右键刷新时，如果当前表格有勾选数据时，需要随之变按钮禁用状态
    handleDataSetSelectUpdate();
    if (props?.location?.state && props?.location?.state?.queryFlag === true) {
      headerTableDs.query(headerTableDs.currentPage);
    }
  }, []);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  // 进来不查询，当从详情返回后查询当前页面
  useEffect(() => {
    if (headerTableDs.toData().length > 0) {
      headerTableDs.query(headerTableDs.currentPage);
    }
    queryMapIdpValue({
      tenantId,
      list: 'HWM_SEND_RECEIVE_OUTSOURCING',
      inList: 'HWM_SEND_RECEIVE_WAREHOUSE',
    }).then(res => {
      if(res&&res.list){
        setInstructionDocType(res.list.map(i => i.value))
        setInstructionDocTypeInList(res.inList.map(i => i.value))
      }
    });
  }, []);

  const listener = flag => {
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(headerTableDs, 'batchSelect', handleDataSetSelectUpdate);
      handler.call(headerTableDs, 'batchUnSelect', handleDataSetSelectUpdate);
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    handleDataSetSelectUpdate();
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      queryLineTable(null);
    }
  };

  const headerRowClick = record => {
    const ds = lineTableDs.queryDataSet;
    ds.getField('fromWareHouseLov').set('lovPara', {
      tenantId,
      enableFlag: 'Y',
      siteId: record.get('fromSiteId'),
      locatorCategoryAreaFlag: 'Y',
    });
    ds.getField('toWareHouseLov').set('lovPara', {
      tenantId,
      enableFlag: 'Y',
      siteId: record.get('toSiteId'),
      locatorCategoryAreaFlag: 'Y',
    });
    queryLineTable(record?.toData()?.instructionDocId);
  };

  // 行列表数据查询
  const queryLineTable = instructionDocId => {
    if (instructionDocId) {
      lineTableDs.setQueryParameter('instructionDocId', instructionDocId);
    } else {
      lineTableDs.setQueryParameter('instructionDocId', 0);
    }
    lineTableDs.query();
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    const _selectedStatus: string[] = [];
    const _instructionDocIds: string[] = [];
    headerTableDs.selected.forEach(item => {
      const { instructionDocStatus, instructionDocId } = item.toData();
      _instructionDocIds.push(instructionDocId);
      if (_selectedStatus.indexOf(instructionDocStatus) === -1) {
        _selectedStatus.push(instructionDocStatus);
      }
    });
    setSelectedInstructionDocIds(_instructionDocIds);
    setSelectedStatus(_selectedStatus);
  };

  // 点击状态变更的回调
  const clickMenu = async key => {
    const selectItems: any = [];
    headerTableDs.selected.forEach(item => {
      const _selectItem = {
        instructionDocId: item?.toData()?.instructionDocId,
        instructionDocStatus: item?.toData()?.instructionDocStatus,
        instructionDocType: item?.toData()?.instructionDocType,
      };
      selectItems.push({ ..._selectItem });
    });

    return handleChangeStatus({
      params: {
        instructionDocList: selectItems,
        alterStatus: key,
      },
    }).then(res => {
      if (res && res.success) {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        notification.success({});
        headerTableDs.clearCachedSelected();
        headerTableDs.query(headerTableDs.currentPage);
      }
    });
  };

  // 点击"行取消"的回调
  const clickDeleteLine = async record => {
    return handleDeleteLine({
      params: record.toData().instructionDocLineId,
    }).then(res => {
      if (res && res.success) {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        notification.success({});
        headerTableDs.clearCachedSelected();
        headerTableDs.query(headerTableDs.currentPage);
      }
    });
  };

  const handleCancelDetail = (record:any) => {
    record.reset();
    record.setState('editing', false);
  };

  const handleSubmitDetail = async (record:any) => {
    return lengSaveDetail({
      params: [record.toData()],
    }).then((res) => {
      if(res.success){
        notification.success({
          message: '操作成功',
        });
        drawerTableDs.query();
        record.setState('editing', false);
      }
    })
  };

  const materialLotColumns: ColumnProps[] = [
    { name: 'identification', width: 150 },
    { name: 'materialLotStatusDesc', width: 150 },
    { name: 'container', width: 150 },
    { name: 'qty', align: ColumnAlign.right },
    { name: 'weighingQuantity', align: ColumnAlign.right, width: 120, editor: record => record?.getState('editing') },
    { name: 'uomCode' },
    { name: 'weighingTime', width: 150, editor: record => record?.getState('editing')  },
    { name: 'numberPlate' , width: 120, editor: record => record?.getState('editing') },
    { name: 'lot' },
    { name: 'sendLocatorCode', width: 150 },
    { name: 'sendDate', width: 150, align: ColumnAlign.center },
    { name: 'sendRealName' },
    { name: 'receiveLocatorCode', width: 150 },
    { name: 'receiveDate', width: 150, align: ColumnAlign.center },
    { name: 'receivePerson' },
    {
      name: 'opration',
      lock: ColumnLock.right,
      width: 180,
      align: ColumnAlign.center,
      title: '操作',
      renderer: ({ record }) => renderActionDetail(record),
    },
  ];

  const renderActionDetail = (record) => {
    if(!record?.getState('editing')){
      return (
        <PermissionButton
          type="text"
          permissionList={[
            {
              code: `hzero.tarzan.in.library.send.receive.doc.new.detail.button.edit`,
              type: 'button',
              meaning: '明细页-编辑按钮',
            },
          ]}
          onClick={(e:any) => {
            e.preventDefault();
            e.stopPropagation()
            record.setState('editing', true);
          }}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </PermissionButton>
      )
    }
    return (
      <>
        <a onClick={() => handleCancelDetail(record)} style={{ marginRight: '0.1rem' }}>
          {intl.get('hzero.common.button.cancel').d('取消')}
        </a>
        <a onClick={() => handleSubmitDetail(record)}>
          {intl.get('hzero.common.button.save').d('保存')}
        </a>
      </>
    )
  };

  const sendColumns: ColumnProps[] = [
    { name: 'identification' },
    { name: 'containerIdentification' },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'qualityStatusDesc',
    },
    { name: 'qty', align: ColumnAlign.right },
    { name: 'uomCode' },
    { name: 'lot' },
    { name: 'sendLocatorCode', width: 150 },
    { name: 'sendDate', width: 150, align: ColumnAlign.center },
    { name: 'sendRealName' },
    { name: 'targetMaterialLotCode' },
  ];
  const receiveColumns: ColumnProps[] = [
    { name: 'identification' },
    { name: 'containerIdentification' },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'qualityStatusDesc',
    },
    { name: 'qty', align: ColumnAlign.right },
    { name: 'uomCode' },
    { name: 'lot' },
    { name: 'receiveLocatorCode', width: 150 },
    { name: 'receiveDate', width: 150, align: ColumnAlign.center },
    { name: 'receivePerson' },
    { name: 'targetMaterialLotCode' },
  ];

  // 点击“物料批明细”的回调
  const goLotDetail = record => {
    // 实物
    if (!(['MAT', 'LOT'].includes(record?.data?.identifyType) || ['MAT', 'LOT'].includes(record?.data?.toIdentifyType))) {
      drawerTableDs.setQueryParameter('instructionDocLineId', record.toData().instructionDocLineId);
      drawerTableDs.setQueryParameter('instructionDocType', record.toData().instructionDocType);
      drawerTableDs.setQueryParameter('identifyType', record.toData().identifyType);
      drawerTableDs.setQueryParameter('toIdentifyType', record.toData().toIdentifyType);
      drawerTableDs.query();
    }

    // 非实物
    if (['MAT', 'LOT'].includes(record?.data?.identifyType) || ['MAT', 'LOT'].includes(record?.data?.toIdentifyType)) {
      sendDrawerTableDs.setQueryParameter(
        'instructionDocLineId',
        record.toData().instructionDocLineId,
      );
      sendDrawerTableDs.setQueryParameter('instructionDocType', record.toData().instructionDocType);
      sendDrawerTableDs.setQueryParameter('identifyType', record.toData().identifyType);
      sendDrawerTableDs.setQueryParameter('toIdentifyType', record.toData().toIdentifyType);
      sendDrawerTableDs.setQueryParameter('detailCategory', 'SEND');
      sendDrawerTableDs.query();
      receiveDrawerTableDs.setQueryParameter(
        'instructionDocLineId',
        record.toData().instructionDocLineId,
      );
      receiveDrawerTableDs.setQueryParameter(
        'instructionDocType',
        record.toData().instructionDocType,
      );
      receiveDrawerTableDs.setQueryParameter('identifyType', record.toData().identifyType);
      receiveDrawerTableDs.setQueryParameter('toIdentifyType', record.toData().toIdentifyType);
      receiveDrawerTableDs.setQueryParameter('detailCategory', 'RECEIVE');
      receiveDrawerTableDs.query();
    }
    _copyDrawer = Modal.open({
      title:
        !(['MAT', 'LOT'].includes(record?.data?.identifyType) || ['MAT', 'LOT'].includes(record?.data?.toIdentifyType))
          ? intl.get(`${modelPrompt}.materialBatch.details`).d('物料批信息')
          : intl.get(`${modelPrompt}.materialDetail`).d('物料明细'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          {!(['MAT', 'LOT'].includes(record?.data?.identifyType) || ['MAT', 'LOT'].includes(record?.data?.toIdentifyType)) && (
            customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
              },
              <Table dataSet={drawerTableDs} columns={materialLotColumns} />,
            )
          )}
          {(['MAT', 'LOT'].includes(record?.data?.identifyType) || ['MAT', 'LOT'].includes(record?.data?.toIdentifyType)) && (
            <>
              <Collapse bordered={false} defaultActiveKey={['sendDetail']}>
                <Panel
                  header={intl.get(`${modelPrompt}.send.detail`).d('发出明细')}
                  key="sendDetail"
                >
                  {customizeTable(
                    {
                      code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
                    },
                    <Table dataSet={sendDrawerTableDs} columns={sendColumns} />,
                  )}
                </Panel>
              </Collapse>
              <Collapse bordered={false} defaultActiveKey={['receiveDetail']}>
                <Panel
                  header={intl.get(`${modelPrompt}.receive.detail`).d('接收明细')}
                  key="receiveDetail"
                >
                  {customizeTable(
                    {
                      code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
                    },
                    <Table dataSet={receiveDrawerTableDs} columns={receiveColumns} />,
                  )}
                </Panel>
              </Collapse>
            </>
          )}
        </>
      ),
      footer: (
        <Button
          style={{ float: 'right' }}
          onClick={() => {
            _copyDrawer.close();
          }}
        >
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 打开指定物料批页面
  const handleAppointMaterialLot = async record => {
    const recordData = record.toData();
    const appointProps = {
      instructionDocId: headerTableDs?.current?.toData().instructionDocId,
      instructionDocType: headerTableDs?.current?.toData().instructionDocType,
      instructionId: recordData.instructionId,
      instructionDocLineId: recordData.instructionDocLineId,
      materialId: recordData.materialId,
      revisionCode: recordData.revisionCode,
      siteId: recordData.toSiteId,
      locatorId: recordData.fromLocatorId || recordData.fromWareHouseId,
      ownerType: recordData.fromOwnerType,
      ownerId: recordData.fromOwnerId,
      toleranceFlag: recordData.toleranceFlag,
      toleranceType: recordData.toleranceType,
      toleranceMaxValue: recordData.toleranceMaxValue,
      toleranceMinValue: recordData.toleranceMinValue,
      quantity: recordData.requiredQty,
    }
    if (appointProps.instructionId) {
      Modal.open({
        title: intl.get(`${modelPrompt}.AppointMaterialLotPage`).d('指定物料批'),
        key: Modal.key(),
        className: 'hmes-style-modal',
        style: {
          width: 1000,
        },
        maskClosable: true,
        destroyOnClose: true,
        drawer: true,
        closable: true,
        okButton: false,
        cancelButton: false,
        children: (
          <AppointMaterialLotPage
            appointProps={appointProps}
            // customizeTable={customizeTable}
          />
        ),
      });
    }
  };

  const handleCancel = (record) => {
    record.reset();
    record.setState('editing', false);
  };

  const handleSubmit = async (record) => {
    return lengSave({
      params: record.toData(),
    }).then((res) => {
      if(res.success){
        notification.success({
          message: '操作成功',
        });
        headerTableDs.query()
        record.setState('editing', false);
      }
    })
  };

  const headerTableColumns: ColumnProps[] = [
    {
      name: 'instructionDocNum',
      lock: ColumnLock.left,
      width: 180,
      renderer: ({ record, value }) => {
        if (record?.get('instructionDocStatus') === 'RELEASED') {
          return (
            <a
              onClick={() => {
                headerTableDs.batchUnSelect(headerTableDs.selected);
                props.history.push(
                  `/hwms/in-library/send-receive-doc-new/detail/${record?.get('instructionDocId')}`,
                );
              }}
            >
              {value}
            </a>
          );
        }
        return value;
      },
    },
    { name: 'instructionDocTypeDesc', width: 170 },
    { name: 'instructionDocStatusDesc' },
    { name: 'weighingQuantity', editor: record => record?.getState('editing'), width:120 },
    { name: 'weighingTime', editor: record => record?.getState('editing'), width:150 },
    { name: 'numberPlate', editor: record => record?.getState('editing'), width:120 },
    { name: 'fromSiteCode', width: 150 },
    { name: 'toSiteCode', width: 150 },
    {
      name: 'demandTime',
      width: 150,
      align: ColumnAlign.center,
    },
    { name: 'printTimes' },
    { name: 'createByName' },
    {
      name: 'createDate',
      width: 150,
      align: ColumnAlign.center,
    },
    {
      name: 'specialFlag',
      width: 140,
      editor: record => record?.getState('editing'),
      renderer: ({ record }) => record?.get('flex')?.specialFlag,
    },
    { name: 'remark' },
    {
      name: 'lastUpdatedByName',
      width: 100,
    },
    {
      name: 'lastUpdateDate',
      width: 140,
    },
    {
      name: 'opration',
      lock: ColumnLock.right,
      width: 180,
      align: ColumnAlign.center,
      title: '操作',
      renderer: ({ record }) => renderAction(record),
    },
  ];

  const renderAction = (record) => {
    if(!record?.getState('editing')){
      return (
        <PermissionButton
          type="text"
          permissionList={[
            {
              code: `hzero.tarzan.in.library.send.receive.doc.new.button.edit`,
              type: 'button',
              meaning: '主页-编辑按钮',
            },
          ]}
          onClick={(e:any) => {
            e.preventDefault();
            e.stopPropagation()
            record.setState('editing', true);
          }}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </PermissionButton>
      )
    }
    return (
      <>
        <a onClick={() => handleCancel(record)} style={{ marginRight: '0.1rem' }}>
          {intl.get('hzero.common.button.cancel').d('取消')}
        </a>
        <a onClick={() => handleSubmit(record)}>
          {intl.get('hzero.common.button.save').d('保存')}
        </a>
      </>
    )
  };

  const lineTableColumns: ColumnProps[] = [
    { name: 'lineNumber', lock: ColumnLock.left, align: ColumnAlign.left },
    {
      name: 'identifyType',
      lock: ColumnLock.left,
      width: 120,
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        } if (value === 'MATERIAL_LOT') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
        return '';
      },
    },
    { name: 'materialCode', lock: ColumnLock.left, width: 150 },
    { name: 'revisionCode', lock: ColumnLock.left },
    {
      name: 'materialName',
      width: 150,
    },
    { name: 'requiredQty', align: ColumnAlign.right },
    { name: 'uomCode' },
    { name: 'statusDesc' },
    { name: 'firstExecutedQty' },
    { name: 'secondExecutedQty' },
    { name: 'fromSiteCode', width: 150 },
    { name: 'fromWareHouseCode', width: 150 },
    { name: 'fromLocatorCode', width: 150 },
    { name: 'soNumber' },
    { name: 'soLineNumber', width: 150 },
    { name: 'toSiteCode', width: 150 },
    { name: 'toWareHouseCode', width: 150 },
    { name: 'toLocatorCode', width: 150 },
    {
      name: 'toleranceFlag',
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    { name: 'toleranceTypeDesc' },
    { name: 'toleranceMaxValue', align: ColumnAlign.right },
    { name: 'toleranceMinValue', align: ColumnAlign.right },
    { name: 'contractNumber' },
    {
      name: 'poLineId',
      lock: ColumnLock.right,
      width: 200,
      align: ColumnAlign.center,
      title: intl.get(`${modelPrompt}.option`).d('操作'),
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a onClick={() => goLotDetail(record)}>
              {intl.get(`${modelPrompt}.details`).d('明细')}
            </a>
            <a
              disabled={!(!['CANCEL', 'CLOSED', 'COMPLETED'].includes(record?.toData().statusCode) && ["MATERIAL_LOT" , ''].includes(record?.toData().fromIdentifyType))}
              onClick={() => handleAppointMaterialLot(record)}
            >
              {intl.get(`${modelPrompt}.create.materialLotAppoint`).d('指定物料批')}
            </a>
            <a disabled={record?.get('cancelFlag') !== 'Y'} onClick={() => clickDeleteLine(record)}>
              {intl.get(`${modelPrompt}.option.lineCancel`).d('行取消')}
            </a>
          </span>
        );
      },
    },
  ];

  const menu = (
    <Menu className={styles['split-menu']} style={{ width: '100px' }}>
      <Menu.Item disabled={isAllEqualWithKeyWord(selectedStatus, 'CANCEL')} key="CANCEL">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('CANCEL')}>
          {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
        </a>
      </Menu.Item>
      <Menu.Item disabled={isAllEqualWithKeyWord(selectedStatus, 'CLOSED')} key="CLOSED">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('CLOSED')}>
          {intl.get(`${modelPrompt}.button.close`).d('关闭')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const onFieldEnterDown = () => {
    headerTableDs.query(props.headerTableDs.currentPage);
  }
  const handleSendOut = async () => {
    const res = await sendOut({
      params: {
        instructionDocId: headerTableDs.selected[0]?.data.instructionDocId,
        locatorId: headerTableDs.selected[0]?.data.locatorId,
        toWareHouseId: lineTableDs?.records[0]?.data.toWareHouseId,
        sendCheckFlag: 'CHECK',
      },
    })
    if(res&&res.success){
      if(res.rows.flag === 'Y'){
        const locatorDs = new DataSet({
          fields: [{
            name: 'locatorLov',
            label: intl.get(`${modelPrompt}.label.locator`).d('库位'),
            type: FieldType.object,
            lovCode: 'HWM_LOCATOR_CODE_NEW',
            required: true,
            textField: 'locatorName',
            valueField: 'locatorId',
            lovPara: {
              instructionDocId: headerTableDs.selected[0]?.data.instructionDocId,
              locatorId: headerTableDs.selected[0]?.data.locatorId,
              toWareHouseId: lineTableDs?.records[0]?.data.toWareHouseId,
            },
          },
          {
            name: 'locatorId',
            bind: 'locatorLov.locatorId',
          }],
        })
        Modal.open({
          key: 'approvalHistory',
          maskClosable: false, // 点击蒙层是否允许关闭
          keyboardClosable: false, // 按 esc 键是否允许关闭
          destroyOnClose: true, // 关闭时是否销毁
          drawer: false,
          drawerBorder: false,
          style: {
            width: 560,
          },
          title: '选择库位',
          children: <Form dataSet={locatorDs}>
            <Lov name="locatorLov" />
          </Form>,
          onOk: async () => {
            if(await locatorDs?.current?.validate(true)){
              const result = await sendOut({
                params: {
                  instructionDocId: headerTableDs.selected[0]?.data.instructionDocId,
                  locatorId: locatorDs.current?.get('locatorId'),
                  toWareHouseId: lineTableDs?.records[0]?.data.toWareHouseId,
                  sendCheckFlag: 'EXECUTE',
                },
              })
              if(result&&!result.failed){
                notification.success({
                  message: '操作成功',
                });
                headerTableDs.query()
              }
              return true;

            }
            return false
          },
        });
      }else{
        notification.success({
          message: '操作成功',
        });
        headerTableDs.query()
      }
    }
  };

  const handleSendIn = async () => {
    const lovParams= {
      ...headerTableDs.selected[0]?.data,
    };
    const res = await sendIn({
      params: {
        ...headerTableDs.selected[0]?.data,
      },
    })
    if(res&&res.success){
      if(res.rows.flag === true){
        const locatorDs = new DataSet({
          fields: [{
            name: 'locatorLov',
            label: intl.get(`${modelPrompt}.label.locator`).d('库位'),
            type: FieldType.object,
            lovCode: 'HWM_LOCATOR_CODE',
            required: true,
            textField: 'locatorName',
            valueField: 'locatorId',
            lovQueryUrl:`${BASIC.HMES_BASIC}/v1/${tenantId}/inventory-send-receive/recive`,
            lovQueryAxiosConfig: {
              method: 'POST',
              transformResponse(data) {
                const { list } = JSON.parse(data)?.rows;
                return list;
              },
            },
            lovPara: {
              ...lovParams,
            },
          },
          {
            name: 'locatorId',
            bind: 'locatorLov.locatorId',
          }],
        })
        Modal.open({
          key: 'sendIn',
          maskClosable: false, // 点击蒙层是否允许关闭
          keyboardClosable: false, // 按 esc 键是否允许关闭
          destroyOnClose: true, // 关闭时是否销毁
          drawer: false,
          drawerBorder: false,
          style: {
            width: 560,
          },
          title: '选择库位',
          children: <Form dataSet={locatorDs}>
            <Lov name="locatorLov" />
          </Form>,
          onOk: async () => {
            if(await locatorDs?.current?.validate(true)){
              const result = await sendInSubmit({
                params: {
                  ...headerTableDs.selected[0]?.data,
                  locatorId: locatorDs.current?.get('locatorId'),
                },
              })
              if(result&&!result.failed){
                notification.success({
                  message: '操作成功',
                });
                headerTableDs.query()
              }
              return true;

            }
            return false
          },
        });
      }else{
        notification.success({
          message: '操作成功',
        });
        headerTableDs.query()
      }
    }
  }

  const handleWeighbridgeSynchronization = () => {
    const synchronizationParams = { instructionDocIds: selectedInstructionDocIds };
    return weighbridgeSynchronizationApi({
      params: synchronizationParams,
    }).then((res) => {
      if(res.success){
        notification.success({
          message: '操作成功',
        });
        headerTableDs.query()
      }
    })
  }


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.sendReceiveDoc`).d('库存调拨平台')}>
        <PermissionButton
          type="c7n-pro"
          loading={sendInLoading}
          disabled={headerTableDs.selected.length !== 1||
            ['COMPLETED', 'CLOSED', 'CANCEL','RELEASED'].includes(headerTableDs?.selected[0]?.data?.instructionDocStatus)||
            !instructionDocTypeInList.includes(headerTableDs?.selected[0]?.data?.instructionDocType)}
          color={ButtonColor.primary}
          onClick={handleSendIn}
          permissionList={[
            {
              code: `${path}.button.oneClickSendIn`,
              type: 'button',
              meaning: '列表页-一键接收按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.oneClickSendIn`).d('一键接收')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          loading={sendOutLoading}
          disabled={headerTableDs.records.length === 0
            ||headerTableDs.selected.length !== 1||
            ['COMPLETED', 'CLOSED', 'CANCEL'].includes(headerTableDs?.selected[0]?.data?.instructionDocStatus)||
            !instructionDocTypeList.includes(headerTableDs?.selected[0]?.data?.instructionDocType)}
          color={ButtonColor.primary}
          onClick={handleSendOut}
          permissionList={[
            {
              code: `${path}.button.oneClickSendOut`,
              type: 'button',
              meaning: '列表页-一键发出按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.oneClickSendOut`).d('一键发出')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => props.history.push(`/hwms/in-library/send-receive-doc-new/detail/create`)}
          permissionList={[
            {
              code: `${path}.button.create`,
              type: 'button',
              meaning: '列表页-新建按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.create`).d('创建单据')}
        </PermissionButton>
        <Dropdown
          overlay={menu}
          disabled={
            selectedStatus.length === 0 ||
            (isAllEqualWithKeyWord(selectedStatus, 'CLOSED') &&
              isAllEqualWithKeyWord(selectedStatus, 'CANCEL'))
          }
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={
              selectedStatus.length === 0 ||
              (isAllEqualWithKeyWord(selectedStatus, 'CLOSED') &&
                isAllEqualWithKeyWord(selectedStatus, 'CANCEL'))
            }
            loading={changeStatusLoading}
            permissionList={[
              {
                code: `${path}.button.changeStatus`,
                type: 'button',
                meaning: '列表页-状态变更按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        {/* <FRPrintButton
          kid="TRANSFER_ORDER"
          queryParams={selectedInstructionDocIds}
          disabled={!selectedInstructionDocIds.length}
          printObjectType="INSTRUCTION_DOC"
        /> */}
        <Button
          color={ButtonColor.primary}
          disabled={headerTableDs.selected.length !== 1  || headerTableDs.selected[0].get('instructionDocStatus') !== 'RELEASED'}
          onClick={handleWeighbridgeSynchronization}
          loading={weighbridgeSynchronizationLoading}
        >
          {intl.get(`${modelPrompt}.select.weighbridgeSynchronization`).d('地磅同步')}
        </Button>
        <TemplatePrintButton
          disabled={!selectedInstructionDocIds.length}
          printButtonCode='HME-INVENTORY_SEND-PRINT'
          printParams={{ docId: selectedInstructionDocIds.join(',') }}
        />
      </Header>
      <Spin spinning={lengSaveLoading}>
        <Content>
          {customizeTable(
            {
              filterCode: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.QUERY`,
              code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.HEAD`,
            },
            <Table
              queryBar={TableQueryBarType.filterBar}
              queryBarProps={{
                fuzzyQuery: false,
                autoQuery: false,
                onFieldEnterDown,
              }}
              searchCode="kcdbpt1"
              customizedCode="kcdbpt1"
              dataSet={headerTableDs}
              columns={headerTableColumns}
              highLightRow
              showCachedSelection={false}
              onRow={({ record }) => {
                return {
                  onClick: () => {
                    headerRowClick(record);
                  },
                };
              }}
            />,
          )}
          <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
            <Panel header={intl.get(`${modelPrompt}.title.line`).d('行信息')} key="basicInfo">
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.LINE`,
                },
                <Table
                  customizedCode="kcdbpt2"
                  className={styles['expand-table']}
                  dataSet={lineTableDs}
                  queryBar={TableQueryBarType.bar}
                  highLightRow={false}
                  columns={lineTableColumns}
                  onRow={({ record }) => {
                    return {
                      onClick: () => {
                        lineTableDs.select(record);
                      },
                    };
                  }}
                />,
              )}
            </Panel>
          </Collapse>
        </Content>
      </Spin>

    </div>
  );
});

export default flow(
  formatterCollections({ code: ['tarzan.inLibrary.sendReceiveDoc', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_MATERIAL_LOT.QUERY`,
    ],
  }),
)(SendReceiveList);
