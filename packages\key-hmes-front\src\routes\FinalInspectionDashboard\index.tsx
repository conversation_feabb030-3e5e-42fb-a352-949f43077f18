import React, { useEffect, useState, useMemo } from 'react';
import moment from 'moment';
import { Content } from 'components/Page';
import { FullScreenContainer } from '@jiaminghi/data-view-react';
import { DataSet, Lov } from 'choerodon-ui/pro';
import logo from './assets/logo.png';
import request from 'utils/request';
import styles from './index.module.less';
import PassRate from './components/PassRate';
import NgQty from './components/NgQty';
import CpChart from './components/CpChart';
import NotPassProfile from './components/NotPassProfile';
import { getCurrentOrganizationId } from 'utils/utils';
import { topFilterFormDS } from './Stores';

const CurrentTime = () => {
  const [nowTime, setNowTime] = useState(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
  useEffect(() => {
    const timer = setInterval(() => {
      return setNowTime(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
    }, 1000)

    return () => {  // 每次卸载都执行此函数，清楚定时器
      clearTimeout(timer)
    }
  }, []);
  return <span style={{ color: '#36C5FF', fontSize: 18, fontWeight: 600, marginLeft: '3%',marginBottom:'5%' }}> {nowTime} </span>;
};

const Main = ({ topFilterFormDs, isFullScreen }) => {
// const Main = ({isFullScreen}) => {
  // const [pieData, setPieData] = useState([]);
  
  const [filterValue, setFilterValue] = useState([]); // 筛选值

  const [timers, setTimers] = useState<number>(1000000000);
  
  // const [initLoading, setInitLoading] = useState<boolean>(false);

  useEffect(() => {
    getTimers();
  }, [])

  const onFilterChange = (e) => {
    setFilterValue(e);
    console.log('11',filterValue);    
  };

  const getTimers = async () => {
    const url = `/hpfm/v1/${getCurrentOrganizationId()}/lovs/value/batch?QMS.MANAGEMENT_FREQUENCY=QMS.MANAGEMENT_FREQUENCY`
    const result = await request(url, {
      method: 'GET',
    });
    const data = result['QMS.MANAGEMENT_FREQUENCY'].filter(item => item.value === 'FINAL')
    if (data.length > 0) {
      setTimers(Number(data[0].meaning))
    }
  }

  return (
    <>
      <Content style={{ padding: 0, margin: 0, height: '100%' }}>
        {/* loading */}
        {/* {initLoading && <Spin spinning={initLoading} className={styles['center-loading']} />} */}
        <div className={styles['dashboard-container']}>
          {/* header */}
          <div className={styles['dashboard-title']}>
            <div className={styles['dashboard-title-left']}>
              <img src={logo} alt="img" style={{ width:'35%',height:'100%',marginLeft: '2%' }} />
              <CurrentTime />
              {/* <TopFilterForm topFilterFormDS={topFilterFormDs} setFilterValue={setFilterValue} /> */}
            </div>
            <div className={styles['dashboard-title-center']}>制程/成品检验看板</div>
            <div className={styles['dashboard-title-right']}>
              <Lov
                dataSet={topFilterFormDs}
                name="materialLov"
                placeholder="请选择物料"
                style={{backgroundColor: 'rgba(0,0,0,0)'}}
                onChange={onFilterChange}
                maxTagCount={1}
                maxTagTextLength={3}
                maxTagPlaceholder={(restValues) => `+${restValues.length}...`}
              />
            </div>
          </div>

          {/* Content */}
          <div className={styles['dashboard-content']}>
            <div className={styles['dashboard-left-side']}>
              <div className={styles['dashboard-item-left-top']}>
                <PassRate timers={timers}/>
              </div>
              <div className={styles['dashboard-item-left-bottom']}>            
                <NotPassProfile  timers={timers}/>
              </div>
            </div>
            <div className={styles['dashboard-right-side']}>
              <div className={styles['dashboard-item-left-top']}>
                <NgQty isFullScreen={isFullScreen} timers={timers}/>
              </div>
              <div className={styles['dashboard-item-right-bottom']}>            
                <CpChart timers={timers}/>
              </div>
            </div>
           
          </div>
        </div>
      </Content>
    </>
  );
};

const IncomeInspectionManagement = () => {
  const [isFullScreen, setIsFullScreen] = useState(false); // 是否全屏

  const topFilterFormDs = useMemo(() => new DataSet(topFilterFormDS()), []);

  const windowFullScreenChange = () => {
    if (document.fullscreenElement) {
      console.log('进入全屏');
      setIsFullScreen(true);
    } else {
      console.log('退出全屏');
      setIsFullScreen(false);
    }
  };
  useEffect(() => {
    document.addEventListener('fullscreenchange', windowFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', windowFullScreenChange);
    };
  }, []);

  return (    
    <>
      <div className={styles['screen-container']}>
        {isFullScreen ? (
          <FullScreenContainer>
            <Main
              topFilterFormDs={topFilterFormDs}
              isFullScreen={isFullScreen}
            />
          </FullScreenContainer>
        ) : (
          <Main
            topFilterFormDs={topFilterFormDs}
            isFullScreen={isFullScreen}
          />
        )}
      </div>
    </>
  );
};
export default IncomeInspectionManagement;
