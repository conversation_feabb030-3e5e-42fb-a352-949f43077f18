import intl from 'utils/intl';
import {FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import {getCurrentSiteInfo} from "@utils/utils";

const endUrl = '';
const modelPrompt = 'tarzan.inputOutputReport';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  primaryKey: 'dataRecordId',
  dataKey: 'content',
  totalKey: 'totalElements',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-input-product-report/list/ui`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      // required: true,
      dynamicProps: {
        defaultValue: () => {
          const  siteInfo = getCurrentSiteInfo();
          if (siteInfo.siteId) {
            return { ...siteInfo }
          }
          return undefined;
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'workshopLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaCode`).d('车间'),
      // required: true,
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.AREA',
      computedProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'areaCode',
      bind: 'workshopLov.areaCode',
    },
    {
      name: 'areaWorkOrder',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sapWorkOrder`).d('SAP工单'),
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
    },
    {
      name: 'areaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaCode`).d('车间'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sapWorkOrder`).d('SAP工单'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('品名编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('品名描述'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('工单数量'),
    },
    {
      name: 'sumAssembleQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumAssembleQty`).d('投入'),
    },
    {
      name: 'sumPrimaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumPrimaryUomQty`).d('产出'),
    },
    {
      name: 'rate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rate`).d('收率'),
    },
  ],
});

const drawerDS = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: false,
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sapWorkOrder`).d('SAP工单'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('投入物料'),
    },
    {
      name: 'assembleQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('投入重量'),
    },
    {
      name: 'type',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('类型'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumAssembleQty`).d('物料'),
    },
    {
      name: 'productionBatch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumPrimaryUomQty`).d('工艺批次'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rate`).d('产出重量'),
    },
  ],
});


export { tableDS, drawerDS };
