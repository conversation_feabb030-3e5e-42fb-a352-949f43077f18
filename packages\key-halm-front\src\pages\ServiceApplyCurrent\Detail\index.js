/**
 * ServiceApplyCurrent - 我的服务申请
 * @since 2020/12/03
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { Component } from 'react';
import { Bind } from 'lodash-decorators';
import { Row, Col, Icon, Spin, Steps } from 'choerodon-ui';
import {
  Lov,
  Form,
  Modal,
  Table,
  Switch,
  Button,
  DataSet,
  Output,
  TextField,
  DateTimePicker,
  TextArea,
} from 'choerodon-ui/pro';
import moment from 'moment';
import { connect } from 'dva';
import classNames from 'classnames';
import { isUndefined } from 'lodash';
import queryString from 'querystring';
import { routerRedux } from 'dva/router';
import { Header } from 'components/Page';

import notification from 'utils/notification';
import intl from 'utils/intl';
import request from 'utils/request';
import { HALM_MTC, HALM_MDM } from 'alm/utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentUserId, getCurrentOrganizationId } from 'utils/utils';

import { selectOrganizationEmployeeRelation } from 'alm/services/srService';
import OrgPartnerLov from 'alm/components/OrgPartnerLov';
import CommonComponent from 'alm/components/CommonComponent';
import { handlePlannerAndOwner } from 'alm/components/CommonComponent/utils/';
import { handleStaffCallBack } from 'alm/components/DefaultMaintainer/utils';
import ViewRender from 'alm/pages/ServiceApply/Detail/ViewRender';
import EditRender from 'alm/pages/ServiceApply/Detail/EditRender';

import { tableProps as priorityTableProps } from 'alm/components/PriorityLov/tableProps';
import HeaderButtons from '../../ServiceApply/Detail/HeaderButtons';
import { changeSrStatus, createSr, updateSr } from '../../ServiceApply/api';

import styles from '../../ServiceApply/index.module.less';
import { detailFormDS, malfunctionDS, locationModalDS } from '../Stores/detailDS';
import RefuseTip from '../../../assets/refuse-tip.png';

const prompt = 'amtc.serviceApply';
const promptCode = `${prompt}.model.serviceApply`;
@formatterCollections({
  code: ['alm.common', 'alm.component', 'amtc.serviceApply', 'amtc.srEvaluateTemp'],
})
@connect(({ serviceApply, loading }) => ({
  serviceApply,
  userId: getCurrentUserId(),
  tenantId: getCurrentOrganizationId(),
  loading: {
    saveLoading: loading.effects['serviceApply/saveData'],
  },
}))
export default class serviceApplyCurrent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      current: 0, // 步骤条进度
      isNew: false,
      editFlag: false,
      showFault: false, // 是否展示缺陷信息
      resetInfo: {}, // 数据还原（页面加载时的初始化值）
      mapAddress: '', // 地图地址
      failureRequiredFlag: false, // 是否需要故障登记
      evalHierarchyList: [], // 缺陷信息
      providedObjectCode: '', // 必须提供工作对象至
      manuallySpecifyFlag: false, // 是否手工指定
      queryDeatilLoading: false, // 详情加载loading
      itemProperty: {
        orgId: {
          disabled: true, // 需求组织
        },
      },
      handleRecord: {}, // 手动填写的负责人/计划员的值
      fileModuleId: '',
    };
    this.locTreeRef = React.createRef();
    this.detailFormDS = new DataSet(detailFormDS());
    this.malfunctionDS = new DataSet(malfunctionDS(this.handleChangeSelectFaultNode));
    this.locationModalDS = new DataSet(locationModalDS());
    this.handleStaffCallBackBind = handleStaffCallBack.bind(this);
  }

  componentDidMount() {
    this.initFormData();
  }

  componentDidUpdate(preProps) {
    this.handleCompUpdate(preProps);
  }

  @Bind
  handleCompUpdate(preProps) {
    const {
      match: {
        params: { srId },
      },
    } = preProps;
    if (!this.state.isNew && srId !== this.props.match.params.srId) {
      this.initFormData();
    }
  }

  /**
   * 初始化页面数据
   */
  @Bind()
  async initFormData() {
    const {
      match: {
        params: { srId },
      },
      location: { state },
      tenantId,
    } = this.props;

    if (isUndefined(srId)) {
      this.setState({ isNew: true, fileModuleId: Date.now() });
      if (state?.detail) {
        // 如果通过告警工作台创建， 手动查询默认职责负责人，设置服务区域disabled
        const newRecord = this.detailFormDS.loadData([
          { ...state.detail, srStatus: 'DRAFT', mapSourceCode: 'NO_DISPLAY' },
        ]);
        await handlePlannerAndOwner({
          queryData: { serviceType: 'SERVICE_REQUEST' },
          type: 'both',
          record: newRecord.current,
          handleRecord: {},
        });
        this.handleChangeDefaultItem({
          maintSite: {
            disabled: true,
          },
        });
      }

      Promise.all([
        this.getCurrentEmployee(),
        selectOrganizationEmployeeRelation({
          tenantId,
          childFlag: 'N',
        }),
      ]).then(res => {
        const data = {
          srStatus: 'DRAFT',
          mapSourceCode: 'NO_DISPLAY',
        };
        if (res[0] && !res[0].failed) {
          const currentEmployee = res[0];
          data.contactId = currentEmployee.employeeId;
          data.contactName = currentEmployee.employeeName;
          data.reporterId = currentEmployee.employeeId;
          data.reporterName = currentEmployee.employeeName;
          data.phone = currentEmployee.mobile;
        }
        if (res[1] && !res[1].failed) {
          const currentOrg = res[1][0];
          data.orgId = currentOrg.orgId;
          data.orgName = currentOrg.orgName;
          data.orgType = currentOrg.orgType;
          data.reportOrgId = currentOrg.orgId;
          data.reportOrgName = currentOrg.orgName;
          data.reportOrgType = currentOrg.orgType;
        }
        this.detailFormDS.loadData([
          {
            ...this.detailFormDS?.current?.toData(),
            ...data,
          },
        ]);
      });
    } else {
      this.getCurrentEmployee();
      if (state) {
        this.setState({ editFlag: state.isEdit });
      }
      this.fetchDetailInfo(srId);
    }
  }

  // 查询当前租户下的员工信息
  @Bind()
  getCurrentEmployee() {
    const { dispatch, tenantId } = this.props;
    return dispatch({
      type: 'serviceApply/getCurrentEmployee',
      payload: {
        tenantId,
      },
    });
  }

  /**
   * 查询申请单明细
   */
  @Bind()
  fetchDetailInfo(newSrId) {
    const { dispatch, tenantId } = this.props;
    const { resetInfo } = this.state;

    this.setState({ queryDeatilLoading: true });
    this.detailFormDS.setQueryParameter('srId', newSrId);
    this.detailFormDS
      .query()
      .then(res => {
        if (res) {
          if (res.manuallySpecifyFlag) {
            // 如果手工指定为1 则客户/需求组织可编辑
            this.handleChangeDefaultItem({
              orgId: {
                disabled: false,
              },
            });
          }
          // 设备存在
          if (res.assetId) {
            dispatch({
              type: 'serviceApply/queryEvalItem',
              payload: {
                assetId: res.assetId,
                tenantId,
              },
            }).then(res1 => {
              const flag = res1?.content?.length > 0;
              this.setState({
                showFault: flag,
                resetInfo: {
                  ...resetInfo,
                  showFault: flag,
                  evalItemId: res?.evalItemId,
                  evalItemName: res?.evalItemName,
                },
              });
            });
          }
          // 缺陷评估项存在
          if (res.evalItemId) {
            this.handleChangeRcAssesment(res.evalItemId);
          }
          // 位置存在
          if (res.assetLocationId) {
            request(`${HALM_MDM}/v1/${tenantId}/asset-locations/${res.assetLocationId}`, {
              method: 'GET',
            }).then(locRes => {
              if (locRes && locRes.regionIds) {
                this.setState({
                  isShowMapFlag: true,
                  mapAddress: locRes.regionName.split('/').join(''),
                });
              } else {
                this.setState({
                  isShowMapFlag: false,
                  mapAddress: '',
                });
              }
            });
          } else {
            this.setState({
              isShowMapFlag: false,
              mapAddress: '',
            });
          }
          this.setState({
            queryDeatilLoading: false,
            providedObjectCode: res.providedObjectCode,
            failureRequiredFlag: res.failureRequiredFlag,
            fileModuleId: res.fileModuleId,
          });
        }
      })
      .catch(() => {
        this.setState({ queryDeatilLoading: false });
        this.detailFormDS.loadData([{}]);
      });
  }

  /**
   * 报告人所在组织变化
   */
  @Bind()
  handleChangeReportOrg() {
    this.detailFormDS.current.set('reporterLov', null);
  }

  /**
   * 报告人改变
   * @param {*} value
   */
  @Bind
  handleChangeReporter(value) {
    if (value) {
      this.detailFormDS.current.set('reportOrgId', value?.orgId);
      this.detailFormDS.current.set('reportOrgName', value?.orgName);
    }
  }

  /**
   * 选中右侧故障集的行数据
   * @param record
   * @param selectFlag
   */
  @Bind()
  handleChangeSelectFaultNode(record, selectFlag) {
    const { evalHierarchyList } = this.state;
    const { parentCodeId } = record.data;
    if (selectFlag) {
      const parentRecord = this.malfunctionDS.find(
        malRecord => malRecord.data.asmtCodeId === parentCodeId
      );
      this.detailFormDS.current.set(`${evalHierarchyList[0].code}`, {
        asmtCodeId: parentCodeId || record.data.asmtCodeId,
        asmtCodeName: parentCodeId ? parentRecord.data.asmtCodeName : record.data.asmtCodeName,
      });
      this.detailFormDS.current.set(`${evalHierarchyList[1].code}`, {
        asmtCodeId: parentCodeId ? record.data.asmtCodeId : null,
        asmtCodeName: parentCodeId ? record.data.asmtCodeName : null,
      });
    } else {
      this.detailFormDS.current.set(`${evalHierarchyList[0].code}`, {
        asmtCodeId: null,
        asmtCodeName: null,
      });
      if (parentCodeId) {
        this.detailFormDS.current.set(`${evalHierarchyList[1].code}`, {
          asmtCodeId: null,
          asmtCodeName: null,
        });
      }
    }
  }

  @Bind()
  handleChangeShowFault(val) {
    this.setState({
      showFault: val,
    });
  }

  /**
   * 缺陷评估项改变
   */
  @Bind
  handleChangeRcAssesment(val) {
    const { dispatch, tenantId } = this.props;
    const { isNew } = this.state;
    this.setState({
      evalHierarchyList: [],
    });
    if (val) {
      if (isNew) {
        this.malfunctionDS.setQueryParameter('evalItemId', val);
        this.malfunctionDS.query();
      }

      dispatch({
        type: 'woMalfuction/getEvalHierarchyList',
        payload: { tenantId, evaluateId: val },
      }).then(res => {
        if (res) {
          const newList = res.map(item => {
            return {
              ...item,
              code: `${item.basicTypeCode.toLowerCase()}Code`,
              id: `${item.basicTypeCode.toLowerCase()}CodeId`,
              meaning: `${item.basicTypeCode.toLowerCase()}CodeMeaning`,
            };
          });

          // 刷新DS结构
          this.handleRefreshDetailDS(newList);

          this.setState({
            evalHierarchyList: newList,
          });
        } else {
          this.setState({
            evalHierarchyList: [],
          });
        }
      });
    }
    if (isNew && !val) {
      this.malfunctionDS.loadData([]);
    }
  }

  /**
   * 刷新detailFormDS结构
   */
  @Bind()
  handleRefreshDetailDS(newList) {
    // 保留原有数据
    const oldData = this.detailFormDS.toData();
    // 创建新DataSet
    this.detailFormDS = new DataSet(detailFormDS(newList));
    // 加载原有数据
    this.detailFormDS.loadData(oldData);
  }

  /**
   * 修改字段必输/可修改
   */
  @Bind()
  handleChangeDefaultItem(value) {
    let { itemProperty } = this.state;
    itemProperty = {
      ...itemProperty,
      ...value,
    };
    this.setState({ itemProperty });
  }

  /**
   * 表单字段变化
   */
  @Bind
  async handleChangeSetter(key, value, type) {
    const record = this.detailFormDS.current;

    if (key === 'srTypeLov') {
      this.handleChangeFailureRequiredFlag(value ? String(value.failureRequiredFlag) : false);
      this.handleChangeProObjCode(value?.providedObjectCode);
      this.setState({
        providedObjectCode: value ? value.providedObjectCode : null,
        failureRequiredFlag: value ? String(value.failureRequiredFlag) : false,
      });
      record.set('priorityId', value ? value.priorityId : null);
      record.set('priorityName', value ? value.priorityName : null);
      if (!value || !value.failureRequiredFlag) {
        // 清除故障信息
        record.set('evalItemLov', {
          evalItemId: null,
          evalItemName: null,
        });
        record.set('faultDate', null);
        record.set('failureRequiredFlag', false);
        this.handleChangeSetter('evalItemLov');
      }
      // 当禁止输入设备位置时需清空
      if (value?.providedObjectCode === 'NOT_ALLOW') {
        record.set('assetLocationId', null);
        record.set('assetLocationName', null);
        record.set('assetLov', null);
        this.handleChangeShowFault(false);
      }
    } else if (key === 'manuallySpecifyFlag') {
      const manuallySpecifyFlag = record.get(key);
      this.setState({ manuallySpecifyFlag });
      this.handleChangeDefaultItem({
        orgId: {
          disabled: manuallySpecifyFlag === 0,
        },
      });
      record.set('orgId', null);
      record.set('orgName', null);
      record.set('contactLov', {
        contactId: null,
        contactName: null,
        contactDesc: null,
      });
      record.set('phone', null);
      record.set('contactDesc', null);
    } else if (key === 'contactLov') {
      if (value) {
        record.set('orgId', value.orgId);
        record.set('orgName', value.orgName);
        record.set('phone', value.mobile);
      } else {
        record.set('orgId', null);
        record.set('orgName', null);
        record.set('phone', null);
      }
    } else if (key === 'orgId') {
      record.set('orgId', type === 'PLATFORM' ? value.unitId : value.orgId);
      record.set('orgName', type === 'PLATFORM' ? value.unitName : value.orgName);
      record.set('orgType', type);
    } else if (key === 'evalItemLov') {
      record.set('partCode', {
        asmtCodeId: null,
        asmtCodeName: null,
      });
      record.set('riskCode', {
        asmtCodeId: null,
        asmtCodeName: null,
      });
      this.handleChangeRcAssesment(record.get('evalItemId'));
    }
  }

  /**
   * 创建工单
   */
  @Bind()
  handleCreateWo() {
    const { tenantId, dispatch, serviceApply } = this.props;
    const { currentEmployee } = serviceApply;

    const detail = this.detailFormDS && this.detailFormDS.current && this.detailFormDS.current.data;
    const { srId } = detail;
    // 检验是否可创建工单避免重复创建
    dispatch({
      type: 'serviceApply/checkSrToCreateWo',
      payload: {
        srId,
      },
    }).then(result => {
      if (Object.keys(result).length === 0) {
        dispatch({
          type: 'serviceApply/fetchDetailInfo',
          payload: {
            tenantId,
            srId,
          },
        }).then(res => {
          detail.srNumber = res.srNumber;
          detail.plannerId = detail.plannerId ? detail.plannerId : currentEmployee.employeeId;
          detail.plannerName = detail.plannerName
            ? detail.plannerName
            : currentEmployee.employeeName;
          // 查询服务区域明细数据
          const queryMaintSiteUrl = `${HALM_MDM}/v1/${tenantId}/maint-sites`;
          request(`${queryMaintSiteUrl}/${res.maintSiteId}`, {
            method: 'GET',
            query: {
              tenantId,
              maintSiteId: res.maintSiteId,
            },
          }).then(res1 => {
            detail.woplannerPrioritySourceSr = res1.woplannerPrioritySourceSr; // WO签派员优先来源SR
            detail.sourceCode = 'WO';
            detail.sourceParamType = 'SR';
            detail.reportDate = moment(detail.reportDate).format('YYYY-MM-DD HH:mm:ss');
            detail.faultDate = moment(detail.faultDate).format('YYYY-MM-DD HH:mm:ss');
            dispatch(
              routerRedux.push({
                pathname: `/amtc/work-order/create`,
                search: queryString.stringify(detail),
              })
            );
          });
        });
      }
    });
  }

  @Bind
  handleCreateSub() {
    const { history } = this.props;
    const detailForm = this.detailFormDS?.current?.data;
    const {
      srId,
      srNumber,
      maintSiteId,
      maintSiteName,
      srName,
      description,
      assetId,
      assetNum,
      assetDesc,
      descAndLabel,
      assetLocationId,
      assetLocationName,
      locationDesc,
      reportOrgId,
      reportOrgName,
      reporterId,
      reporterName,
      reportDate,
      currencySymbol,
    } = detailForm;

    const detail = {
      maintSiteId,
      maintSiteName,
      woName: srName,
      description,
      assetId,
      assetNum,
      assetDesc,
      descAndLabel,
      assetLocationId,
      assetLocationName,
      locationDesc,
      reportOrgId,
      reportOrgName,
      reporterId,
      reporterName,
      reportDate,
      currencySymbol,
      sourceTypeCode: 'SR',
      sourceSrIds: JSON.stringify([srId]),
      sourceInfoDTOList: JSON.stringify([
        {
          sourceType: 'SR',
          sourceNum: srNumber,
          sourceName: srName,
        },
      ]),
    };
    history.push({
      pathname: `/amtc/sub-requisition/create`,
      state: {
        sourceParamType: 'SRC', // 标识为我的服务申请跳转过去的
      },
      query: detail,
    });
  }

  /**
   * 状态变更-取消
   */
  @Bind()
  handleChangeSrStatusOfCancel() {
    Modal.confirm({
      key: Modal.key(),
      title: intl.get(`${prompt}.view.message.detailLine.cancel`).d('是否取消该服务申请单？'),
      onOk: () => this.handleChangeSrStatus('CANCELED'),
    });
  }

  // 状态变更
  @Bind()
  async handleChangeSrStatus(targetSrStatus) {
    const detail = this.detailFormDS?.current?.data;
    const params = { ...detail };

    const res = await changeSrStatus(targetSrStatus, params);
    if (res && res.failed) {
      notification.warning({ message: res.message });
    } else {
      notification.success();
      this.fetchDetailInfo(res.srId);
    }
  }

  // 显示拒绝原因填写弹窗
  @Bind()
  openRefuseReasonModal() {
    Modal.open({
      key: Modal.key(),
      title: (
        <div className={styles['refuse-modal-title']}>
          <img src={RefuseTip} alt="" />
          <span>{intl.get(`${promptCode}.refuseReason`).d('拒绝的原因')}：</span>
        </div>
      ),
      closable: true,
      children: (
        <Row style={{ margin: '0 10px 10px 10px' }}>
          <Col span={24}>
            <TextArea dataSet={this.detailFormDS} name="denialReason" cols={90} rows={5} />
          </Col>
        </Row>
      ),
      onOk: async () => {
        this.handleChangeSrStatus('REJECTED');
      },
    });
  }

  /**
   * 保存
   */
  @Bind()
  async handleSave() {
    const { fileModuleId } = this.state;
    const {
      match: {
        params: { srId },
      },
      dispatch,
      tenantId,
    } = this.props;
    if (await this.detailFormDS.validate()) {
      const data = this.detailFormDS.current.toJSONData();
      const res = data.srId
        ? await updateSr({ tenantId, ...data })
        : await createSr({ tenantId, ...data, fileModuleId });

      if (res && !res.failed) {
        if (isUndefined(srId)) {
          // 这儿之前有个(srId && res.srId.toString() !== srId)判断 想不到是啥场景 暂时先删除
          dispatch(
            routerRedux.push({
              pathname: `/amtc/service-apply-current/detail/${res.srId}`,
            })
          );
        } else if (res?.srId) {
          this.setState({
            isNew: false,
            editFlag: false,
          });
          this.fetchDetailInfo(res.srId);
        }
      }
    }
  }

  /**
   * 编辑/取消
   */
  @Bind()
  handleEdit() {
    const { showFault, editFlag, resetInfo } = this.state;
    if (editFlag) {
      this.detailFormDS.reset();
      // 还原故障信息
      if (showFault !== resetInfo?.showFault) {
        this.setState({
          showFault: resetInfo.showFault,
        });
      }
    }
    this.setState({ editFlag: !editFlag });
  }

  /**
   * 上一步
   */
  prev() {
    const { current } = this.state;
    this.setState({ current: current - 1 });
  }

  /**
   * 下一步
   */
  async next() {
    const { current } = this.state;
    const record = this.detailFormDS.current;
    let continueToSign = true; // 是否可以继续下一步

    if (current === 0) {
      Promise.all([
        record?.getField('srTypeLov')?.checkValidity(),
        record?.getField('srName')?.checkValidity(),
        record?.getField('priorityName')?.checkValidity(),
        record?.getField('maintSiteLov')?.checkValidity(),
        record?.getField('assetLov')?.checkValidity(),
        record?.getField('assetLocationName')?.checkValidity(),
      ]).then(res => {
        continueToSign = res.findIndex(r => !r) === -1;
        if (continueToSign) {
          this.setState({ current: current + 1 });
        }
      });
    } else {
      this.setState({ current: current + 1 });
    }
  }

  // 点击展开
  @Bind()
  handleExpand(expanded, record) {
    const { tenantId } = this.props;
    // 判断节点是否异步加载子结点
    if (expanded && record.get('childFlag') && !record.children) {
      record.setState('loadding', true);

      // 查询其下全部子数据（不是子孙数据）
      const parentCodeId = record.get('asmtCodeId');

      // 查询下一级评估对象代码列表信息
      request(`${HALM_MTC}/v1/${tenantId}/asmt-codes/nextList/${parentCodeId}`, {
        method: 'GET',
        query: {
          tenantId,
          parentCodeId,
        },
      }).then(res => {
        const recordsChildren = res.map(item => {
          return {
            ...item,
            parentCodeId,
            children: item.lowerFlag === 'Y' ? [] : null,
            childFlag: item.lowerFlag === 'Y',
          };
        });
        this.malfunctionDS.data = [...this.malfunctionDS.toData(), ...recordsChildren];
        record.setState('loadding', false);
      });
    }
  }

  // icon 渲染问题， 首先判断record的值和自定义状态来判断出叶节点和父节点进行不同的渲染
  expandicon({ prefixCls, expanded, expandable, record, onExpand }) {
    if (!record.get('childFlag') || record.get('levelPath')) {
      // 子结点渲染-不存在子结点或为第二层
      return <span style={{ paddingLeft: '0.18rem' }} />;
    }
    if (record.getState('loadding') === true) {
      // 自定义状态渲染
      return <Spin tip="loding" delay={200} size="small" />;
    }
    const iconPrefixCls = `${prefixCls}-expand-icon`;
    const classString = classNames(iconPrefixCls, {
      [`${iconPrefixCls}-expanded`]: expanded,
    });
    return (
      <Icon
        type="baseline-arrow_right"
        className={classString}
        onClick={onExpand}
        tabIndex={expandable ? 0 : -1}
      />
    );
  }

  @Bind()
  handleMaintSiteCallBack() {
    this.setState({ handleRecord: {} });
  }

  /**
   * 故障（受工单类型影响）显示改变
   */
  @Bind()
  handleChangeFailureRequiredFlag(value) {
    this.setState({ failureRequiredFlag: value });
  }

  @Bind()
  handleChangeProObjCode(value) {
    this.setState({ providedObjectCode: value });
  }

  /**
   * 新建的渲染
   */
  @Bind()
  createRender() {
    const { loading, tenantId } = this.props;
    const {
      current,
      showFault,
      evalHierarchyList,
      providedObjectCode,
      failureRequiredFlag,
      manuallySpecifyFlag,
      itemProperty,
    } = this.state;

    const searchItem = {
      sourceParamType: 'SR',
    };

    const stepsAction = (
      <div className={styles['steps-action']}>
        {current > 0 && (
          <Button onClick={() => this.prev()}>
            {intl.get(`hzero.common.button.previous`).d('上一步')}
          </Button>
        )}
        {current < 2 && (
          <Button color="primary" onClick={() => this.next()}>
            {intl.get(`hzero.common.button.next`).d('下一步')}
          </Button>
        )}
        {current === 2 && (
          <Button
            color="primary"
            onClick={this.handleSave}
            loading={loading.saveLoading}
            waitType="throttle"
            wait={1000}
          >
            {intl.get(`hzero.common.button.save`).d('保存')}
          </Button>
        )}
      </div>
    );
    const steps = [
      {
        title: intl.get(`${prompt}.view.message.basicInfo`).d('基本信息'),
        key: 0,
        content: (
          <div className={styles['steps-content-one-col']}>
            <Form dataSet={this.detailFormDS}>
              <Lov
                name="srTypeLov"
                onChange={value => this.handleChangeSetter('srTypeLov', value)}
              />
              <TextField name="srName" />
              <CommonComponent
                name="maintSiteLov"
                dataSet={this.detailFormDS}
                moreProps={{
                  searchItem,
                  isNew: true,
                  serviceType: 'SERVICE_REQUEST',
                }}
                moreFun={{
                  onChangeShowFault: this.handleChangeShowFault,
                }}
                isEdit
                disabled={itemProperty?.maintSite?.disabled}
                callback={this.handleMaintSiteCallBack}
              />
              <Lov name="priorityLov" tableProps={priorityTableProps} />

              <CommonComponent
                name="assetLocationName"
                dataSet={this.detailFormDS}
                disabled={providedObjectCode === 'NOT_ALLOW'}
                moreProps={{
                  that: this,
                  tenantId,
                  searchItem,
                  locationModalDS: this.locationModalDS,
                  serviceType: 'SERVICE_REQUEST',
                  handleRecord: this.state.handleRecord,
                }}
                moreFun={{
                  onChangeShowFault: this.handleChangeShowFault,
                  onChangeRcAssesment: this.handleChangeRcAssesment,
                }}
                isEdit
              />
              <CommonComponent
                name="assetLov"
                dataSet={this.detailFormDS}
                moreProps={{
                  searchItem,
                  serviceType: 'SERVICE_REQUEST',
                  handleRecord: this.state.handleRecord,
                }}
                moreFun={{
                  onChangeShowFault: this.handleChangeShowFault,
                  onChangeRcAssesment: this.handleChangeRcAssesment,
                }}
                isEdit
              />
              <TextField name="locationDesc" />
            </Form>
            {stepsAction}
          </div>
        ),
      },
      {
        title: intl.get(`${prompt}.view.message.faultDesc`).d('故障描述'),
        key: 1,
        content:
          showFault && failureRequiredFlag === '1' ? (
            <div className={styles['steps-content-two-col']}>
              <Row type="flex">
                <Col span={12} className={styles['angle-wrapper']}>
                  <Form dataSet={this.detailFormDS}>
                    <Lov
                      name="evalItemLov"
                      onChange={() => this.handleChangeSetter('evalItemLov')}
                    />
                  </Form>
                  {evalHierarchyList.length > 0 ? (
                    <div className={styles['eval-hierarchy-list']}>
                      <Form dataSet={this.detailFormDS}>
                        <Output name={`${evalHierarchyList[0].code}`} />
                        {evalHierarchyList.length > 1 ? (
                          <Output name={`${evalHierarchyList[1].code}`} />
                        ) : null}
                      </Form>
                    </div>
                  ) : null}
                  <Form dataSet={this.detailFormDS}>
                    <DateTimePicker name="faultDate" />
                    <TextField name="description" />
                  </Form>
                </Col>
                <Col span={12}>
                  <Table
                    key="serviceApplyCurrentMalfunction"
                    customizedCode="AORI.SERVICE_APPLY_CURRENT.MALFUNCTION"
                    mode="tree"
                    expandIcon={this.expandicon}
                    dataSet={this.malfunctionDS}
                    className={styles['halm-tree-table']}
                    onExpand={this.handleExpand}
                    expandedRowRenderer={() => false}
                  >
                    <Table.Column name="asmtCodeName" />
                  </Table>
                </Col>
              </Row>
              {stepsAction}
            </div>
          ) : (
            <div className={styles['steps-content-one-col']}>
              <Form dataSet={this.detailFormDS}>
                <TextField name="description" />
              </Form>
              {stepsAction}
            </div>
          ),
      },
      {
        title: intl.get(`${prompt}.view.message.processingArrangement`).d('处理安排'),
        key: 2,
        content: (
          <div className={styles['steps-content-two-col']}>
            <Form dataSet={this.detailFormDS} columns={2}>
              {manuallySpecifyFlag ? (
                <TextField name="contactDesc" />
              ) : (
                <Lov
                  name="contactLov"
                  onChange={value => this.handleChangeSetter('contactLov', value)}
                />
              )}
              <Switch
                name="manuallySpecifyFlag"
                onChange={value => this.handleChangeSetter('manuallySpecifyFlag', value)}
              />
              <TextField name="phone" />
              <OrgPartnerLov
                name="orgName"
                handleOk={(value, type) => this.handleChangeSetter('orgId', value, type)}
                disabled={!manuallySpecifyFlag}
              />
              <CommonComponent
                name="plannerGroupName"
                isEdit
                dataSet={this.detailFormDS}
                moreProps={{
                  isNew: true,
                  handleRecord: this.state.handleRecord,
                  serviceType: 'SERVICE_REQUEST',
                }}
                callback={lovRecord => this.handleStaffCallBackBind(lovRecord, 'plannerGroup')}
              />
              {/* <CommonComponent
                name="plannerName"
                isEdit
                dataSet={this.detailFormDS}
                moreProps={{
                  isNew: true,
                  handleRecord: this.state.handleRecord,
                  serviceType: 'SERVICE_REQUEST',
                }}
                callback={lovRecord => this.handleStaffCallBackBind(lovRecord, 'planner')}
              /> */}
              <Lov name="reporterOrgLov" onChange={this.handleChangeReportOrg} />
              <Lov name="reporterLov" onChange={this.handleChangeReporter} />
            </Form>
            {stepsAction}
          </div>
        ),
      },
    ];

    return (
      <div className={styles['service-apply-content']}>
        <Steps current={current} size="large">
          {steps.map(item => (
            <Steps.Step title={item.title} key={`step_${item.key}`} />
          ))}
        </Steps>
        <div className={styles['steps-content']}>{steps[current].content}</div>
      </div>
    );
  }

  /**
   * 编辑的渲染
   */
  @Bind()
  editRender() {
    const { tenantId, serviceApply } = this.props;
    const {
      showFault,
      itemProperty,
      evalHierarchyList,
      providedObjectCode,
      failureRequiredFlag,
      fileModuleId,
    } = this.state;
    const { currentEmployee } = serviceApply;
    const { employeeId } = currentEmployee;

    const editProps = {
      employeeId,
      fileModuleId,
      that: this,
      tenantId,
      RefuseTip,
      showFault,
      itemProperty,
      evalHierarchyList,
      providedObjectCode,
      failureRequiredFlag,
      detailFormDS: this.detailFormDS,
      locationModalDS: this.locationModalDS,
      onChangeShowFault: this.handleChangeShowFault,
      onChangeRcAssesment: this.handleChangeRcAssesment,
      onChangeSetter: this.handleChangeSetter,
      onMaintSiteCallBack: this.handleMaintSiteCallBack,
      onChangeReporter: this.handleChangeReporter,
      onChangeReportOrg: this.handleChangeReportOrg,
    };

    return <EditRender {...editProps} />;
  }

  render() {
    const { serviceApply, loading, dispatch, history } = this.props;
    const {
      isNew,
      editFlag,
      showFault,
      mapAddress, // TODO
      isShowMapFlag,
      evalHierarchyList,
      queryDeatilLoading,
      failureRequiredFlag,
      fileModuleId,
    } = this.state;

    const { currentEmployee } = serviceApply;
    const { employeeId } = currentEmployee;

    const viewProps = {
      RefuseTip,
      dispatch,
      showFault,
      mapAddress,
      isShowMapFlag,
      evalHierarchyList,
      queryDeatilLoading,
      failureRequiredFlag,
      detailFormDS: this.detailFormDS,
      styles,
      employeeId,
      fileModuleId,
    };

    const headerButtonsProps = {
      isNew,
      isEdit: editFlag,
      history,
      dispatch,
      loading,
      sourcePage: 'SRC',
      employeeId,
      detailFormDS: this.detailFormDS,
      onSave: this.handleSave,
      onEdit: this.handleEdit,
      onCreateSub: this.handleCreateSub,
      onCreateWo: this.handleCreateWo,
      onChangeSrStatus: this.handleChangeSrStatus,
      onChangeSrStatusOfCancel: this.handleChangeSrStatusOfCancel,
      onOpenRefuseReasonModal: this.openRefuseReasonModal,
      onReload: this.fetchDetailInfo,
    };

    return (
      <>
        <Header
          title={intl.get(`${prompt}.view.title.serviceApplyCurrent`).d('我的服务申请')}
          onBack={this.handleBack}
          backPath="/amtc/service-apply-current/list"
        >
          <HeaderButtons {...headerButtonsProps} />
        </Header>
        {isNew ? this.createRender() : editFlag ? this.editRender() : <ViewRender {...viewProps} />}
      </>
    );
  }
}
