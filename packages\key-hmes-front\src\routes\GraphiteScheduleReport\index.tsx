import React, { useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { isNil } from 'lodash';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { API_HOST, BASIC } from '@utils/config';
import { tableDS } from './stories';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.event.graphiteScheduleReport';

const GraphiteScheduleReport = (props) => {

  const { tableDs } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'stoveCode',
        width: 150,
      },
      {
        name: 'equipmentCode',
        width: 150,
      },
      {
        name: 'equipmentName',
        width: 150,
      },
      {
        name: 'stoveCount',
        width: 120,
      },
      {
        name: 'materialCode',
        width: 120,
      },
      {
        name: 'materialName',
        width: 120,
      },
      {
        name: 'productionBatch',
        width: 120,
      },
      {
        name: 'layerMeaning',
        width: 120,
      },
      {
        name: 'sumWeight',
        width: 150,
      },
      {
        name: 'detailQty',
        width: 120,
      },
      {
        name: 'feedFlag',
        width: 120,
      },      
      {
        name: 'chargingDate',
        width: 150,
      },
      {
        name: 'powerDate',
        width: 150,
      },
      {
        name: 'dischargingDate',
        width: 150,
      },
      {
        name: 'qty',
        width: 150,
      },
      {
        name: 'sumNumber',
        width: 150,
      },
    ];
  }, []);

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return {
      ...queryParmas,
      eoIds: [tableDs.selected.map(item => item.get('eoId'))],
    }
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.mes.event.graphiteScheduleReport.title.list').d('石墨化装出炉进度表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/hme-graphite-schedule-report/excel/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="traphiteScheduleReport"
          customizedCode="traphiteScheduleReport"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.mes.event.graphiteScheduleReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(GraphiteScheduleReport),
);
