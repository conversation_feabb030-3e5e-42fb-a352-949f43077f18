.lineSideSilo {
  :global {
    .c7n-divider.c7n-divider-horizontal {
      margin: 0 0 !important;
    }

    .c7n-card-body {
      padding: 0 !important;
      background: #395470;
    }
    .c7n-pro-btn-wrapper{
      font-size: 16px !important;
    }
    .c7n-pro-input-number-wrapper.c7n-pro-input-number-wrapper label input, .c7n-pro-input-number-wrapper.c7n-pro-input-number-wrapper label > span{
      color: '#fff' !important;
      font-size: 16px !important;
    }
  }
}
.ProcessingCompleted {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content .c7n-pro-table-row {
      height: 45px !important;
    }

    .icon-refresh {
      background-color: rgb(91, 136, 160) !important;
      color: white !important;
    }
    // 表头
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content table .c7n-pro-table-tfoot tr th, .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content table .c7n-pro-table-thead tr th{
      background-color: #3c87ad !important;
      color: rgba(147, 203, 255, 1) !important;
      border: none !important;
      font-size: 17px !important;
    }

    // .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-cell {
    //   background:red !important;

    // }
    // 表格内容
    .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
      background-color: #3c87ad !important;
      color: white !important;
      font-size: 17px !important;
    }

    .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .c7n-pro-btn-wrapper {
      color: white !important;
      font-size: 16px !important;
    }

    .icon {
      color: white !important;
    }

    .c7n-pro-pagination-perpage {
      color: white !important;
    }

    .c7n-pro-pagination-page-info {
      color: white !important;
    }
  }
}
.materialContent{
  color: #fff;
  border-radius: 2px;
  height: 110px;
  margin: 5px 0;
  font-size: 16px;
  .materialName{
    background: rgba(69, 172, 241, 0.25);
    line-height: 110px;
    text-align: center;
    word-break: break-all;
  }
  .materialLot{
    height: 110px;
    background: rgba(69, 172, 241, 0.1);
    .materialLotContent{
      display: flex;
      height: 100%;
      padding: 5px;
      flex-direction: column;
      overflow: hidden;
      .materialLotContentBottom::-webkit-scrollbar{
        width: 0;
        height: 0;
      }
      .materialLotContentBottom{
        flex: 1;
        height: 100%;
        display: flex;
        margin-top: 5px;
        overflow-x: auto;
        .materialLotItem{
          img{
            position: absolute !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 30px;
            height: auto;
          }
          position: relative;
          display: inline-block;
          border: 1px solid rgba(0, 212, 205, 1);
          padding: 5px;
          border-radius: 5px;
          height: 100%;
          background: rgba(255, 255, 255, 0.15);
          margin-right: 10px;
          position: relative;
          .check{
            position: absolute;
            top: 0;
            left: 0;
            color: rgba(0, 212, 205, 1);
            background-color: rgba(0, 212, 205, 1);
          }
          .itemTop{
            padding-left: 10px;

            .lot{
              margin-right: 10px;
            }
            padding-bottom: 5px;
            display: flex;
            border-bottom: 1px solid rgba(255, 255, 255, 0.35);
            .c7n-pro-input-number-wrapper.c7n-pro-input-number-wrapper.c7n-pro-input-number-required-colors{
              :global{
                background-color: rgba(0, 212, 205, 1) !important;
              }
            }
          }
        }
      }
      .textAuto{
        color:rgba(112, 187, 243, 1);
        font-size: 16px;
      }
      .label{
        width: 35%;
        white-space:nowrap;
      }
      .textAutoValue{
        font-size: 14px;
        input{
          color:#fff !important;
        }
      }
    }
  }
}


// .titleMater {
//   flex-grow: 0;
//   flex-shrink: 0;
//   color: white;
//   background: #45acf140;
//   margin: 0.08rem;
//   width: 60px;
//   /* text-align: center; */
//   display: flex;
//   justify-content: center;
//   flex-wrap: wrap;
//   align-items: center;
//   flex-direction: column;

//   .Alternative {
//     background: #ffffff40;
//     border-radius: 10px;
//     padding: 3px;
//   }
// }

// .hzero-draggable-card-content {
//   background: #38708f !important;
// }

// .numberModal {
//   :global {
//     .c7n-pro-modal-header {
//       background-color: #3c87ad !important;
//       padding: 8px 16px 8px !important;
//     }

//     .c7n-pro-modal-title {
//       color: white !important;
//     }

//     .icon-close {
//       color: white !important;
//       top: 0 !important;
//     }
//   }
// }

// .modalForm {
//   :global {
//     .c7n-pro-field-label {
//       color: white !important;
//       font-size: 18px;
//     }

//     .c7n-pro-output {
//       color: white !important;
//     }

//     .c7n-pro-select-wrapper {
//       background: #50819c !important;
//       color: white !important;
//     }
//   }
// }

// .MachineMaterialsModal {
//   :global {
//     .c7n-pro-modal-header {
//       background-color: #3c87ad !important;
//       padding: 8px 16px 8px !important;
//     }

//     .c7n-pro-modal-title {
//       color: white !important;
//     }
//   }

//   .icon-refresh {
//     background-color: rgb(91, 136, 160) !important;
//     color: white !important;
//   }

//   .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-cell {
//     background-color: #3c87ad !important;
//     color: white !important;
//     border: none !important;
//   }

//   .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
//     background-color: #3c87ad !important;
//     color: white !important;
//   }

//   .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
//     background-color: #3c87ad !important;
//     color: white !important;
//   }

//   .c7n-pro-btn-wrapper {
//     background-color: #3c87ad !important;
//     color: white !important;
//   }

//   .icon {
//     color: white !important;
//   }

//   .c7n-pro-pagination-perpage {
//     color: white !important;
//   }

//   .c7n-pro-pagination-page-info {
//     color: white !important;
//   }
// }

// .laneTitle {
//   display: inline-flex;
//   width: 100%;

//   .materialInfo {
//     color: white;
//     margin-left: 10px;
//     flex-grow: 1;
//     width: 50%;
//     white-space: nowrap;
//     overflow: hidden;
//     text-overflow: ellipsis;
//   }

//   .numberInfo {
//     .materialInfo;
//     text-align: right;
//     margin-left: 0;
//   }
// }
