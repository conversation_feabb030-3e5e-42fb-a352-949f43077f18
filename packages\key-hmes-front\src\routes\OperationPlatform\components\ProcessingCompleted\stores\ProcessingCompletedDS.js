// 加工件（工单）DS
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.hmes.operationPlatform.ProcessingCompleted';

const detailDS = () => ({
  autoQuery: false,
  autoCreate: false,
  autoQueryAfterSubmit: false,
  paging: false,
  fields: [
    {
      name: 'serialNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.serialNumber`).d('序号'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('条码号'),
    },
    {
      name: 'productionBatch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionBatch`).d('工艺批次'),
    },
    {
      name: 'userName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.userName`).d('创建人'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.changeMaterialName`).d('转换物料品名'),
      lookupCode: 'HME.CHANGE_MATERIAL',
    },
  ],
});

const formDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  paging: false,
  fields:[
    {
      name: 'unitQty',
      type: 'number',
      label: intl.get(`${modelPrompt}.unitQty`).d('吨袋单位重量'),
    },
    {
      name: 'materialLotNum',
      type: 'number',
      required: true,
      precision: 0,
      min:0,
      label: intl.get(`${modelPrompt}.materialLotNum`).d('打包条码'),
    },
    {
      name: 'totalQty',
      type: 'number',
      precision: 0,
      min:0,
      label: intl.get(`${modelPrompt}.totalQty`).d('完工总量'),
    },
    {
      name: 'productionBatch',
      type: 'string',
      label: intl.get(`${modelPrompt}.productionBatch`).d('工艺批次'),
      validator: value => {
        const pattern = new RegExp('[\u4e00-\u9fa5\\（，。\\）]');
        if (pattern.test(value)) {
          return intl.get(`${modelPrompt}.not.ch`).d('不允许输入中文');
        }
        return true;
      },
    },
    {
      name: 'startTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.startTime`).d('开始时间'),
    },
    {
      name: 'endTime',
      type: 'string',
      label: intl.get(`${modelPrompt}.endTime`).d('结束时间'),
    },
    {
      name: 'startQty',
      type: 'number',
      min: 0,
      precision: 2,
      label: intl.get(`${modelPrompt}.startQty`).d('开始重量'),
    },
    {
      name: 'endQty',
      type: 'number',
      precision: 2,
      min: 'startQty',
      label: intl.get(`${modelPrompt}.endQty`).d('结束重量'),
    },
    {
      name: 'qty',
      type: 'number',
      precision: 2,
      min: 0,
      label: intl.get(`${modelPrompt}.qty`).d('报工数量'),
    },
    {
      name: 'reportingTotal',
      type: 'number',
      label: intl.get(`${modelPrompt}.reportingTotal`).d('报工总量'),
      precision: 6,
    },
    {
      name: 'changeMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.changeMaterialName`).d('转换物料品名'),
      lookupCode: 'HME.CHANGE_MATERIAL',
    },
    {
      name: 'changeMaterialCode',
      label: intl.get(`${modelPrompt}.changeMaterialCode`).d('转换物料编码'),
      type: FieldType.string,
    },
  ],
  events: {
    update: ({ name, value, dataSet, oldValue }) => {
      if (name === 'productionBatch' && value && value !== oldValue) {
        const productionBatchs = value.replace(/\s*/g,"");
        dataSet?.current?.set('productionBatch', productionBatchs);
      }
    },
  },
})

export { detailDS, formDS };
