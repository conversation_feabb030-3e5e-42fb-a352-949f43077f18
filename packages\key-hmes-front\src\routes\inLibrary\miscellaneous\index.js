/**
 * 杂项工作台-入口文件
 * @date 2023-1-3
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState, useMemo } from 'react';
import { DataSet, Table, Dropdown, Modal, Button } from 'choerodon-ui/pro';
import { <PERSON><PERSON>, <PERSON>u, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import ExcelExport from 'components/ExcelExport';
import { flow } from 'lodash';
import { observer } from 'mobx-react';

import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
// import FRPrintButton from '@components/tarzan-ui/FRPrintButton';
import myInstance from '@utils/myAxios';
import { BASIC, API_HOST } from '@utils/config';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import { queryMapIdpValue } from 'services/api';
import moment from 'moment';
import { TemplatePrintButton } from '../../../components/tarzan-ui';
import { headerTableDS, lineTableDS } from './stores/ListDS';
import styles from './index.module.less';
import MaterialLotDrawer from './MaterialLotDrawer';
import AppointMaterialLotPage from './AppointMaterialLot';
import { headDS, tableDS } from './stores/MaterialBatchDS';
import CreateMaterial from './CreateMaterialDrawer';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.receive.miscellaneous';
let modalAssembly;
let valueSet
let instructionType
let modalMaterial;
let headerResult;

// const lugeUrl = '-24175';
const lugeUrl = '';

const Order = observer(props => {
  const {
    headerTableDs,
    lineTableDs,
    match: { path },
    customizeTable,
  } = props;
  // 判断头搜索条件切换
  const [siteId, setSiteId] = useState();
  const [selectedStatus, setSelectedStatus] = useState(undefined);
  const [printIds, setPrintIds] = useState([]); // 头表格选择的id
  const [LengIds, setLengIds] = useState([]); // 行表格选择的id
  const [selectedItem, setSelectedItems] = useState([]);
  const [tableDsArr, setTableDsArr] = useState([]);
  const headDs = useMemo(() => new DataSet(headDS()), []); // 物料批头ds
  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 物料批行ds
  const [loading, setLoading] = useState(false);
  const [batchListsArr, setBatchListsArr] = useState([]);
  const [changeSaveLoading, setchangeSaveLoading] = useState(false);
  const [linechangeSaveLoading, setlinechangeSaveLoading] = useState(false);

  const { run: specialFlagSave } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/miscellaneous/head/main-interface/edit`,
      method: 'GET',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  const { run: weighbridgeSynchronizationApi, loading: weighbridgeSynchronizationLoading } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/miscellaneous/weighbridge/doc/sync`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );


  // 头选中行instructionDocType
  // DS事件监听
  useEffect(() => {

    listener(true);
    return function clean() {
      listener(false);
    };

  });
  // 创建多行物料批弹框内 物料批明细的选中事件监听
  useEffect(() => {
    for (let i = 0; i < selectedItem.length; i++) {
      tableDsArr[i].addEventListener('batchSelect', handleSelectLineTableArr);
      tableDsArr[i].addEventListener('batchUnSelect', handleSelectLineTableArr);
    }

    return () => {
      for (let i = 0; i < selectedItem.length; i++) {
        tableDsArr[i].removeEventListener('batchSelect', handleSelectLineTableArr);
        tableDsArr[i].removeEventListener('batchUnSelect', handleSelectLineTableArr);
      }
    };
  }, [tableDsArr]);
  useEffect(() => {
    queryMapIdpValue({
      mttrDension: 'MT.MISCELLANEOUS',
    }).then(res => {
      valueSet = res
    });
  })
  // 返回页面时恢复选中项和当前项状态
  useEffect(() => {
    if (props?.history?.action === 'PUSH') {
      headerTableDs.query(props.headerTableDs.currentPage);
      handleLineTableChange({
        dataSet: headerTableDs,
      });
    }
  }, []);
  // 创建多行物料批弹框内 物料批明细的选中事件，用来手机选中的物料批id(materialLotId)
  const handleSelectLineTableArr = () => {
    let selectedList = [];
    for (let i = 0; i < selectedItem.length; i++) {
      const selected = tableDsArr[i].selected.map(item => {
        return item.get('materialLotId');
      });
      selectedList = [...selectedList, ...selected];
    }
    updateModalTitle(selectedList);
  };
  const updateModalTitle = selected => {
    modalMaterial.update({
      title: createModalTitle(selected),
    });
  };
  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (headerTableDs.queryDataSet) {
      const handler = flag
        ? headerTableDs.queryDataSet.addEventListener
        : headerTableDs.queryDataSet.removeEventListener;
      handler.call(headerTableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
      handler.call(headerTableDs, 'batchSelect', handleLineTableChange);
      handler.call(headerTableDs, 'batchUnSelect', handleLineTableChange);
    }
    if (lineTableDs) {
      lineTableDs.addEventListener('batchSelect', handleSelectLineTable);
      lineTableDs.addEventListener('batchUnSelect', handleSelectLineTable);
    }
  };

  // 头搜索条件切换清空供应商地点
  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (data.siteId !== siteId) {
      setSiteId(data.siteId);
    }
    if (!data.instructionDocTypeObj) {
      record.set('instructionDocStatus', null);
    }
    if (!data.site) {
      record.set('warehouse', null);
    }
    if (!data.warehouse) {
      record.set('locator', null);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 列表刷新清除头单选状态
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      queryLineTable();
    }
  };

  // 行列表事件, 更新选中行数量
  const handleLineTableChange = ({ dataSet }) => {
    const _selectedStatus = [];
    const _printIds = [];
    const completedList = ['1_PROCESSING', '1_COMPLETED', '2_PROCESSING', 'COMPLETED'];
    dataSet.selected.forEach(item => {
      const instructionDocStatus = item?.data?.instructionDocStatus;
      _printIds.push(item?.data?.instructionDocId);
      if (completedList.indexOf(instructionDocStatus) > -1) {
        if (_selectedStatus.indexOf('COMPLETED') === -1) {
          _selectedStatus.push('COMPLETED');
        }
      } else if (_selectedStatus.indexOf(instructionDocStatus) === -1) {
        _selectedStatus.push(instructionDocStatus);
      }
    });
    setSelectedStatus(_selectedStatus.length === 1 ? _selectedStatus[0] : undefined);
    setPrintIds(_printIds);
  };

  const handleSelectLineTable = ({ dataSet }) => {
    const _setLengIds = []
    dataSet.selected.forEach((item) => {
      _setLengIds.push(item?.data?.instructionId)
    })
    setLengIds(_setLengIds)
    let noMaterialLot = false;
    // eslint-disable-next-line array-callback-return
    const selected = lineTableDs.selected.map(item => {
      // 非实物不可操作
      if (item.get('identifyType') !== 'MATERIAL_LOT') {
        noMaterialLot = true;
      }
      return item.get('instructionDocLineId');
    });
    if (noMaterialLot) {
      setSelectedItems([]);
    } else {
      setSelectedItems(selected);
    }
  }

  // 行列表数据查询
  const queryLineTable = data => {
    instructionType = data?.instructionDocType
    lineTableDs.setQueryParameter('instructionDocId', data?.instructionDocId);
    lineTableDs.setQueryParameter('instructionDocType', data?.instructionDocType);
    lineTableDs.setQueryParameter('workOrderId', data?.workOrderId);
    lineTableDs.setQueryParameter('prodLineId', data?.prodLineId);
    lineTableDs.setQueryParameter('materialId', data?.materialId);
    lineTableDs.setQueryParameter('revisionCode', data?.revisionCode);
    lineTableDs.query();
  };

  // 操作列渲染
  const optionRender = record => (
    <>
      <a
        style={{ marginRight: '8px' }}
        onClick={() => {
          handleMaterialLotDetail(record);
        }}
      >
        {intl.get(`${modelPrompt}.detail`).d('明细')}
      </a>
      <a
        disabled={!(!['CANCEL', 'CLOSED', 'COMPLETED'].includes(record?.toData().instructionStatus) && ["MATERIAL_LOT", ''].includes(record?.toData().identifyType))}
        onClick={() => handleAppointMaterialLot(record)}
      >
        {intl.get(`${modelPrompt}.create.materialLotAppoint`).d('指定物料批')}
      </a>
      <PermissionButton
        type="text"
        disabled={
          linechangeSaveLoading || record.get('instructionStatus') !== 'RELEASED' || record.get('permissionFlag') !== 'Y'
        }
        onClick={() => handleCancel(record)}
        loading={changeSaveLoading}
        permissionList={[
          {
            code: `${path}.button.edit`,
            type: 'button',
            meaning: '列表页-编辑新建删除复制按钮',
          },
        ]}
      >
        {intl.get(`${modelPrompt}.line.button.cancel`).d('行取消')}
      </PermissionButton>
    </>
  );

  // 组件信息
  const handleMaterialLotDetail = async record => {
    const recordDetail = record?.toData() || {};
    modalAssembly = Modal.open({
      title:
        recordDetail.identifyType === 'MATERIAL_LOT'
          ? intl.get(`${modelPrompt}.materialLotDetail`).d('物料批明细')
          : intl.get(`${modelPrompt}.materialDetail`).d('物料明细'),
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <MaterialLotDrawer
          record={{
            identifyType: recordDetail.identifyType,
            instructionId: recordDetail.instructionId,
            instructionDocType: headerTableDs.current.get('instructionDocType'),
          }}
          customizeTable={customizeTable}
        />
      ),
      footer: (
        <Button onClick={() => modalAssembly.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 打开指定物料批页面
  const handleAppointMaterialLot = async record => {
    const recordData = record.toData();
    const appointProps = {
      instructionDocId: headerTableDs?.current?.toData().instructionDocId,
      instructionDocType: headerTableDs?.current?.toData().instructionDocType,
      instructionId: recordData.instructionId,
      instructionDocLineId: recordData.instructionDocLineId,
      materialId: recordData.materialId,
      revisionCode: recordData.revisionCode,
      siteId: headerTableDs.current.toData().siteId,
      locatorId: recordData.locatorId || recordData.warehouseId,
      ownerType: recordData.fromOwnerType,
      ownerId: recordData.fromOwnerId,
      toleranceFlag: recordData.toleranceFlag,
      toleranceType: recordData.toleranceType,
      toleranceMaxValue: recordData.toleranceMaxValue,
      toleranceMinValue: recordData.toleranceMinValue,
      quantity: recordData.quantity,
    }
    if (appointProps.instructionId) {
      Modal.open({
        title: intl.get(`${modelPrompt}.AppointMaterialLotPage`).d('指定物料批'),
        key: Modal.key(),
        className: 'hmes-style-modal',
        style: {
          width: 1000,
        },
        maskClosable: true,
        destroyOnClose: true,
        drawer: true,
        closable: true,
        okButton: false,
        cancelButton: false,
        children: (
          <AppointMaterialLotPage
            appointProps={appointProps}
          // customizeTable={customizeTable}
          />
        ),
      });
    }
  };

  // 行取消
  const handleCancel = async record => {
    setlinechangeSaveLoading(true)
    let falg = true
    valueSet.mttrDension.map((res) => {
      if (instructionType === res.value) {
        if (res.description === 'EQUIPMENT') {
          falg = false
        }
      }
    })
    if (falg) {
      const _index = headerTableDs.currentIndex;
      return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/miscellaneous/line/cancel/ui`, {
        method: 'POST',
        body: {
          instructionId: record.get('instructionId'),
          businessType: 'CANCEL',
          instructionDocType: instructionType,
        },
      }).then(res => {
        if (res?.success) {
          headerTableDs.query(props.headerTableDs.currentPage).then(() => {
            headerTableDs.locate(_index);
          });
          lineTableDs.query(props.lineTableDs.currentPage);
          notification.success({
            message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
          });
        } else {
          notification.error({
            message: res?.message,
          });
        }
        setlinechangeSaveLoading(false)
      });
    }
    // 新逻辑
    const _index = headerTableDs.currentIndex;
    return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/miscellaneous/line/cancel/ui`, {
      method: 'POST',
      body: {
        instructionId: record.get('instructionId'),
        businessType: 'CANCEL',
        instructionDocType: instructionType,
      },
    }).then(res => {
      if (res?.success) {
        headerTableDs.query(props.headerTableDs.currentPage).then(() => {
          headerTableDs.locate(_index);
        });
        lineTableDs.query(props.lineTableDs.currentPage);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
      setlinechangeSaveLoading(false)
    });

  };


  const handleCancelList = (record) => {
    record.reset();
    record.setState('editing', false);
  };

  const handleSubmit = async (record) => {
    return specialFlagSave({
      params: {
        instructionDocId: record?.get('instructionDocId'),
        specialFlag: record?.get('specialFlag'),
        weighingQuantity: record?.get('weighingQuantity'),
        weighingTime: record?.get('weighingTime'),
        numberPlate: record?.get('numberPlate'),
      },
    }).then((res) => {
      if(res.success){
        notification.success({
          message: '操作成功',
        });
        headerTableDs.query()
        record.setState('editing', false);
      }
    })
  };

  const renderAction = (record) => {
    if(!record?.getState('editing')){
      return (
        <PermissionButton
          type="text"
          permissionList={[
            {
              code: `hzero.tarzan.les.in-library-management.miscellaneous.ps.header.list.button.edit`,
              type: 'button',
              meaning: '主页头表-编辑按钮',
            },
          ]}
          onClick={() => {
            record.setState('editing', true);
          }}
        >
          {intl.get('tarzan.common.button.edit').d('编辑')}
        </PermissionButton>
      )
    }
    return (
      <>
        <a onClick={() => handleCancelList(record)} style={{ marginRight: '0.1rem' }}>
          {intl.get('hzero.common.button.cancel').d('取消')}
        </a>
        <a onClick={() => handleSubmit(record)}>
          {intl.get('hzero.common.button.save').d('保存')}
        </a>
      </>
    )
  };


  // 头列表配置
  const headerTableColumns = [
    {
      name: 'instructionDocNum',
      width: 150,
      renderer: ({ record, value }) => {
        if (record.data.instructionDocStatus === 'RELEASED' && !['Y', 'P'].includes(record.get('flex')?.approveFlag)) {
          return (
            <a
              onClick={() => {
                props.history.push(
                  `/hwms/in-library/miscellaneous/detail/${record.data.instructionDocId}`,
                );
              }}
            >
              {value}
            </a>
          );
        }
        return value;
      },
      lock: 'left',
    },
    {
      name: 'instructionDocTypeDesc',
      width: 150,
    },
    {
      name: 'instructionDocStatusDesc',
      width: 80,
    },
    {
      name: 'approveFlagDesc',
      width: 80,
    },
    {
      name: 'siteCode',
      width: 120,
    },
    {
      name: 'customerCode',
      width: 120,
    },
    {
      name: 'customerName',
      width: 120,
    },
    {
      name: 'weighingQuantity',
      width: 120,
      editor: record => record?.getState('editing'),
    },
    {
      name: 'weighingTime',
      width: 120,
      editor: record => record?.getState('editing'),
    },
    {
      name: 'numberPlate',
      width: 120,
      editor: record => record?.getState('editing'),
    },
    {
      name: 'differentQuantity',
      width: 120,
    },
    {
      name: 'accountTypeDesc',
      width: 80,
    },
    {
      name: 'costcenterCode',
      width: 150,
    },
    {
      name: 'costcenterDesc',
      width: 150,
    },
    {
      name: 'costcenterCategoryDesc',
      width: 100,
    },
    {
      name: 'internalOrderCode',
      width: 150,
    },
    {
      name: 'internalOrderCodeDesc',
      width: 150,
    },
    {
      name: 'internalOrderCategoryDesc',
      width: 100,
    },
    {
      name: 'fixedAssets',
      width: 120,
    },
    {
      name: 'fixedAssetsDesc',
      width: 120,
    },
    {
      name: 'specialFlag',
      width: 140,
      editor: record => record?.getState('editing'),
    },
    {
      name: 'remark',
      width: 120,
    },
    {
      name: 'printTimes',
      width: 100,
    },
    {
      name: 'realName',
      width: 120,
    },
    {
      name: 'creationDate',
      align: 'center',
      width: 150,
    },
    {
      name: 'f',
      width: 100,
    },
    {
      name: 'lastUpdatedByName',
      width: 100,
    },
    {
      name: 'lastUpdateDate',
      width: 140,
      align: 'center',
    },
    {
      lock: 'right',
      width: 180,
      align: 'center',
      title: '操作',
      renderer: ({ record }) => renderAction(record),
    },
  ];

  // 行信息表配置
  const lineTableColumns = [
    {
      name: 'lineNumber',
      width: 60,
      lock: 'left',
    },
    {
      name: 'identifyType',
      width: 120,
      lock: 'left',
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        } if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    {
      name: 'materialCode',
      width: 140,
      lock: 'left',
    },
    {
      name: 'revisionCode',
      width: 80,
      lock: 'left',
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'quantity',
      width: 100,
    },
    {
      name: 'executedQty',
      width: 80,
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'instructionStatusDesc',
      width: 80,
    },
    {
      name: 'soNumber',
      width: 140,
    },
    {
      name: 'soLineNum',
      width: 100,
    },
    {
      name: 'warehouseCode',
      width: 140,
    },
    {
      name: 'locatorCode',
      width: 140,
    },
    {
      name: 'toleranceFlag',
      width: 80,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceTypeDesc',
      width: 100,
    },
    {
      name: 'toleranceMaxValue',
      width: 80,
    },
    {
      name: 'toleranceMinValue',
      width: 80,
    },
    {
      name: 'remark',
      width: 140,
    },
    {
      name: 'option',
      fixed: 'right',
      lock: 'right',
      width: 180,
      align: 'center',
      title: intl.get(`${modelPrompt}.option`).d('操作'),
      renderer: ({ record }) => optionRender(record),
    },
  ];

  const headerRowClick = record => {
    queryLineTable(record?.toData());
  };

  const clickMenu = async ({ key }) => {
    setchangeSaveLoading(true)
    // const examData = []
    // const lastData = []
    // console.log('值集数据', valueSet)
    // headerTableDs.selected?.forEach((res) => {
    //   console.log('勾选的数据', res)
    //   valueSet.mttrDension.map((item) => {
    //     if (res.data.instructionDocType === item.value) {
    //       console.log('筛选出的数据', item)
    //       if (item.description === 'EQUIPMENT') {
    //         //调用接口
    //         examData.push(res)
    //       } else {
    //         lastData.push(res)
    //       }
    //     }
    //   })
    // })
    // console.log('通过的数据', examData)
    // if (examData.length !== 0) {
    //   console.log('111', examData)
    //   //新增逻辑
    // }
    // if (examData.length !== 0) {
    //   console.log('222', lastData)
    //   const instructionDocIds =
    //     headerTableDs?.selected?.map(item => {
    //       return item?.get('instructionDocId');
    //     }) || [];

    //   return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/miscellaneous/state-change/ui`, {
    //     method: 'POST',
    //     body: {
    //       instructionDocIds,
    //       instructionDocStatus: key,
    //     },
    //   }).then(res => {
    //     if (res?.success) {
    //       headerTableDs.batchUnSelect(headerTableDs.selected);
    //       headerTableDs.clearCachedSelected();
    //       setSelectedStatus(undefined);
    //       setPrintIds([]);
    //       headerTableDs.query(props.headerTableDs.currentPage);
    //       notification.success({
    //         message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
    //       });
    //     } else {
    //       notification.error({
    //         message: res?.message,
    //       });
    //     }
    //   });
    // }
    const instructionDocIds =
      headerTableDs?.selected?.map(item => {
        return item?.get('instructionDocId');
      }) || [];

    return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/miscellaneous/state-change/ui`, {
      method: 'POST',
      body: {
        instructionDocIds,
        instructionDocStatus: key,
      },
    }).then(res => {
      setchangeSaveLoading(false)
      if (res?.success) {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        headerTableDs.clearCachedSelected();
        setSelectedStatus(undefined);
        setPrintIds([]);
        headerTableDs.query(props.headerTableDs.currentPage);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
        setchangeSaveLoading(false)
      }
    });
  };

  const createDelivery = () => {
    props.history.push(`/hwms/in-library/miscellaneous/detail/create`);
  };

  const onFieldEnterDown = () => {
    headerTableDs.query(props.headerTableDs.currentPage);
  }

  const examineAndApprove = async () => {
    let falge = false
    let falgData = {}

    headerTableDs.selected?.forEach((res) => {
      valueSet.mttrDension.map((item) => {
        if (res.data.instructionDocType === item.value) {
          if (item.description === 'COST' || item.description === 'INTERNAL') {
            // 调用接口
            falge = true
            falgData = res.data
          } else {
            notification.error({
              message: intl.get(`${modelPrompt}.operation.successa`).d('该单据类型不允许提交审批！'),
            });

          }
        }
      })
    })
    if (falge) {
      return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/miscellaneous/submit/ui`, {
        method: 'POST',
        body: falgData,
      }).then(res => {
        if (res?.success) {
          notification.success({
            message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
          });
        } else {
          notification.error({
            message: res?.message,
          });
        }
      });
    }
  }
  // 物料批弹窗上的删除按钮，用来删除与指令的关系
  const handleDelete = selected => {
    const data = {
      instructionDocLineId: headDs.toData()[0].deliverLineId,
      materialLotIdList: selected,
    };
    const url = `${API_HOST}${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/mt-delivery-doc/material-lot/remove/ui`;
    return myInstance.post(url, data).then(async res => {
      if (res?.data?.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
        const arr = [];
        // eslint-disable-next-line array-callback-return
        selectedItem.map(() => {
          arr.push(new DataSet(tableDS()));
        });
        setTableDsArr([...arr]);
        modalMaterial.update({
          children: (
            <CreateMaterial
              headDs={headDs}
              tableDs={tableDs}
              batchList={batchListsArr}
              resultData={headerResult}
              tableDsArr={arr}
              selectedItem={selectedItem}
              customizeTable={customizeTable}
            />
          ),
        });
        for (let i = 0; i < selectedItem.length; i++) {
          arr[i].setQueryParameter('instructionDocLineId', selectedItem[i]);
          arr[i].query();
        }
        headDs.setQueryParameter('instructionDocLineId', selectedItem.join(','));
        setLoading(true);
        await headDs.query();
        setLoading(false);
      } else {
        notification.error({
          message: res?.data?.message,
        });
      }
    });
  };
  // 创建物料批
  const handleEstablish = async () => {
    const validate = await headDs.validate();
    if (validate) {
      const data = {
        deliverHeaderId: headDs.toData()[0].deliverHeaderId,
        deliverNumber: headDs.toData()[0].deliverNumber,
        lineInfoList: headDs.toData(),
      };
      const url = `${API_HOST}${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/miscellaneous/material-lot/create/ui`;
      return myInstance.post(url, data).then(async res => {
        if (res?.data?.success) {
          notification.success({
            message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
          });
          const batchLists = res?.data?.rows;
          setBatchListsArr(batchLists);
          const arr = [];
          // eslint-disable-next-line array-callback-return
          selectedItem.map(() => {
            arr.push(new DataSet(tableDS()));
          });
          setTableDsArr([...arr]);
          modalMaterial.update({
            children: (
              <CreateMaterial
                headDs={headDs}
                tableDs={tableDs}
                batchList={batchLists}
                resultData={headerResult}
                tableDsArr={arr}
                selectedItem={selectedItem}
                customizeTable={customizeTable}
              />
            ),
          });
          for (let i = 0; i < selectedItem.length; i++) {
            arr[i].setQueryParameter('instructionDocLineId', selectedItem[i]);
            arr[i].query();
          }
          headDs.setQueryParameter('instructionDocLineId', selectedItem.join(','));
          setLoading(true);
          await headDs.query();
          setLoading(false);
        } else {
          notification.error({
            message: res?.data?.message,
          });
        }
      });
    }
  };
  const createModalTitle = (selected = []) => {
    return (
      <div>
        <span style={{ fontSize: '14px' }}>
          {intl.get(`${modelPrompt}.create.materialBatch`).d('创建物料批')}
        </span>
        <div
          style={{
            float: 'right',
            display: 'flex',
            flexDirection: 'row-reverse',
            alignItems: 'center',
            marginRight: '0.3rem',
          }}
        >
          <PermissionButton
            style={{
              marginLeft: '0.1rem',
              marginRight: '0.2rem',
            }}
            icon="save"
            type="c7n-pro"
            color={ButtonColor.primary}
            onClick={() => handleEstablish()}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.create.establish`).d('创建')}
          </PermissionButton>
          <PermissionButton
            style={{
              marginLeft: '0.1rem',
              marginRight: '0.2rem',
            }}
            icon="delete"
            type="c7n-pro"
            color={ButtonColor.default}
            onClick={() => handleDelete(selected)}
            disabled={selected.length === 0}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.delete').d('删除')}
          </PermissionButton>
          {/* <FRPrintButton
            kid="MATERIAL_LOT_PRINT" // 使用打印组件的功能的唯一标识，业务提供
            queryParams={selected} // 查询数据
            disabled={selected.length === 0}
            printObjectType="INSTRUCTION_DOC"
          /> */}
          <TemplatePrintButton
            disabled={!selected.length}
            printButtonCode='DELIVERY_DOC_IN'
            printParams={{ materialLotIdList: selected.join(',') }}
          />
        </div>
      </div>
    );
  };
  // 创建物料批
  const handleCreateMaterial = async () => {
    let falg = true
    valueSet.mttrDension.map((res) => {
      if (instructionType === res.value) {
        if (res.tag === 'RETURN') {
          if (res.description === 'SCRAP' || res.description === 'CUSTOMER') {
            falg = false
          }
        }
      }
    })
    if (!falg) {
      const arr = [];
      // eslint-disable-next-line array-callback-return
      selectedItem.map(() => {
        arr.push(new DataSet(tableDS()));
      });
      setTableDsArr([...arr]);
      if (selectedItem.length === 0) {
        notification.error({
          message: '非实物不可创建物料批',
        });
        return
      }
      headDs.setQueryParameter('instructionDocLineId', selectedItem.join(','));
      setLoading(true);
      const res = await headDs.query();
      headDs.records.forEach(item => item.set('productionDate', moment().format('YYYY-MM-DD HH:mm:ss')))
      setLoading(false);
      if (res.success) {
        headerResult = res.rows;
        modalMaterial = Modal.open({
          title: createModalTitle([]),
          className: 'hmes-style-modal',
          maskClosable: true,
          destroyOnClose: true,
          drawer: true,
          closable: true,
          onClose: () => {
            tableDs.batchUnSelect(tableDs.selected);
          },
          style: {
            width: 1080,
          },
          children: (
            <CreateMaterial
              headDs={headDs}
              tableDs={tableDs}
              batchList={[]}
              resultData={res.rows}
              tableDsArr={arr}
              selectedItem={selectedItem}
              customizeTable={customizeTable}
            />
          ),
          footer: (
            <Button onClick={() => modalMaterial.close()}>
              {intl.get('tarzan.common.button.back').d('返回')}
            </Button>
          ),
        });
      }
    } else {
      notification.error({
        message: intl.get(`${modelPrompt}.operation.successa`).d('该单据类型不允许创建物料批！'),
      });
    }

  };
  const extraButton = (
    <Button onClick={handleCreateMaterial} disabled={!LengIds.length} color="primary">
      {intl.get(`${modelPrompt}.createMaterialBatch`).d('创建物料批')}
    </Button>
  );

  const getExportQueryParams = () => {
    return {
      ...headerTableDs.queryDataSet.toJSONData()[0],
      instructionDocIds: headerTableDs.selected.map(item => item.get('instructionDocId')).join(','),
    }
  }


  const handleWeighbridgeSynchronization = () => {
    const synchronizationParams = { instructionDocIds: printIds };
    return weighbridgeSynchronizationApi({
      params: synchronizationParams,
    }).then((res) => {
      if(res.success){
        notification.success({
          message: '操作成功',
        });
        headerTableDs.query()
      }
    })
  }


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.miscellaneous`).d('杂项工作台')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={createDelivery}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.createDelivery`).d('创建单据')}
        </PermissionButton>
        <Dropdown
          overlay={
            <Menu onClick={clickMenu} className={styles['split-menu']}>
              {selectedStatus === 'RELEASED' && (
                <Menu.Item key="CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
                  </a>
                </Menu.Item>
              )}
              {(selectedStatus === 'COMPLETED' ||
                selectedStatus === 'PROCESSING' ||
                selectedStatus === '1_COMPLETED' ||
                selectedStatus === '1_PROCESSING' ||
                selectedStatus === '2_PROCESSING')
                && (
                  <Menu.Item key="CLOSED">
                    <a target="_blank" rel="noopener noreferrer">
                      {intl.get(`${modelPrompt}.button.close`).d('关闭')}
                    </a>
                  </Menu.Item>
                )}
            </Menu>
          }
          trigger={['click']}
          disabled={
            [
              'RELEASED',
              'COMPLETED',
              'PROCESSING',
              '1_COMPLETED',
              '1_PROCESSING',
              '2_PROCESSING',
            ].indexOf(selectedStatus) === -1
          }
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            loading={changeSaveLoading}
            disabled={
              [
                'RELEASED',
                'COMPLETED',
                'PROCESSING',
                '1_COMPLETED',
                '1_PROCESSING',
                '2_PROCESSING',
              ].indexOf(selectedStatus) === -1
            }
            permissionList={[
              {
                code: `miscellaneous.list.button.changeStatus`,
                type: 'button',
                meaning: '列表页-状态变更按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        {/* <FRPrintButton
          kid="MISCELLANEOUS_WORKBENCH"
          queryParams={printIds}
          disabled={!(printIds.length > 0)}
          printObjectType="INSTRUCTION_DOC"
        /> */}
        <TemplatePrintButton
          disabled={!printIds.length}
          printButtonCode='MISCELLANEOUS'
          printParams={{ docId: printIds.join(',') }}
        />
        <Button
          disabled={printIds.length !== 1}
          onClick={examineAndApprove}
        >提交审批</Button>
        <ExcelExport
          method="GET"
          requestUrl={`${
            BASIC.HMES_BASIC
          }/v1/${getCurrentOrganizationId()}/miscellaneous/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          otherButtonProps={{
            disabled: !headerTableDs.records.length,
          }}
        />
        <Button
          color="primary"
          disabled={headerTableDs.selected.length !== 1  || headerTableDs.selected[0].get('instructionDocStatus') !== 'RELEASED'}
          onClick={handleWeighbridgeSynchronization}
          loading={weighbridgeSynchronizationLoading}
        >
          {intl.get(`${modelPrompt}.select.weighbridgeSynchronization`).d('地磅同步')}
        </Button>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.HEAD`,
          },
          <Table
            searchCode="zxgzt1"
            customizedCode="zxgzt1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
              onFieldEnterDown,
            }}
            dataSet={headerTableDs}
            columns={headerTableColumns}
            highLightRow
            onRow={({ record }) => {
              return {
                onClick: () => {
                  headerRowClick(record);
                },
              };
            }}
          />,
        )}
        <Collapse bordered={false} collapsible="icon" defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            extra={extraButton}
            dataSet={lineTableDs}
          >
            {lineTableDs && (
              customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.LINE`,
                },
                <Table
                  customizedCode="zxgzt2"
                  className={styles['expand-table']}
                  dataSet={lineTableDs}
                  highLightRow={false}
                  columns={lineTableColumns}
                />,
              )
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
});

export default flow(
  formatterCollections({ code: ['tarzan.receive.miscellaneous', 'tarzan.common'] }),
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_MATERIAL_LOT.QUERY`,
    ],
  }),
)(Order);
