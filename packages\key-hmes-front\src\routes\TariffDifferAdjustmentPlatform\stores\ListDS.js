import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.tariffDifferAdjustmentPlatform';
const tenantId = getCurrentOrganizationId();
const endUrl = '';

const tableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: 'multiple',
  primaryKey: 'electricPriceSumIfaceId',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}${endUrl}/v1/${tenantId}/hme-electric-price-sum-ifaces/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.Tariff_Differ_Adjustment_Platform_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.Tariff_Differ_Adjustment_Platform_LIST.HEAD`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      name: 'dateTime',
      type: FieldType.month,
      label: intl.get(`${modelPrompt}.dateTime`).d('月份'),
      format: 'YYYY-MM',
    },
  ],
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderQty`).d('工单数量'),
    },
    {
      name: 'sumQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderQty`).d('订单产出数量'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'month',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.month`).d('月份'),
    },
    {
      name: 'powerStartDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.powerStartDate`).d('送电任务开始时间'),
    },
    {
      name: 'powerEndDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.powerEndDate`).d('送电任务结束时间'),
    },
    {
      name: 'equipmentCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentCode`).d('石墨化炉编码'),
    },
    {
      name: 'equipmentName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.equipmentName`).d('石墨化炉描述'),
    },
    {
      name: 'workcellCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellCode`).d('送电工位编码'),
    },
    {
      name: 'workcellName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workcellName`).d('送电工位描述'),
    },
    {
      name: 'stoveCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stoveCode`).d('装炉编码'),
    },
    {
      name: 'chargingDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.chargingDate`).d('装炉日期'),
    },
    {
      name: 'dischargingDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dischargingDate`).d('出炉日期'),
    },
    {
      name: 'sumNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumNumber`).d('装炉坩埚数'),
    },
    {
      name: 'sumWeight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumWeight`).d('装炉总重量'),
    },
    {
      name: 'stoveCount',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stoveCount`).d('炉使用次数'),
    },
    {
      name: 'tipWoValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.tipWoValue`).d('尖用电量'),
      precision: 3,
    },
    {
      name: 'peakWoValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.peakWoValue`).d('峰用电量'),
      precision: 3,
    },
    {
      name: 'valleyWoValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.valleyWoValue`).d('谷用电量'),
      precision: 3,
    },
    {
      name: 'levelWoValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.levelWoValue`).d('平用电量'),
      precision: 3,
    },
    {
      name: 'planElectricity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.planElectricity`).d('计划成本电费'),
      precision: 3,
    },
    {
      name: 'actualElectricity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.actualElectricity`).d('实际成本电费'),
      precision: 3,
    },
    {
      name: 'priceDifference',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.priceDifference`).d('差异费用'),
      precision: 3,
    },
    {
      name: 'difStatusMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.difStatusMeaning`).d('是否调整差异'),
    },
    {
      name: 'difMessage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.difMessage`).d('接口返回消息'),
    },
  ],
});


export { tableDS };
