
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const organizationId = getCurrentOrganizationId();
const API = `${BASIC.HMES_BASIC}`;
// const API = `/kd-mes-20000`;

const modelPrompt = 'tarzan.hmes.FurnaceLoadingDiagramPlatform';

const updateDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    selection: false,
  }
}

const tableDS = () => {
  return {
    autoQuery: true,
    autoCreate: false,
    selection: 'multiple',
    queryFields: [
      {
        name: 'equipmentCode',
        // required: true,
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('炉位编码'),
      },
      {
        name: 'templeLov',
        lovCode: 'KD.MES.STOVE_TEMPLATE',
        type: 'object',
        ignore: 'always',
        label: intl.get(`${modelPrompt}.templeLov`).d('装炉模板编码'),
      },
      {
        name: 'templateCode',
        bind: 'templeLov.templateCode',
      },
      {
        name: 'stoveTemplateHeadId',
        bind: 'templeLov.stoveTemplateHeadId',
      },

      {
        name: 'stoveCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.stoveCode`).d('装炉编码'),
      },
      {
        name: 'stoveLots',
        type: 'string',
        label: intl.get(`${modelPrompt}.stoveLotLov`).d('坩埚批次'),
      },
      {
        name: 'stoveMaterialCodes',
        lookupCode: 'HWM_USE_NUMBER',
        type: 'string',
        multiple: ',',
        textField: 'value',
        label: intl.get(`${modelPrompt}.stoveMaterialCodeLov`).d('坩埚物料编码'),
      },
      {
        name: 'statusList',
        lookupCode: 'HME.STOVE_STATUS',
        type: 'string',
        multiple: ',',
        label: intl.get(`${modelPrompt}.statusListLov`).d('装炉图状态'),
      },
      {
        name: 'releaseFlag',
        lookupCode: 'MT.FLAG',
        type: 'string',
        label: intl.get(`${modelPrompt}.releaseFlag`).d('是否已全部释放'),
      },
    ],
    fields: [
      {
        name: 'equipmentCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('炉位编码'),
      },
      {
        name: 'stoveCount',
        type: 'string',
        label: intl.get(`${modelPrompt}.stoveCount`).d('炉使用次数'),
      },

      {
        name: 'templateName',
        type: 'string',
        label: intl.get(`${modelPrompt}.templateName`).d('装炉模板名称'),
      },
      {
        name: 'stoveCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.stoveCode`).d('装炉编码'),
      },
      {
        name: 'operationName',
        type: 'string',
        label: intl.get(`${modelPrompt}.operationName`).d('工序'),
      },
      {
        name: 'statusMeaning',
        type: 'string',
        label: intl.get(`${modelPrompt}.statusMeaning`).d('状态'),
      },
      {
        name: 'chargingDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.chargingDate`).d('装炉日期'),
      },
      {
        name: 'dischargingDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.dischargingDate`).d('出炉日期'),
      },
      {
        name: 'chargingLayer',
        type: 'string',
        label: intl.get(`${modelPrompt}.chargingLayer`).d('装炉层数'),
      },
      {
        name: 'chargingMethodDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.chargingMethod`).d('装炉方式'),
      },
      {
        name: 'sumNumber',
        type: 'string',
        label: intl.get(`${modelPrompt}.sumNumber`).d('装炉坩埚数'),
      },
      {
        name: 'sumWeight',
        type: 'string',
        label: intl.get(`${modelPrompt}.sumWeight`).d('装炉重量'),
      },
      {
        name: 'userName',
        type: 'string',
        label: intl.get(`${modelPrompt}.userName`).d('制表'),
      },
      {
        name: 'releaseFlag',
        type: 'string',
        label: intl.get(`${modelPrompt}.releaseFlag`).d('是否已全部释放'),
      },
      {
        name: 'releaseQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.releaseQty`).d('现在已释放量'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/list/ui`,
          method: 'GET',
        };
      },
    },
  }
}

const crucibleDetailDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    selection: false,
    paging: false,
    fields: [
      {
        name: 'stoveLot',
        type: 'string',
        label: intl.get(`${modelPrompt}.stoveLot`).d('坩埚批次'),
      },
      {
        name: 'supplierName',
        type: 'string',
        label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('料号'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('坩埚物料描述'),
      },
      {
        name: 'stoveNumber',
        type: 'string',
        label: intl.get(`${modelPrompt}.stoveNumber`).d('数量'),
      },
      {
        name: 'releaseQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.releaseQty`).d('已释放数量'),
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/lot/detail/query`,
          method: 'GET',
        };
      },
    },
  }
}

const enterModalDS = () => {
  return {
    autoCreate: true,
    fields: [
      {
        name: 'equipmentCode',
        required: true,
        type: 'string',
        label: intl.get(`${modelPrompt}.equipmentCode`).d('炉位编码'),
      },
      {
        name: 'templeLov',
        lovCode: 'KD.MES.STOVE_TEMPLATE',
        required: true,
        ignore: 'always',
        type: 'object',
        label: intl.get(`${modelPrompt}.templeLov`).d('装炉模板编码'),
      },
      {
        name: 'stoveTemplateHeadId',
        bind: 'templeLov.stoveTemplateHeadId',
      },
    ],
  }
}

const detailDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    selection: false,
    paging: false,
    fields: [
      {
        name: 'sequence',
        type: 'string',
        label: intl.get(`${modelPrompt}.sequence`).d('序号'),
      },
      {
        name: 'backgroundColor',
        type: 'string',
        label: intl.get(`${modelPrompt}.backgroundColor`).d('型号颜色'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('料号'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('原料型号'),
      },
      {
        name: 'productionBatch',
        type: 'string',
        label: intl.get(`${modelPrompt}.productionBatch`).d('工艺批次'),
      },
      {
        name: 'remark',
        type: 'string',
        label: intl.get(`${modelPrompt}.remark`).d('加工地点'),
      },
      {
        name: 'stoveNumber',
        type: 'string',
        label: intl.get(`${modelPrompt}.stoveNumber`).d('点位数/坩锅数'),
      },
      {
        name: 'weight',
        type: 'number',
        label: intl.get(`${modelPrompt}.weight`).d('总重量'),
      },
      {
        name: 'aveNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.aveNum`).d('平均装锅'),
      },
      {
        name: 'carbonization',
        type: 'string',
        label: intl.get(`${modelPrompt}.carbonization`).d('是否碳化'),
        lookupCode: 'HME.STOVE_CARBONIZATION',
      },
    ],
    events: {
      update: ({ name, value, record }) => {
        if (name === 'weight') {
          if (value) {
            record?.set('aveNum', (value / record?.get('stoveNumber')).toFixed(2))
          } else {
            record?.set('aveNum', null)
          }
        }
      },
    },
  }
}

const remarkFormDS = () => {
  return {
    autoCreate: true,
    fields: [
      {
        name: 'remark',
        type: 'string',
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
    ],
  }
}

const materialDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    selection: false,
    paging: false,
    queryFields: [
      {
        name: 'locatorLov',
        required: true,
        type: 'object',
        label: intl.get(`${modelPrompt}.locatorLov`).d('线边仓库'),
        lovCode: 'HME.STOVE_LOCATOR',
        ignore: 'always',
      },
      {
        name: 'locatorId',
        bind: 'locatorLov.locatorId',
      },
      {
        name: 'materialIdLov',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialIdLov`).d('物料'),
        lovCode: 'MT.MATERIAL',
        ignore: 'always',
      },
      {
        name: 'materialId',
        bind: 'materialIdLov.materialId',
      },
      {
        name: 'productionBatch',
        label: intl.get(`${modelPrompt}.productionBatch`).d('工艺批次'),
      },
    ],
    fields: [
      {
        name: 'materialCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        type: 'string',
      },
      {
        name: 'materialName',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        type: 'string',
      },
      {
        name: 'productionBatch',
        label: intl.get(`${modelPrompt}.productionBatch`).d('工艺批次'),
        type: 'string',
      },
      {
        name: 'uomCode',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
        type: 'string',
      },
      {
        name: 'weight',
        label: intl.get(`${modelPrompt}.weight`).d('现有量'),
        type: 'number',
      },
      {
        name: 'qty',
        label: intl.get(`${modelPrompt}.qty`).d('投入量'),
        type: 'number',
        max: 'weight',
        min: 0,
        precision: 2,
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/inv/query`,
          method: 'POST',
          data,
        };
      },
    },
  }
}

const materialDetailDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    selection: 'multiple',
    // paging: false,
    cacheSelection: true,
    primaryKey: 'materialLotId',
    dataKey: 'content',
    totalKey: 'totalElements',
    modifiedCheck: false,
    queryFields: [
      {
        name: 'materialLotCode',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
        type: 'string',
      },
    ],
    fields: [
      {
        name: 'materialLotCode',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
        type: 'string',
      },
      {
        name: 'uomCode',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
        type: 'string',
      },
      {
        name: 'weight',
        label: intl.get(`${modelPrompt}.weight`).d('现有量'),
        type: 'number',
      },
      {
        name: 'qty',
        label: intl.get(`${modelPrompt}.qty`).d('投入量'),
        type: 'number',
        max: 'weight',
        min: 0,
        precision: 2,
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/inv/lot/query`,
          method: 'POST',
          data,
        };
      },
    },
  }
}

const addMaterialDetailDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    selection: 'multiple',
    paging: false,
    queryFields: [
      {
        name: 'materialLotCode',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
        type: 'string',
      },
    ],
    fields: [
      {
        name: 'materialLotCode',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
        type: 'string',
      },
      {
        name: 'uomCode',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
        type: 'string',
      },
      {
        name: 'weight',
        label: intl.get(`${modelPrompt}.weight`).d('现有量'),
        type: 'number',
      },
      {
        name: 'qty',
        label: intl.get(`${modelPrompt}.qty`).d('投入量'),
        type: 'number',
        max: 'weight',
        min: 0,
        precision: 2,
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/inv/lot/query`,
          method: 'POST',
          data,
        };
      },
    },
  }
}

const lotDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    selection: 'single',
    paging: false,
    queryFields: [
      {
        name: 'locatorLov',
        type: 'object',
        ignore: 'always',
        required: true,
        label: intl.get(`${modelPrompt}.locatorLov`).d('线边仓库'),
        lovCode: 'HME.STOVE_LOCATOR',
      },
      {
        name: 'locatorId',
        bind: 'locatorLov.locatorId',
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
        lookupCode: 'HWM_CRUCIBLE_MATERIAL',
        lovPara: {
          tenantId: organizationId,
        },
      },
      {
        name: 'supplierName',
        label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
        type: 'string',
      },
      // {
      //   name: 'materialLov',
      //   type: 'object',
      //   ignore: 'always',
      //   label: intl.get(`${modelPrompt}.materialLov`).d('物料'),
      //   lovCode: 'HWM_CRUCIBLE_MATERIAL',
      // },{
      //   name: 'materialId',
      //   bind: 'materialLov.materialId',
      // },
    ],
    fields: [
      {
        name: 'materialCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        type: 'string',
      },
      {
        name: 'materialName',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        type: 'string',
      },
      {
        name: 'lotCode',
        label: intl.get(`${modelPrompt}.lotCode`).d('坩埚批次'),
        type: 'string',
      },
      {
        name: 'supplierName',
        label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
        type: 'string',
      },
      {
        name: 'uomCode',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
        type: 'string',
      },
      {
        name: 'totalQty',
        label: intl.get(`${modelPrompt}.able.qty`).d('总套数'),
        type: 'number',
      },
      {
        name: 'qty',
        label: intl.get(`${modelPrompt}.able.qty`).d('可用套数'),
        type: 'number',
      },
      {
        name: 'frequency',
        label: intl.get(`${modelPrompt}.frequency`).d('次数库存'),
        type: 'number',
        defaultValue: 'qty' * 6,
      },
      {
        name: 'creationDate',
        label: intl.get(`${modelPrompt}.usage.creationDate`).d('批次创建时间'),
        type: 'string',
      },

    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/stove/lot/query`,
          method: 'POST',
          data,
        };
      },
    },
  }
}

const stoveDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    selection: false,
    paging: false,
    queryFields: [],
    fields: [
      {
        name: 'stoveLot',
        label: intl.get(`${modelPrompt}.stoveLot`).d('坩锅批次'),
        type: 'string',
      },
      {
        name: 'supplierName',
        label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
        type: 'string',
      },
      {
        name: 'materialCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('料号'),
        type: 'string',
      },
      {
        name: 'materialName',
        label: intl.get(`${modelPrompt}.materialName`).d('坩埚物料描述'),
        type: 'string',
      },
      {
        name: 'stoveNumber',
        label: intl.get(`${modelPrompt}.stoveNumber`).d('数量'),
        type: 'number',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/stove/lot/qty/query`,
          method: 'GET',
        };
      },
    },
  }
}

const submitDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    selection: 'multiple',
    paging: false,
    fields: [
      {
        name: 'materialCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('原材料物料编码'),
        type: 'string',
      },
      {
        name: 'productionBatch',
        label: intl.get(`${modelPrompt}.submitDs`).d('工艺批次'),
        type: 'string',
      },
      {
        name: 'materialName',
        label: intl.get(`${modelPrompt}.materialName`).d('原材料物料名称'),
        type: 'string',
      },
      {
        name: 'weight',
        label: intl.get(`${modelPrompt}.weight`).d('原材料数量'),
        type: 'number',
      },
      {
        name: 'finalMaterialCode',
        label: intl.get(`${modelPrompt}.finalMaterialCode`).d('成品物料编码'),
        type: 'string',
      },
      {
        name: 'finalMaterialName',
        label: intl.get(`${modelPrompt}.finalMaterialName`).d('成品物料描述'),
        type: 'string',
      },
      {
        name: 'finalWeight',
        label: intl.get(`${modelPrompt}.finalWeight`).d('成品现有量'),
        type: 'string',
      },
    ],
    transport: {
      read: () => {
        return {
          url: `${API}/v1/${getCurrentOrganizationId()}/hme-stove-heads/final/material/query`,
          method: 'GET',
        };
      },
    },
  }
}
export { submitDS, stoveDS, lotDS, materialDS, materialDetailDS, addMaterialDetailDS, remarkFormDS, enterModalDS, tableDS, detailDS, updateDS, crucibleDetailDS };

