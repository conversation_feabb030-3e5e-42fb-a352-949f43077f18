
/* eslint-disable jsx-a11y/alt-text */
// 新加工件
import React, { useEffect, useMemo } from 'react';
import { Form, DataSet, Output, Lov, Table, Modal } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from '@components/tarzan-hooks';
import intl from 'utils/intl';
import { connect } from 'dva';
import { formDS, processWoDS, tableDS } from './stores/BatchProductionOrderDS';
import { CardLayout } from '../commonComponents';
import { QueryWorkorder, QueryWorkcell } from './services';
import { namespace } from '../../model';
import styles from './index.modules.less';

const modelPrompt = 'tarzan.hmes.operationPlatform.batchProductionOrder';

const BatchProductionOrder = props => {
  const processWoDs = useMemo(
    () =>
      new DataSet({
        ...processWoDS(),
      }),
    [],
  );

  const formDs = useMemo(
    () =>
      new DataSet({
        ...formDS(),
      }),
    [],
  );
  const tableDs = useMemo(
    () =>
      new DataSet({
        ...tableDS(),
      }),
    [],
  );
  const { run: queryWorkcell, loading: queryWorkcellLoading } = useRequest(QueryWorkcell(), {
    manual: true,
    needPromise: true,
  });
  const { run: queryWorkorder, loading: queryWorkorderLoading } = useRequest(QueryWorkorder(), {
    manual: true,
    needPromise: true,
  });

  const { modelState = {} } = props;
  const { refreshEoData = [] } = modelState;

  useEffect(() => {
    if(refreshEoData){
      queryWorkInfo();
      props.handleRefresh({
        refreshEoData: false,
      })
    }
  }, [refreshEoData]);

  useEffect(() => {
    queryWorkInfo()
  }, [])

  useEffect(() => {
    if (props.loginWkcInfo.productionLineId) {
      formDs.loadData([]);
      processWoDs.current.getField('processLov').set('lovPara', {
        productionLineId: props.loginWkcInfo.productionLineId,
      });
    }
  }, [
    props.loginWkcInfo.productionLineId,
  ]);
  // 查询在制品
  const queryWorkInfo = async () => {
    const res = await queryWorkcell({
      params: {
        workStationId: props.loginWkcInfo.workStationId,
      },
    })
    if(res&&res.failed){
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'ERROR',
        recordType: 'query',
        message: res.message,
      });
    }else{
      props.handleAddRecords({
        cardId: props.cardId,
        messageType: 'SUCCESS',
        recordType: 'query',
        message: '查询成功',
      });
      const woData = {
        ...res,
        productionLineName:props.loginWkcInfo.productionLineName,
        locatorCode:props.loginWkcInfo.locatorCode,
        locatorName:props.loginWkcInfo.locatorName,
        operationName:props.loginWkcInfo.operationName,
        description:props.loginWkcInfo.operationDTOList[0].description,
        locatorId: props.loginWkcInfo.locatorId,
        workStationId: props.loginWkcInfo.workStationId,
        operationId: props.loginWkcInfo.operationDTOList[0].operationId,
        eoNum: res.eo.eoNum,
      }
      processWoDs.current.set('processLov', {
        workOrderId: woData.workOrderId,
        workOrderNum: woData.workOrderNum,
      })
      formDs.loadData([woData]);
      props.changeEoData(woData);
    }
  }
  const columns = [
    {
      name: 'eoNum',
    },
    {
      name: 'qty',
    },
    {
      name: 'boilerNumber',
    },
  ]

  // 扫描在制品
  const onFetchProcessed = async (value, list) => {
    if(value){
      let eoList = null
      const res = await queryWorkorder({
        params: {
          eoList: list,
          workOrderId: value.workOrderId,
          workStationId: props.loginWkcInfo.workStationId,
          operationId: props.loginWkcInfo.operationDTOList[0].operationId,
        },
      })
      if(res&&res.failed){
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'ERROR',
          recordType: 'query',
          message: res.message,
        });
        processWoDs.reset();
        if (props.loginWkcInfo.productionLineId) {
          processWoDs.current.getField('processLov').set('lovPara', {
            productionLineId: props.loginWkcInfo.productionLineId,
          });
        }
      }else{
        props.handleAddRecords({
          cardId: props.cardId,
          messageType: 'SUCCESS',
          recordType: 'query',
          message: '查询成功',
        });
        if(res?.eoList?.length > 1){
          tableDs.loadData(res.eoList);
          // 弹框
          Modal.open({
            key: 'BatchProductionOrder',
            title: '请选择',
            className: styles.BatchProductionOrder,
            children: (<Table columns={columns} dataSet={tableDs} />),
            style: {
              width: '50%',
            },
            contentStyle: { backgroundColor: '#3c87ad' },
            closable: false,
            cancelButton: false,
            onOk: async () => {
              if(tableDs.selected.length === 0)return false
              eoList = [tableDs.selected[0].data]
              const result = await queryWorkorder({
                params: {
                  ...res,
                  eoList,
                  workStationId: props.loginWkcInfo.workStationId,
                  operationId: props.loginWkcInfo.operationDTOList[0].operationId,
                },
              })
              if(result&&result.failed){
                props.handleAddRecords({
                  cardId: props.cardId,
                  messageType: 'ERROR',
                  recordType: 'query',
                  message: result.message,
                });
              }else{
                const woData = {
                  ...result,
                  // workOrderId: value.workOrderId,
                  // workOrderNum: value.workOrderNum,
                  // workOrderType: value.workOrderType,
                  productionLineName:props.loginWkcInfo.productionLineName,
                  locatorCode:props.loginWkcInfo.locatorCode,
                  locatorName:props.loginWkcInfo.locatorName,
                  operationName:props.loginWkcInfo.operationName,
                  description:props.loginWkcInfo.operationDTOList[0].description,
                  locatorId: props.loginWkcInfo.locatorId,
                  workStationId: props.loginWkcInfo.workStationId,
                  operationId: props.loginWkcInfo.operationDTOList[0].operationId,
                  eoNum: result.eo.eoNum,
                }
                formDs.loadData([woData]);
                props.changeEoData(woData);
              }
              return true;
            },
          })
        }else{
          eoList = res.eoList;
          const woData = {
            ...res,
            eoList,
            workOrderId: value.workOrderId,
            workOrderNum: value.workOrderNum,
            workOrderType: value.workOrderType,
            productionLineName:props.loginWkcInfo.productionLineName,
            locatorCode:props.loginWkcInfo.locatorCode,
            locatorName:props.loginWkcInfo.locatorName,
            operationName:props.loginWkcInfo.operationName,
            description:props.loginWkcInfo.operationDTOList[0].description,
            locatorId: props.loginWkcInfo.locatorId,
            workStationId: props.loginWkcInfo.workStationId,
            operationId: props.loginWkcInfo.operationDTOList[0].operationId,
            eoNum: res.eo.eoNum,
          }
          formDs.loadData([woData]);
          props.changeEoData(woData);
        }
      }
    }else{
      formDs.loadData([]);
      props.changeEoData({});
    }
  };

  return (
    <CardLayout.Layout spinning={queryWorkorderLoading||queryWorkcellLoading}>
      <CardLayout.Header
        title={intl.get(`${modelPrompt}.batchProductionOrder`).d('批次生产单')}
        help={props?.cardUsage?.meaning}
        content={
          <Lov
            dataSet={processWoDs}
            // id={`operationPlatformInput${props.priorityLayout?.filter(item => item.i === '25')[0]?.priority}`}
            name="processLov"
            onChange={value => onFetchProcessed(value)}
            modalProps={{
              contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
              className: styles.BatchProductionOrder,
              okProps: {
                style: {
                  background: '#1b7efc',
                },
              },
              cancelProps: {
                style: {
                  background: '#50819c',
                  color: 'white',
                },
              },
            }}
          />
        }
      />
      <CardLayout.Content>
        <Form labelWidth={150} className={styles.BatchProductionOrder} dataSet={formDs} labelAlign="right" labelLayout="horizontal">
          <Output name="eoNum" />
          <Output name="materialCode" />
          <Output name="materialName" />
          <Output name="productionLineName" />
          <Output name="locatorName"/>
          <Output name="description" />
          <Output name="workOrderNum" />
          <Output name="sapWorkOrderNum" />
          <Output name="qty"
            renderer={({ value }) => (
              <span style={{ color: 'rgba(0, 212, 205, 1)', fontSize: '24px'}}>
                {value}{value?`kg`:''}
              </span>
            )} />
        </Form>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['tarzan.hmes.operationPlatform.batchProductionOrder','model.org.monitor'] })(
  connect((state) => {
    return {
      modelState: state[namespace],
    };
  })(BatchProductionOrder));
