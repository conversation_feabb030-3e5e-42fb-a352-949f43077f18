import intl from 'utils/intl';
import {FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import {getCurrentSiteInfo} from "@utils/utils";
import notification from "hzero-front/lib/utils/notification";
import moment from 'moment';


const endUrl = '';
const modelPrompt = 'tarzan.CarburantDetectionReport';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}${endUrl}/v1/${tenantId}/mt-ztj-detect/list/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          const newContent = (rows?.content || [])?.map((item) => {
            const inspectItemFields = {};
            (item?.inspectItems || [])?.forEach((i) => {
              inspectItemFields[i.inspectItemId] = i.inspectValue;
            })
            return {
              ...item,
              ...inspectItemFields,
            }
          });
          return {
            ...rows,
            content: newContent,
          };
        },
      };
    },
  },
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteName',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
        enableFlag: 'Y',
      },
      dynamicProps: {
        defaultValue: () => {
          const  siteInfo = getCurrentSiteInfo();
          if (siteInfo.siteId) {
            return { ...siteInfo }
          }
          return undefined;
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'materialLotObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.samplingNumber`).d('取样编号'),
      lovCode: 'MT.MATERIAL_LOT',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
    {
      name: 'materialLotIds',
      type: FieldType.string,
      bind: 'materialLotObj.materialLotId',
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialLotCodes',
      type: FieldType.string,
      bind: 'materialLotObj.materialLotCode',
    },
    {
      name: 'productionBatch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionBatch`).d('出料口'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('型号'),
    },
    {
      name: 'packagingMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packagingMethod`).d('包装方式'),
    },
    {
      name: 'startDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.samplingDateFrom`).d('取样日期从'),
      max: 'endDate',
    },
    {
      name: 'endDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.samplingDateTo`).d('取样日期至'),
      min: 'startDate',
    },
    {
      name: 'startDateTime',
      type: FieldType.time,
      label: intl.get(`${modelPrompt}.samplingTimeFrom`).d('取样时间从'),
      max: 'endDateTime',
      transformRequest: (value) => value ? moment(value).format('HH:mm:ss') : null,
    },
    {
      name: 'endDateTime',
      type: FieldType.time,
      label: intl.get(`${modelPrompt}.samplingTimeTo`).d('取样时间至'),
      min: 'startDateTime',
      transformRequest: (value) => value ? moment(value).format('HH:mm:ss') : null,
    },
    {
      name: 'lastInspectResult',
      type: FieldType.string,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      label: intl.get(`${modelPrompt}.lastInspectResult`).d('判定结果'),
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteName`).d('站点名称'),
    },
    {
      name: 'sourceObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingNumber`).d('取样编号'),
    },
    {
      name: 'productionBatch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionBatch`).d('出料口'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingDate`).d('取样日期'),
    },
    {
      name: 'creationDateTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.samplingTime`).d('取样时间'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('型号'),
    },
    {
      name: 'packagingMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packagingMethod`).d('包装方式'),
    },
    {
      name: 'okJudgement',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.okJudgement`).d('合格判定'),
    },
    {
      name: 'ngJudgement',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngJudgement`).d('不合格处理'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});


export { tableDS };
