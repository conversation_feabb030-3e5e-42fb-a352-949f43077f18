/**
 * @Description: 物料批管理平台-列表页
 * @Author: <<EMAIL>>
 * @Date: 2022-1-25 14:38:56
 * @LastEditTime: 2023-07-31 16:37:30
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState, useCallback } from 'react';
import {
  DataSet,
  Table,
  Button,
  Modal,
  Form,
  Select,
  TextArea,
} from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import ExcelExport from 'components/ExcelExport';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Badge } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { flow, isNil } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import queryString from 'querystring';
import { openTab } from 'utils/menuTab';
import myInstance from '@utils/myAxios';
import { useRequest } from '@components/tarzan-hooks';
import { API_HOST, BASIC } from '@utils/config';
import notification from 'utils/notification';
import request from "utils/request";
import { TemplatePrintButton } from '@components/tarzan-ui';
import { tableDS, historyDS, freezeDetailDS } from './stores/ListTable';
import HistoryDrawer from './HistoryDrawer';
import { FetchDynamicColumn } from './services';

const modelPrompt = 'tarzan.hmes.product.materialLotTrance';
const tenantId = getCurrentOrganizationId();

let TableHeight = 600;

const SubjectMaintainList = (props: any) => {
  const { tableDs, customizeTable } = props;

  useEffect(() => {
    TableHeight = window.screen.height - 450;
  }, []);

  const historyDs = useMemo(() => new DataSet(historyDS()), []);

  const [ transferModalInfo, setTransferModalInfo] = useState({});

  const fetchDynamicColumn = useRequest(FetchDynamicColumn('mt_material_lot_attr'));

  const [selectedMaterialLotList, setSelectedMaterialLotList] = useState<any[]>([]);

  // 存储选中数据的key
  const [selectedKey, setSelectedKey] = useState([]);
  const [freezeYList, setFreezeYList] = useState([]);
  const [freezeNList, setFreezeNList] = useState([]);

  useEffect(() => {
    // 进入页面，进行数据查询时，有两种不同查询情况
    // 1.从新建/详情页返回到列表页
    // 2.从其他功能页面跳转到列表页
    if (Object.keys(props?.location?.query).length === 0 || props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      tableDs.query(props.tableDs.currentPage);
      return;
    }
    // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
    const {
      siteId,
      siteCode,
      materialId,
      materialCode,
      revisionCode,
      lotCode,
      qualityStatus,
      ownerType,
      ownerId,
      ownerCode,
      warehouseId,
      wareHouseCode,
      locatorCode,
      locatorId,
    } = props?.location?.query || {};
    const queryParams = {
      siteLov: siteId ? { siteId, siteCode } : undefined,
      materialLov: materialId ? { materialId, materialCode } : undefined,
      revisionCodes: revisionCode && revisionCode.length ? [revisionCode] : undefined,
      lotList: lotCode && lotCode.length ? [lotCode] : undefined,
      qualityStatus,
      ownerType,
      ownerLov: ownerId
        ? { soLineId: ownerId, customerId: ownerId, soNumContent: ownerCode }
        : undefined,
      locatorLov: locatorId
        ? [{ locatorId, locatorCode }]
        : undefined,
      wareHouseLov: warehouseId
        ? [{ locatorId: warehouseId, locatorCode: wareHouseCode }]
        : undefined,
      enableFlag: 'Y',
    };
    // tableDs.queryDataSet.loadData([queryParams]);
    tableDs.queryDataSet.loadData([{}]);
    tableDs.queryDataSet.current.set(queryParams);
    setTimeout(() => {
      tableDs.query();
      props?.history.replace({
        pathname: '/hmes/product/material-lot-traceability/list',
      })
    }, 200);
  }, [props?.location?.query, props?.location?.state]);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
      tableDs.clearCachedRecords();
    };
  }, []);

  const listener = flag => {
    // 列表交互监听
    if (tableDs) {
      const handerQuery = flag
        ? tableDs.queryDataSet.addEventListener
        : tableDs.queryDataSet.removeEventListener;
      const handler = flag ? tableDs.addEventListener : tableDs.removeEventListener;
      // const resetHandler = flag ? tableDs.addEventListener : tableDs.removeEventListener;
      // const resetHandlers = flag ? tableDs.addEventListener : tableDs.removeEventListener;

      // 查询条件更新时操作
      handerQuery.call(tableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
      handler.call(tableDs, 'load', handleDataSetSelectUpdate);
      // 头选中和撤销选中事件
      handler.call(tableDs, 'select', handleDataSetSelectUpdate);
      handler.call(tableDs, 'unSelect', handleDataSetSelectUpdate);
      handler.call(tableDs, 'selectAll', handleDataSetSelectUpdate);
      handler.call(tableDs, 'unSelectAll', handleDataSetSelectUpdate);
      // resetHandler.call(tableDs.queryDataSet, 'reset', handleReset);
      // resetHandlers.call(tableDs, 'reset', handleResets);
    }
  };

  useEffect(() => {
    handleInitTransferLovDs();
  }, []);

  // const handleReset = () => {
  //   setTimeout(() => {
  //     tableDs.queryDataSet.reset();
  //     tableDs.queryDataSet.loadData([]);
  //     tableDs.queryDataSet.current.set({});
  //   }, 100);
  // }

  const handleInitTransferLovDs = () => {
    request(`/hpfm/v1/${tenantId}/lov-view/info`, {
      method: 'GET',
      query: { viewCode: 'HWM_DEVELOP_MATERIAL_GET' },
    }).then((res) => {
      if(res && res.failed) {
        notification.warning(res.message);
      } else {
        setTransferModalInfo(res);
      }
    });
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = ({ name, record }) => {
    if (name === 'ownerType') {
      record.set('ownerLov', {});
    }
    if (name === 'reservedObjectType') {
      record.set('reservedObjectLov', {});
    }
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    const _materialLotList: string[] = [];
    tableDs.selected.forEach(item => {
      const { materialLotId } = item.toData();
      _materialLotList.push(materialLotId);
    });
    setSelectedMaterialLotList(_materialLotList);
    // 解冻冻结相关
    handleDataSetSelectUpdateFun();
  };

  // 处理选中条状态
  const handleDataSetSelectUpdateFun = () => {
    if (tableDs && tableDs.selected) {
      const selectList = tableDs.selected;
      if (selectList && selectList.length) {
        const arr = [];
        const freezeYArr = [];
        const freezeNArr = [];
        selectList.forEach(i => {
          // @ts-ignore
          arr.push(i.data.materialLotId);
          if (i.data.freezeFlag === 'Y') {
            // @ts-ignore
            freezeYArr.push(i.data.freezeFlag);
          }
          if (i.data.freezeFlag === 'N') {
            // @ts-ignore
            freezeNArr.push(i.data.freezeFlag);
          }
        });
        setSelectedKey(arr);
        setFreezeYList(freezeYArr);
        setFreezeNList(freezeNArr);
      } else {
        setSelectedKey([]);
      }
    } else {
      setSelectedKey([]);
    }
  };

  const [dynamicColumns, setDynamicColumns] = useState<ColumnProps[]>([]);

  useEffect(() => {
    const dynamicColumn: ColumnProps[] = [];
    ((fetchDynamicColumn.data || {}).content || []).forEach(item => {
      if (item.enableFlag === 'Y') {
        dynamicColumn.push({
          header: item.attrMeaning,
          name: item.attrName,
          align: ColumnAlign.left,
          width: 150,
          renderer: ({ record }) => {
            return (record?.get('attrMap') || {})[item.attrName];
          },
        });
      }
    });
    setDynamicColumns(dynamicColumn);
  }, [fetchDynamicColumn.data]);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'identification',
        align: ColumnAlign.left,
        width: 250,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                props.history.push(
                  `/hmes/product/material-lot-traceability/detail/${record?.get('materialLotId')}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'materialLotCode',
        width: 250,
      },
      {
        name: 'materialCode',
        width: 250,
      },
      {
        name: 'revisionCode',
        width: 100,
      },
      {
        name: 'materialDesc',
        width: 250,
      },
      {
        name: 'primaryUomQty',
        align: ColumnAlign.right,
        width: 100,
        renderer: ({ record }) => {
          const primaryUomQty = record?.get('primaryUomQty');
          return parseFloat(
            String(Number(primaryUomQty).toFixed(Number(record?.get('decimalNumber')))),
          );
        },
      },
      {
        name: 'primaryUomCode',
        width: 120,
      },
      {
        name: 'wareHouseCode',
        width: 250,
      },
      {
        name: 'locatorCode',
        width: 250,
      },
      {
        name: 'lot',
        width: 100,
      },
      {
        name: 'productionDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'expirationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'extendedShelfLifeTimes',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'supplierLot',
        width: 100,
      },
      {
        name: 'secondaryUomQty',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'secondaryUomCode',
        width: 120,
      },
      {
        name: 'enableFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          >
            { }
          </Badge>
        ),
      },
      {
        name: 'qualityStatusDesc',
        width: 120,
      },
      {
        name: 'createReasonDesc',
        width: 150,
      },
      {
        name: 'inLocatorTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'inSiteTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'siteCode',
        width: 250,
      },
      {
        name: 'eoNum',
        width: 150,
      },
      {
        name: 'sourceMaterialLotCode',
        width: 150,
      },
      {
        name: 'productionBatch',
        width: 150,
      },
      {
        name: 'ownerTypeDesc',
        width: 150,
        renderer: ({ value }) => value || intl.get(`tarzan.common.ownerType`).d('自有'),
      },
      {
        name: 'ownerCode',
        width: 250,
      },
      {
        name: 'ownerDesc',
        width: 250,
      },
      {
        name: 'supplierCode',
        width: 250,
      },
      {
        name: 'supplierDesc',
        width: 250,
      },
      {
        name: 'supplierSiteCode',
        width: 250,
      },
      {
        name: 'supplierSiteDesc',
        width: 250,
      },
      {
        name: 'customerCode',
        width: 250,
      },
      {
        name: 'customerDesc',
        width: 250,
      },
      {
        name: 'customerSiteCode',
        width: 250,
      },
      {
        name: 'customerSiteDesc',
        width: 250,
      },
      {
        name: 'reservedFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            { }
          </Badge>
        ),
      },
      {
        name: 'reservedObjectTypeDesc',
        width: 120,
      },
      {
        name: 'reservedObjectCode',
        width: 250,
      },
      {
        name: 'assembleToolCode',
        width: 250,
      },
      {
        name: 'assemblePointCode',
        width: 250,
      },
      {
        name: 'unloadTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'loadTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'stoveCode',
        width: 150,
      },
      {
        name: 'equipmentCode',
        width: 150,
      },
      {
        name: 'stoveCount',
        width: 100,
      },
      {
        name: 'overOrderInterceptionFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            { }
          </Badge>
        ),
      },
      {
        name: 'freezeFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            { }
          </Badge>
        ),
      },
      {
        name: 'stocktakeFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            { }
          </Badge>
        ),
      },
      {
        name: 'materialLotStatus',
        width: 150,
      },
      {
        name: 'instructionDocNum',
        width: 150,
      },
      {
        name: 'instructionNum',
        width: 250,
      },
      {
        name: 'currentContainerCode',
        width: 250,
      },
      {
        name: 'topContainerCode',
        width: 250,
      },
      {
        name: 'printTimes',
        width: 250,
        renderer: ({ value }) => value || 0,
      },
      {
        name: 'creationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'createdUsername',
        width: 250,
      },
      {
        name: 'lastUpdateDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'lastUpdatedUsername',
        width: 250,
      },
    ];
  }, []);

  const handleQueryMaterialLotsHistory = () => {
    historyDs.setQueryParameter('selectedMaterialLotList', selectedMaterialLotList);
    historyDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      closable: true,
      drawer: true,
      maskClosable: false,
      style: {
        width: 1080,
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      key: Modal.key(),
      title: (
        <div
          style={{
            width: 'calc(100% - 20px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}</div>
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/export/his/ui`}
            queryParams={{
              materialLotIds: selectedMaterialLotList,
            }}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          />
        </div>
      ),
      destroyOnClose: true,
      children: <HistoryDrawer ds={historyDs} />,
    });
  };

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return {
      ...queryParmas,
      locatorIds: (queryParmas.locatorLov || []).map(item => item.locatorId).join(),
      wareHouseIds: (queryParmas.wareHouseLov || []).map(item => item.locatorId).join(),
      ownerType: queryParmas.ownerType === 'ALL' ? '' : queryParmas.ownerType,
      identifyType: 'MATERIAL_LOT',
      size: tableDs.pageSize,
      page: tableDs.currentPage - 1,
    };
  };

  // 冻结选中的数据
  const deleteSelectData = () => {
    // @ts-ignore
    const freezeDetailDs = new DataSet(freezeDetailDS(true));
    Modal.open({
      destroyOnClose: true,
      closable: true,
      title: intl.get(`${prompt}.modal.selectReason`).d('选择原因'),
      children: (
        <Form dataSet={freezeDetailDs} columns={1} labelWidth={112}>
          <Select name="reason" />
          <TextArea name="Remark" rowSpan={2} />
        </Form>
      ),
      onOk: async () => {
        const validate = await freezeDetailDs.validate();
        if (validate) {
          const resultData = freezeDetailDs.current?.toData();
          const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-overdue-material-lot-report/freeze/ui`;
          myInstance
            .post(url, {
              materialLotIds: [...selectedKey],
              reason: resultData.reason.meaning,
              remark: resultData.Remark,
            })
            .then(res => {
              if (res.data.success) {
                notification.success({});
                setSelectedKey([]);
                setFreezeYList([]);
                setFreezeNList([]);
                // 冻结成功后重查列表
                tableDs.query();
              } else if (res.data.message) {
                notification.error({
                  description: res.data.message,
                });
              }
            });
        } else {
          return false;
        }
      },
    });
  };

  // 解冻选中的数据
  const thawSelectData = () => {
    // @ts-ignore
    const freezeDetailDs = new DataSet(freezeDetailDS());
    Modal.open({
      destroyOnClose: true,
      closable: true,
      title: intl.get(`${prompt}.modal.selectReason`).d('选择原因'),
      children: (
        <Form dataSet={freezeDetailDs} columns={1} labelWidth={112}>
          <Select name="reason" />
          <TextArea name="Remark" rowSpan={2} />
        </Form>
      ),
      onOk: async () => {
        const validate = await freezeDetailDs.validate();
        if (validate) {
          const resultData = freezeDetailDs.current?.toData();
          const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-overdue-material-lot-report/unfreeze/ui`;
          myInstance
            .post(url, {
              materialLotIds: [...selectedKey],
              reason: resultData.reason?.meaning,
              remark: resultData.Remark,
            })
            .then(res => {
              if (res.data.success) {
                notification.success({});
                setSelectedKey([]);
                setFreezeYList([]);
                setFreezeNList([]);
                // 解冻成功后重查列表
                tableDs.query();
              } else if (res.data.message) {
                notification.error({
                  description: res.data.message,
                });
              }
            });
        } else {
          return false;
        }
      },
    });
  };

  const onFieldEnterDown = () => {
    tableDs.query(props.tableDs.currentPage);
  }

  const handleImport = () => {
    openTab({
      key: `/hmes/product/material-lot-traceability/import/HME.MATERIAL_LOT_SPLIT_IMPORT`,
      title: intl.get(`${modelPrompt}.title.import`).d('拆分导入'),
      search: queryString.stringify({
        action: intl.get(`${modelPrompt}.title.import`).d('拆分导入'),
      }),
    });
  };

  const handleTransferConfirm = (params) => {
    return new Promise(async (resolve) => {
      const res = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/research/material-num`, {
        method: 'POST',
        body: {
          materialLotIds: tableDs.selected.map(e => e.get('materialLotId')),
          ...params,
        },
      });

      if(params.checkOrExecuteType === "EXECUTE") {
        if(res?.failed || !res?.success) {
          notification.warning({ description: res.message });
          return resolve(false);
        }
        notification.success();
        tableDs.query();
        return resolve(res);
      }

      if(params.checkOrExecuteType === "CHECK") {
        return resolve(res);
      }
    })
  };

  const handleResearchTransfer = useCallback(async () => {
    if(tableDs.selected.length > 0) {
      const res = await handleTransferConfirm({ checkOrExecuteType: 'CHECK' });
      if(res?.failed || !res?.success) {
        notification.warning({ description: res.message });
      } else {
        const organizationRe = /\{organizationId\}|\{tenantId\}/g;
        const dataSet = new DataSet({
          autoQuery: false,
          pageSize: transferModalInfo.pageSize,
          selection: 'single',
          queryFields: transferModalInfo.queryFields.map(e => ({
            label: e.label,
            type: 'string',
            name: e.field,
          })),
          fields: transferModalInfo.tableFields.map(e => ({
            label: e.title,
            type: 'string',
            name: e.dataIndex,
          })),
          transport: {
            read: () => ({
              url: transferModalInfo.queryUrl.replace(organizationRe, tenantId),
              method: transferModalInfo.requestMethod,
            }),
          },
        });
        const columns = transferModalInfo.tableFields.map(e => ({
          name: e.dataIndex,
          width: e.width,
        }));
        dataSet.setQueryParameter("materialLotIds", tableDs.selected.map(e => e.get('materialLotId')));
        dataSet.query();
        Modal.open({
          key: Modal.key(),
          closable: true,
          title: transferModalInfo.viewName,
          style: {
            width: 800,
          },
          children: (
            <Table
              dataSet={dataSet}
              columns={columns}
            />
          ),
          onOk: () => {
            return new Promise((resolve) => {
              if(dataSet.selected.length > 0) {
                return handleTransferConfirm({ checkOrExecuteType: 'EXECUTE', materialId: dataSet.selected.map(e => e.get('materialId'))[0]}).then((result) => {
                  if(result?.failed || !result?.success) {
                    return resolve(false);
                  }
                  return resolve(true);
                });
              }
              notification.warning({ description: intl.get(`${modelPrompt}.message.selected`).d('请至少勾选一条物料')});
              return resolve(false);
            })
          },
        });
      }
      
    };
  }, [transferModalInfo]);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.materialLotMaintenance`).d('物料批查询')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => {
            props.history.push('/hmes/product/material-lot-traceability/detail/create');
          }}
          permissionList={[
            {
              code: `/hmes/product/material-lot-traceability/list.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Button disabled={!selectedMaterialLotList.length} onClick={handleQueryMaterialLotsHistory}>
          {intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}
        </Button>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          // icon="add"
          disabled={!selectedKey.length || selectedKey.length !== freezeYList.length}
          permissionList={[
            {
              // code: `tarzan${path}.button.edit`,
              code: `/hmes/product/material-lot-traceability/list.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          onClick={thawSelectData}
        >
          {intl.get(`${modelPrompt}.button.unfreeze`).d('解冻')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          // icon="add"
          disabled={!selectedKey.length || selectedKey.length !== freezeNList.length}
          permissionList={[
            {
              // code: `tarzan${path}.button.edit`,
              code: `/hmes/product/material-lot-traceability/list.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          onClick={deleteSelectData}
        >
          {intl.get(`${modelPrompt}.button.freeze`).d('冻结')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          permissionList={[
            {
              // code: `tarzan${path}.button.edit`,
              code: `/hmes/product/material-lot-traceability/list.button.researchTransferBtn`,
              type: 'button',
              meaning: '列表页-编辑研发料转料号按钮',
            },
          ]}
          onClick={() => handleResearchTransfer()}
        >
          {intl.get(`${modelPrompt}.button.researchTransfer`).d('研发料转料号')}
        </PermissionButton>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        <TemplatePrintButton
          disabled={!tableDs.selected.length}
          printButtonCode='HME.MATERIAL_LOT_TEMP'
          printParams={{ materialLotIdList: tableDs.selected.map(item => item.get('materialLotId')).join(',') }}
        />
        <Button icon="vertical_align_top" onClick={handleImport}>
          {intl.get(`${modelPrompt}.import`).d('拆分导入')}
        </Button>
      </Header>
      <Content>
        {customizeTable(
          {
            // @ts-ignore
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LOT_LIST.QUERY`,
            // @ts-ignore
            code: `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LOT_LIST.LIST`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
              onFieldEnterDown,
            }}
            searchCode="wlpglpt"
            customizedCode="wlpglpt"
            dataSet={tableDs}
            columns={columns.concat(dynamicColumns)}
            style={{ height: TableHeight }}
            headerRowHeight={30}
            rowHeight={28}
            footerRowHeight={20}
            queryFieldsLimit={8}
            virtual
            virtualCell
            summaryBar={[
              ({ dataSet }) => {
                const data:any = dataSet?.toData()[0]
                return { label: '汇总数量', value: data?.sumQty };
              },
            ]}
            pagination={{
              showPager: true, // 显示数字按钮
              pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
            }}
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.hmes.product.materialLotTrance', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  // @ts-ignore
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LOT_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LOT_LIST.LIST`,
    ],
  }),
)(SubjectMaintainList);
