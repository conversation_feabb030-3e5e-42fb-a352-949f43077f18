/**
 * @Description: 销单库存转移-入口页
 * @Author: <EMAIL>
 * @Date: 2022/1/14 14:56
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Button, DataSet, Form, Lov, Modal, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Badge } from 'hzero-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { Content, Header } from 'components/Page';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import 'hzero-ued-icon/lib/style/icons.less';
import { drawerDS, entranceDS } from '../stories/EntranceDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inventory.soStockTransfer';

const SOStockTransferList = props => {
  const {
    match: { path },
    dataSet,
  } = props;
  const drawerDs: DataSet = useMemo(() => new DataSet(drawerDS()), []);
  const [materialLotLists, setMaterialLotLists] = useState<string[]>([]); // 用于储存选中的数据
  const { run: handleLocatorTransfer } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-so-inventory-transfer/save/for/ui `,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: handleLocatorRelease, loading: changeLocatorRelease } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-so-inventory-transfer/release/for/ui`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );
  let _drawer;

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (dataSet) {
      const handerQuery = flag
        ? dataSet.queryDataSet.addEventListener
        : dataSet.queryDataSet.removeEventListener;
      const handler = flag ? dataSet.addEventListener : dataSet.removeEventListener;
      // 查询条件更新时操作
      handerQuery.call(dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
      handler.call(dataSet, 'load', handleDataSetSelectUpdate);
      // 头选中和撤销选中事件
      handler.call(dataSet, 'select', handleDataSetSelectUpdate);
      handler.call(dataSet, 'unSelect', handleDataSetSelectUpdate);
      handler.call(dataSet, 'selectAll', handleDataSetSelectUpdate);
      handler.call(dataSet, 'unSelectAll', handleDataSetSelectUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = ({ record }) => {
    if (!record.data.soNumberObject) {
      record.set('soLineNum', '');
    }
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    const _materialLotLists: string[] = [];
    dataSet.selected.forEach(item => {
      const { materialLotId, soNumber, soLineNum, primaryUomQty, supplierCode } = item?.data;
      const _materialLotList: any = {};
      _materialLotList.materialLotId = materialLotId;
      _materialLotList.sourceSoNumber = soNumber;
      _materialLotList.sourceSoLineNumber = soLineNum;
      _materialLotList.primaryUomQty = primaryUomQty;
      _materialLotList.supplierCode = supplierCode;
      _materialLotLists.push(_materialLotList);
    });
    setMaterialLotLists(_materialLotLists);
  };

  // 抽屉中保存按钮的回调
  const handleSave = async () => {
    const newData: any = drawerDs.toData()[0];
    drawerDs!.current!.set('newDate', new Date());
    const validate = await drawerDs.validate(false, true);
    if (!validate) {
      return false;
    }
    const _materialLotLists: string[] = [];
    dataSet.selected.forEach(item => {
      const { materialLotId, soNumber, soLineNum, primaryUomQty,supplierCode } = item?.data;
      const _materialLotList: any = {};
      _materialLotList.materialLotId = materialLotId;
      _materialLotList.sourceSoNumber = soNumber;
      _materialLotList.sourceSoLineNumber = soLineNum;
      _materialLotList.primaryUomQty = primaryUomQty;
      _materialLotList.supplierCode = supplierCode;
      _materialLotLists.push(_materialLotList);
    });
    return handleLocatorTransfer({
      params: {
        ...newData,
        materialLotList: _materialLotLists,
      },
    }).then(res => {
      if (res && res.success) {
        notification.success({});
        setMaterialLotLists([]);
        _drawer.close();
        dataSet.query(dataSet.currentPage);
      } else {
        return Promise.resolve(false);
      }
    });
  };

  // 关闭按钮回调
  const handleClose = () => {
    _drawer.close();
  };

  // 点击库存转移按钮的回调事件
  const goLocatorTransfer = () => {
    _drawer = Modal.open({
      title: intl.get(`${modelPrompt}.title.stock.transfer`).d('库存转移'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 360,
      },
      afterClose: () => {
        drawerDs.reset();
      },
      className: 'hmes-style-modal',
      children: (
        <Form labelWidth={60} dataSet={drawerDs}>
          {/* <C7nFormItemSort name="targetSoNumberLov" itemWidth={['70%', '30%']}> */}
          <Lov name="supplierObj" />
          {/* </C7nFormItemSort> */}
        </Form>
      ),
      footer: (
        <div>
          <Button onClick={handleClose}>{intl.get('tarzan.common.button.cancel').d('取消')}</Button>
          <Button
            key="create"
            type={ButtonType.submit}
            color={ButtonColor.primary}
            onClick={handleSave}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      ),
    });
  };

  // 点击库存释放按钮的回调事件
  const locatorRelease = () => {
    const _materialLotLists: string[] = [];
    dataSet.selected.forEach(item => {
      const { materialLotId, soNumber, soLineNum, primaryUomQty,supplierCode } = item?.data;
      const _materialLotList: any = {};
      _materialLotList.materialLotId = materialLotId;
      _materialLotList.sourceSoNumber = soNumber;
      _materialLotList.sourceSoLineNumber = soLineNum;
      _materialLotList.primaryUomQty = primaryUomQty;
      _materialLotList.supplierCode = supplierCode;
      _materialLotLists.push(_materialLotList);
    });
    return handleLocatorRelease({
      params: materialLotLists,
    }).then(res => {
      if (res && res.success) {
        dataSet.batchUnSelect(dataSet.selected);
        notification.success({});
        dataSet.clearCachedSelected();
        setMaterialLotLists([]);
        dataSet.query(dataSet.currentPage);
      } else {
        return Promise.resolve(false);
      }
    });
  };

  const columns: ColumnProps[] = [
    { name: 'identification', lock: ColumnLock.left, width: 150 },
    { name: 'materialLotCode', width: 150 },
    { name: 'containerCode', width: 120 },
    { name: 'siteCode', width: 150 },
    { name: 'locatorCode', width: 150 },
    { name: 'materialCode', width: 150 },
    { name: 'revisionCode', width: 100 },
    { name: 'qualityStatus' },
    { name: 'primaryUomCode' },
    { name: 'primaryUomQty' },
    { name: 'secondaryUomCode' },
    { name: 'secondaryUomQty' },
    {
      name: 'inSiteTime',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'inLocatorTime',
      align: ColumnAlign.center,
      width: 150,
    },
    { name: 'lot' },
    { name: 'materialLotStatus' },
    {
      name: 'reservedFlag',
      renderer: ({ record }) => (
        <Badge
          status={record?.get('reservedFlag') === 'Y' ? 'success' : 'error'}
          text={
            record?.get('reservedFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
      align: ColumnAlign.center,
    },
    { name: 'reservedObjectType' },
    { name: 'reservedObjectCode' },
    { name: 'eoNum', width: 150 },
    { name: 'createReason' },
    {
      name: 'freezeFlag',
      renderer: ({ record }) => (
        <Badge
          status={record?.get('freezeFlag') === 'Y' ? 'success' : 'error'}
          text={
            record?.get('freezeFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
      align: ColumnAlign.center,
    },
    {
      name: 'stockTakeFlag',
      renderer: ({ record }) => (
        <Badge
          status={record?.get('stockTakeFlag') === 'Y' ? 'success' : 'error'}
          text={
            record?.get('stockTakeFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
      align: ColumnAlign.center,
    },
    // { name: 'soNumber', lock: ColumnLock.right, width: 150 },
    // { name: 'soLineNum', lock: ColumnLock.right, width: 150 },
    { name: 'supplierCode', lock: ColumnLock.right, width: 150 },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.soStockTransfer`).d('供应商库存转移')}>
        <PermissionButton
          type="c7n-pro"
          icon="yidong-o"
          color={ButtonColor.primary}
          onClick={goLocatorTransfer}
          disabled={materialLotLists.length === 0}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.stock.transfer`).d('库存转移')}
        </PermissionButton>
        <Button
          icon="lock_open"
          loading={changeLocatorRelease}
          disabled={materialLotLists.length === 0}
          onClick={locatorRelease}
        >
          {intl.get(`${modelPrompt}.button.stock.release`).d('库存释放')}
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
          }}
          dataSet={dataSet}
          columns={columns}
          highLightRow
          searchCode="SOStockTransfer"
          customizedCode="SOStockTransfer"
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.inventory.soStockTransfer', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...entranceDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(SOStockTransferList),
);
