import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import axios from 'axios';
import { BASIC } from '@utils/config';
import DashboardCard from '../DashboardCard.jsx';
import styles from '../../index.module.less';

const tenantId = getCurrentOrganizationId();
// 不合格笔数处置状态
const url = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/qms-oper-inspects/not-pass-status-pie`;

const NgQty = ({isFullScreen,timers}) => {
  const chartRef = useRef(null);
  const [data, setData] = useState<any>([]);

  useEffect(() => {
    const time = setInterval(() => {
      fetchData();
    }, (timers)*60000)
    return () => {
      clearTimeout(time)
    }
  }, [timers]);

  const fetchData = useCallback(async () => {
    const params = {};
    const res = await axios.post<any, any>(url,params);
    setData(res);
  }, []);
  
  const data1 = data?.pieDatas?.map((i)=>{
    return { name: data?.pieDatas[i]?.type, value: data?.pieDatas[0]?.dataCount||0 };
  });
  // [
  //   { name: '审批中', value: data?.pieDatas[0]?.dataCount||0 },
  //   { name: '退货', value: data?.returnQty||0 },
  //   { name: '返工', value: data?.reworkQty||0 },
  //   { name: '特采', value: data?.passQty||0 },
  // ];
  const option = useMemo(() => {
    return {
      // title:{        
      //   top:'1%',
      //   bottom: '3%',
      //   text: '不合格笔数处置状态',
      //   left: 'center',
      //   textStyle: {
      //     fontWeight: 'bold', // 加粗
      //     color: '#00fff4',
      //   },
      // },
      title: [
        {
          text: `{name|今日不良/笔}\n{val|${data?.allCount||0}}`,
          top: 'center',
          left: 'center',
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                color: '#FF8A5C',
                padding: [10, 0],
              },
              val: {
                fontSize: 32,
                fontWeight: 'bold',
                color: '#FF8A5C',
              },
            },
          },
        },
      ],
      color:['#FF847F','#fc8251','#5470c6','#9A60B4','#ef6567', '#f9c956','#3BA272'],
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0,0,0,0.9)'},
      series: {
        type: 'pie',
        radius: [50, 90],
        center: ['50%', '45%'],
        left: 'center',
        width: 400,
        itemStyle: { borderColor: '#fff', borderWidth: 1 },
        label: {
          padding: [0, -10],
        },
        labelLine: {
          length: 10,
          length2: 50,
          lineStyle: {
            type: 'dashed', // 设置虚线类型
          },
        },
        data1,
      },
    };
  }, [data]);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option]);

  return (
    <DashboardCard style={{ height: '100%' }}>
      <div className={styles['my-scroll-board-title']}>
        不合格笔数处置状态
      </div>
      {isFullScreen? 
        (<div style={{ width: '100%', height: '100%' }} className={styles['dashboard-right-chart-full-screen']}>
          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
        </div>):
        <div style={{ width: '100%', height: '100%' }} className={styles['dashboard-right-chart']}>
          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
        </div>
      }
    </DashboardCard>
  );
};
export default NgQty;
